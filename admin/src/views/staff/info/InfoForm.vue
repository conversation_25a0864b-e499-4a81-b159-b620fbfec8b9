<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="员工姓名" prop="fullName">
        <el-input v-model="formData.fullName" placeholder="请输入员工姓名" />
      </el-form-item>
      <el-form-item label="性别(M/F/U)" prop="gender">
        <el-input v-model="formData.gender" placeholder="请输入性别(M/F/U)" />
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input v-model="formData.age" placeholder="请输入年龄" />
      </el-form-item>
      <el-form-item label="电子邮箱" prop="email">
        <el-input v-model="formData.email" placeholder="请输入电子邮箱" />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="所属部门" prop="department">
        <el-input v-model="formData.department" placeholder="请输入所属部门" />
      </el-form-item>
      <el-form-item label="职位名称" prop="position">
        <el-input v-model="formData.position" placeholder="请输入职位名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InfoApi, InfoVO } from '@/api/staff/info'

/** 员工基础信息 表单 */
defineOptions({ name: 'InfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  fullName: undefined,
  gender: undefined,
  age: undefined,
  email: undefined,
  phone: undefined,
  department: undefined,
  position: undefined
})
const formRules = reactive({
  fullName: [{ required: true, message: '员工姓名不能为空', trigger: ['blur', 'change'] }],
  gender: [{ required: true, message: '性别(M/F/U)不能为空', trigger: ['blur', 'change'] }],
  age: [{ required: true, message: '年龄不能为空', trigger: ['blur', 'change'] }],
  email: [{ required: true, message: '电子邮箱不能为空', trigger: ['blur', 'change'] }],
  phone: [{ required: true, message: '联系电话不能为空', trigger: ['blur', 'change'] }],
  department: [{ required: true, message: '所属部门不能为空', trigger: ['blur', 'change'] }],
  position: [{ required: true, message: '职位名称不能为空', trigger: ['blur', 'change'] }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InfoApi.getInfo(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InfoVO
    if (formType.value === 'create') {
      await InfoApi.createInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await InfoApi.updateInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    fullName: undefined,
    gender: undefined,
    age: undefined,
    email: undefined,
    phone: undefined,
    department: undefined,
    position: undefined
  }
  formRef.value?.resetFields()
}
</script>
