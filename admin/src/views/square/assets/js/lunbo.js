class Carousel {
  constructor() {
    this.currentSlide = 0;
    this.totalSlides = 4;
    this.autoPlayInterval = null;
    this.carouselTrack = document.querySelector('.carousel-track');
    this.iconTabs = document.querySelectorAll('.icon-tab');
    this.textTabs = document.querySelectorAll('.text-tab');
    this.carouselDots = document.querySelectorAll('.carousel-dot');
    this.prevBtn = document.querySelector('.carousel-nav.prev');
    this.nextBtn = document.querySelector('.carousel-nav.next');
    this.slideContents = [
      {
        title: "全场景一体化集成",
        subtitle: "打破数据孤岛",
        contents: [
          "通过底层数据引擎打通 OA、ERP、CRM、外呼系统、销售拓展系统、AI智能分析六大模",
          "块，打破传统企业“多系统并行、数据割裂”的局面，实现跨部门、跨业务的数据协同流",
          "转，提升运营效率，推动数字化转型。"
        ]
      },
      {       
        title: "全流程数据可视化",
        subtitle: "透视管理",
        contents: [
          "支持从销售线索挖掘，客户跟进，订单执行到货款核算的全流程动态监控。管理者可以实时",
          "追踪业务健康度、销售转化率、库存周转率等关键指标，决策效率提升超过60%。助力决",
          "策者快速洞察运营瓶颈，优化资源配置，增强业务竞争力"
        ]
      },
      {   
        title: "AI深度赋能",
        subtitle: "驱动精准决策",
        contents: [
          "AI外呼+鑫线索：AI自动筛选高意向客户，外呼效率提升50%以上；",
          "分析预测：基于销售漏斗的AI建模，实时预警丢单风险并提供策略建议;",
          "自动化报表：替代人工统计，分钟级生成多维度业绩分析。        "
        ]
      },
      {
        title: "灵活部署",
        subtitle: "企业级扩展",
        contents: [
          "支持公有云、私有云、本地化部署，模块化架构可按需扩展（如新增ERP或定制风控规",
          "则），适配中小型企业到集团级客户。",
          ""
        ]
      }
    ];
    this.init();
  }

  init() {
    this.bindEvents();
    this.startAutoPlay();
  }

  bindEvents() {
    this.iconTabs.forEach((tab, index) => {
      tab.addEventListener('click', () => {
        this.goToSlide(index);
      });
    });

    this.textTabs.forEach((tab, index) => {
      tab.addEventListener('click', () => {
        this.goToSlide(index);
      });
    });

    this.carouselDots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        this.goToSlide(index);
      });
    });

    this.prevBtn.addEventListener('click', () => {
      this.previousSlide();
    });

    this.nextBtn.addEventListener('click', () => {
      this.nextSlide();
    });

    const carouselContainer = document.querySelector('.carousel-container');
    carouselContainer.addEventListener('mouseenter', () => {
      this.stopAutoPlay();
    });
    carouselContainer.addEventListener('mouseleave', () => {
      this.startAutoPlay();
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        this.previousSlide();
      } else if (e.key === 'ArrowRight') {
        this.nextSlide();
      }
    });

    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;

    this.carouselTrack.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      this.stopAutoPlay();
    });

    this.carouselTrack.addEventListener('touchend', (e) => {
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;

      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0) {
          this.previousSlide();
        } else {
          this.nextSlide();
        }
      }

      this.startAutoPlay();
    });
  }

  goToSlide(slideIndex) {
    if (slideIndex < 0 || slideIndex >= this.totalSlides) return;
    this.currentSlide = slideIndex;
    this.updateCarousel();
    this.restartAutoPlay();
  }

  nextSlide() {
    this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
    this.updateCarousel();
    this.restartAutoPlay();
  }

  previousSlide() {
    this.currentSlide = this.currentSlide === 0 ? this.totalSlides - 1 : this.currentSlide - 1;
    this.updateCarousel();
    this.restartAutoPlay();
  }

  updateCarousel() {
    const translateX = -this.currentSlide * 100;
    this.carouselTrack.style.transform = `translateX(${translateX}%)`;

    this.iconTabs.forEach((tab, index) => {
      tab.classList.toggle('active', index === this.currentSlide);
    });

    this.textTabs.forEach((tab, index) => {
      tab.classList.toggle('active', index === this.currentSlide);
    });

    this.carouselDots.forEach((dot, index) => {
      dot.classList.toggle('active', index === this.currentSlide);
    });
    const desc = this.slideContents[this.currentSlide];
    if (desc) {
      document.getElementById('carousel-title').innerText = desc.title;
      document.getElementById('carousel-subtitle').innerText = desc.subtitle;
      document.getElementById('carousel-content-1').innerText = desc.contents[0];
      document.getElementById('carousel-content-2').innerText = desc.contents[1];
      document.getElementById('carousel-content-3').innerText = desc.contents[2];
    }
  }

  startAutoPlay() {
    this.autoPlayInterval = window.setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }

  restartAutoPlay() {
    this.stopAutoPlay();
    this.startAutoPlay();
  }
}

document.addEventListener('DOMContentLoaded', () => {
  new Carousel();

  const carouselContainer = document.querySelector('.carousel-container');
  carouselContainer.style.opacity = '0';
  carouselContainer.style.transform = 'translateY(20px)';

  setTimeout(() => {
    carouselContainer.style.transition = 'all 0.6s ease';
    carouselContainer.style.opacity = '1';
    carouselContainer.style.transform = 'translateY(0)';
  }, 100);

  const iconTabs = document.querySelectorAll('.icon-tab');
  iconTabs.forEach(tab => {
    tab.addEventListener('mouseenter', () => {
      const icon = tab.querySelector('.icon');
      if (icon) {
        icon.style.transform = 'scale(1.1) rotate(5deg)';
        icon.style.transition = 'transform 0.3s ease';
      }
    });

    tab.addEventListener('mouseleave', () => {
      const icon = tab.querySelector('.icon');
      if (icon) {
        icon.style.transform = 'scale(1) rotate(0deg)';
      }
    });
  });
});
