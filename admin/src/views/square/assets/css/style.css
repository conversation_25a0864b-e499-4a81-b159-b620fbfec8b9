@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap);

h4,
h5 {
    line-height: 1.3380952381
}

h2,
h6 {
    line-height: 1.2380952381
}

body,
p {
    color: var(--paragraph-color)
}

.banner-video-area,
.bg-relative,
.check-list li,
.circle-counter .percent,
.counter-area-inner,
.our-info__input,
.position-relative,
.section-title .widget-search,
.single-service-inner-2,
.video-thumb {
    position: relative
}

.alignfull,
embed,
iframe,
img,
object {
    max-width: 100%
}

.aligncenter,
.bypostauthor,
.check-list li,
.single-checkbox input:checked~.checkmark:after,
.sticky {
    display: block
}

.comment-navigation .nav-next>a,
.comment-navigation .nav-previous>a,
.post-navigation .nav-next>a,
.post-navigation .nav-previous>a,
.posts-navigation .nav-next>a,
.posts-navigation .nav-previous>a,
.wp-link-pages a {
    transition: .3s ease-in
}

.section-title .sub-title,
body,
html {
    font-family: var(--body-font)
}

.banner-three,
.banner-two,
body,
html {
    overflow-x: hidden
}

:root {
    --main-color: #1c30e2;
    --main-color2: #ee0020;
    --heading-color: #0a0909;
    --paragraph-color: #65645f;
    --body-font: "Inter", sans-serif;
    --heading-font: "Inter", sans-serif;
    --body-font-size: 16px;
    --line-height30: 1.7;
    --white: #fff
}

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%
}

* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: 0;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased
}

body {
    margin: 0;
    line-height: var(--line-height30);
    font-size: var(--body-font-size);
    background: #fff
}

.back-to-top,
.btn,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--heading-font)
}

h1 {
    font-size: 75px;
    line-height: 1.2333333333
}

h2 {
    font-size: 40px
}

h3 {
    font-size: 30px;
    line-height: 1.3833333333
}

h4 {
    font-size: 24px
}

h5 {
    font-size: 20px
}

h6 {
    font-size: 16px
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--heading-color);
    font-weight: 700
}

p {
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    -ms-hyphens: auto;
    hyphens: auto;
    margin-bottom: 10px
}

a {
    color: inherit;
    transition: .4s
}

a,
a:active,
a:focus,
a:hover {
    text-decoration: none;
    outline: 0;
    color: inherit
}

.blog-comment .comment-content table a:hover,
.blog-comment .date,
.check-list li:after,
.color-main,
.comment-navigation .nav-next:hover>a,
.comment-navigation .nav-previous:hover>a,
.header-cat-menu li:hover>a,
.navbar-area .nav-container .navbar-collapse .navbar-nav li:hover a,
.planning-section-title-left a:hover,
.planning-section-title-right a:hover,
.post-navigation .nav-next:hover>a,
.post-navigation .nav-previous:hover>a,
.posts-navigation .nav-next:hover>a,
.posts-navigation .nav-previous:hover>a,
.section-title .ratting-inner,
.section-title .small-title span,
.section-title.style-white .sub-title,
.single-blog-inner .read-more:hover,
.tag-and-share .tags a:hover,
.widget_catagory ul li a:hover,
.widget_contact .social-media li a,
a:hover {
    color: var(--main-color)
}

pre {
    word-break: break-word
}

a i {
    padding: 0 2px
}

#content[tabindex="-1"]:focus,
.banner-slider-control .slick-dots li:focus,
.slider-control-dots .owl-dots .owl-dot:focus,
button:active,
button:focus,
button:hover {
    outline: 0
}

input[type=button]:focus,
input[type=email]:focus,
input[type=number]:focus,
input[type=password]:focus,
input[type=reset]:focus,
input[type=search]:focus,
input[type=submit]:focus,
input[type=tel]:focus,
input[type=text]:focus,
input[type=url]:focus,
select:focus,
textarea:focus {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid #ddd
}

.no-gutter.container,
.no-gutter.container-fluid,
.no-gutter.row {
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0
}

.no-gutter>[class^=col-],
.no-gutter[class^=col-] {
    padding-left: 0;
    padding-right: 0
}

code {
    color: #faa603
}

.blog-comment .comment-list,
.check-list {
    margin: 0;
    padding: 0;
    list-style: none
}

.check-list li {
    padding-left: 20px;
    z-index: 0
}

.check-list li:after {
    position: absolute;
    left: 0;
    top: 0;
    font-family: fontawesome;
    content: "\f105"
}

.clear:after,
.comment-content:after,
.entry-content:after,
.site-content:after,
.site-footer:after,
.site-header:after,
.site-main .comment-navigation,
.site-main .post-navigation,
.site-main .posts-navigation {
    clear: both
}

.comment-navigation .nav-previous,
.post-navigation .nav-previous,
.posts-navigation .nav-previous {
    float: left;
    width: 50%
}

.comment-navigation .nav-next,
.post-navigation .nav-next,
.posts-navigation .nav-next {
    float: right;
    text-align: right;
    width: 50%
}

.comment-list li,
.single-list-inner.style-check li,
.widget_catagory_2 ul li {
    list-style: none
}

.h-100vh {
    height: 100vh
}

.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    width: 1px;
    word-wrap: normal !important
}

.btn,
.read-more-text {
    position: relative;
    overflow: hidden
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, .6);
    clip: auto !important;
    clip-path: none;
    color: #21759b;
    display: block;
    font-size: .875rem;
    font-weight: 700;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000
}

.btn-base,
.btn::after {
    background: var(--main-color)
}

.alignleft {
    float: left;
    clear: both;
    margin-right: 20px
}

.alignright {
    float: right;
    clear: both;
    margin-left: 20px
}

.aligncenter {
    clear: both;
    margin: 0 auto 1.75em
}

.alignfull {
    margin: 1.5em 0
}

.alignwide {
    max-width: 1100px
}

.clear:after,
.clear:before,
.comment-content:after,
.comment-content:before,
.entry-content:after,
.entry-content:before,
.site-content:after,
.site-content:before,
.site-footer:after,
.site-footer:before,
.site-header:after,
.site-header:before {
    content: "";
    display: table;
    table-layout: fixed
}

.banner-small-inner-2 h6:after,
.banner-video-area-2 .banner-slider .slide-item:after,
.banner-video-area-3 .banner-slider .slide-item:after,
.banner-video-area-4 .banner-slider .slide-item:after,
.banner-video-area-4 .banner-slider .slide-item:before,
.blog-comment .comment-body .says,
.breadcrumb-area .page-list li:first-child:after,
.intro-area-inner:hover .read-more-text:after,
.nav-right-part-mobile,
.navbar-area-2 .nav-container .logo:after,
.td-sidebar-service .widget .widget-title:before,
.updated:not(.published),
.video-play-btn.video-play-btn-base:after {
    display: none
}

.comment-content .wp-smiley,
.entry-content .wp-smiley,
.page-content .wp-smiley {
    border: none;
    margin-bottom: 0;
    margin-top: 0;
    padding: 0
}

.custom-logo-link {
    display: inline-block
}

.wp-caption {
    margin-bottom: 1.5em;
    max-width: 100%;
    clear: both
}

.wp-caption img[class*=wp-image-] {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.wp-caption .wp-caption-text {
    margin: .8075em 0
}

.client-slider,
.wp-caption-text {
    text-align: center
}

.wp-link-pages a {
    margin: 0 5px
}

.wp-link-pages {
    margin-bottom: 30px;
    margin-top: 25px
}

.wp-link-pages a,
.wp-link-pages span {
    border: 1px solid #e2e2e2;
    padding: 5px 15px;
    display: inline-block
}

.wp-link-pages .current,
.wp-link-pages a:hover {
    background-color: var(--main-color);
    color: #fff;
    border-color: var(--main-color)
}

.navbar-top ul li a i,
.navbar-top ul li a img,
.navbar-top ul li a svg,
.navbar-top ul li p i,
.navbar-top ul li p img,
.navbar-top ul li p svg,
.widget_catagory ul li a i,
.widget_catagory ul li a svg,
.wp-link-pages span:first-child {
    margin-right: 5px
}

dl,
ol,
ul {
    padding-left: 17px
}

.post-password-form input {
    display: block;
    border: 1px solid #e2e2e2;
    height: 50px;
    border-radius: 3px;
    padding: 0 20px
}

.social-media li a,
.social-media-light a {
    height: 28px;
    width: 28px;
    text-align: center
}

.post-password-form label {
    font-weight: 600;
    color: #333
}

.post-password-form input[type=submit] {
    width: 100px;
    height: 50px;
    background-color: var(--main-color);
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
    border: none;
    cursor: pointer;
    transition: .3s ease-in
}

.post-password-form input[type=submit]:hover {
    background-color: #121a2f
}

.footer-widget .table td,
.footer-widget .table th {
    padding: .5rem !important
}

.media {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.media-body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.custom-gutters-10>.col,
.custom-gutters-10>[class*=col-] {
    padding-right: 5px;
    padding-left: 5px
}

@media all and (min-width:1200px) {

    .no-lg-gutters>.col,
    .no-lg-gutters>[class*=col-] {
        padding-right: 0;
        padding-left: 0
    }
}

@media all and (min-width:1500px) {
    .container {
        max-width: 1319px
    }
}

.btn {
    height: 55px;
    line-height: 55px;
    padding: 0 38px;
    border-radius: 4px;
    border: 0;
    -webkit-transition: .5s;
    -moz-transition: .5s;
    transition: .5s;
    font-weight: 500;
    z-index: 0
}

.btn:active,
.btn:focus {
    outline: 0;
    box-shadow: none
}

.btn::after {
    border-radius: 4px;
    content: "";
    display: block;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    transform: translate(-100%, 0) rotate(10deg);
    transform-origin: top left;
    transition: transform .2s ease-out;
    will-change: transform;
    z-index: -1
}

.btn:hover::after {
    transform: translate(0, 0)
}

.btn:hover {
    border-color: transparent;
    color: red;
    transform: scale(1.05);
    will-change: transform
}

.btn-base,
.btn-base:hover,
.btn-black,
.btn-border-base:focus,
.btn-border-base:hover,
.btn-red,
.btn-red:hover,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children:hover>.sub-menu li:hover:before,
.section-title.style-white .content,
.section-title.style-white .section-counter,
.section-title.style-white .small-title,
.section-title.style-white .title,
.social-media-light a:hover i,
.video-play-btn.video-play-btn-base i {
    color: #fff
}

.btn:hover:after {
    height: 100%;
    width: 135%
}

.btn i {
    font-size: 21px;
    float: right;
    padding-top: 16px;
    padding-left: 5px
}

.btn .fa-paper-plane {
    font-size: 16px;
    padding-left: 8px
}

.btn.border-radius,
.btn.border-radius:after {
    border-radius: 50px
}

.btn-small {
    height: 50px;
    line-height: 50px
}

.btn-small i {
    padding-top: 12px
}

.banner-video-area-4 .banner-slider-control .slick-dots li,
.banner-video-area-4 .banner-slider-control .slick-dots li.slick-active,
.banner-video-area-4 .scroll-down a:hover,
.bg-black,
.btn-base::after {
    background: var(--heading-color)
}

.btn-red {
    background: #f14d5d
}

.btn-black {
    background: var(--heading-color)
}

.btn-black:hover,
.paginations .page-numbers:active,
.paginations .page-numbers:focus,
.paginations .page-numbers:hover,
.sidebar-menu .sidebar-inner .sidebar-subscribe button:hover,
.slider-control-square .owl-nav button:hover,
.tag-and-share .blog-share ul li a:hover,
.widget_contact .social-media li a:hover {
    background: var(--main-color);
    color: #fff
}

.btn-white {
    color: var(--main-color) !important;
    background: #fff
}

.bg-base,
.btn-border-black-2:after,
.btn-border-black:after,
.btn-white:focus,
.btn-white:hover,
.mission-vision-tab .btn.active,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:hover,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover:after,
.project-info .social-media li a:hover,
.read-more-arrow,
.read-more-text:after,
.sidebar-menu .sidebar-inner .social-media li a:hover,
.single-list-media.style-2:hover .media-left,
.single-list-media.style-3:hover .media-left,
.single-list-media:hover .media-left,
.single-pricing-inner:hover .btn,
.single-service-inner .details h4:hover:after,
.social-media-light a:hover,
.team-details-page .social-media li a:hover,
.video-play-btn.video-play-btn-base {
    background: var(--main-color)
}

.btn-white:focus,
.btn-white:hover {
    color: #fff !important
}

.btn-border-base {
    color: var(--main-color);
    border: 2px solid var(--main-color);
    line-height: 53px
}

.btn-border-white {
    color: #fff;
    border: 2px solid rgba(255, 255, 255, .7);
    line-height: 53px
}

.btn-border-black-2:focus,
.btn-border-black-2:hover,
.btn-border-black:focus,
.btn-border-black:hover,
.btn-border-white:focus,
.btn-border-white:hover {
    color: #fff;
    background: var(--main-color);
    border: 2px solid var(--main-color)
}

.btn-border-black {
    color: #a2a2a2;
    border: 2px solid #d4d4d4;
    line-height: 53px
}

.btn-border-black-2 {
    color: #262626;
    border: 2px solid #262626;
    line-height: 53px
}

.read-more-text {
    display: inline-block
}

.read-more-text i {
    font-size: 18px;
    position: absolute;
    top: 1px;
    margin-left: 3px;
    transition: .4s
}

.read-more-text svg {
    font-size: 18px;
    position: absolute;
    top: 1px;
    margin-left: 6px;
    transition: .4s
}

.client-slider .thumb img,
.read-more-text img,
.single-list-media.style-3 .media-left img,
.single-service-inner-2 .details h2,
.single-service-inner-2 .details p,
.single-team-inner .thumb img {
    transition: .4s
}

.read-more-text .hide-text {
    margin-right: 10px;
    transition: .4s;
    margin-left: -122px
}

.read-more-text:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    height: 1px;
    width: 0;
    transition: .4s;
    visibility: hidden;
    opacity: 0
}

.read-more-text:hover i {
    padding-left: 6px
}

.read-more-text:hover:after {
    width: 100%;
    visibility: visible;
    opacity: 1
}

.intro-area-inner:hover .read-more-text .hide-text,
.read-more-text:hover .hide-text,
.slider-control-round .owl-nav .owl-next,
.slider-control-round .owl-nav .owl-prev,
.slider-control-square .owl-nav .owl-next,
.slider-control-square .owl-nav .owl-prev,
.social-media li:first-child {
    margin-left: 0
}

.read-more-arrow {
    height: 35px;
    width: 35px;
    line-height: 35px;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    display: inline-block
}

.read-more-arrow:hover {
    background: #0a1851;
    color: #fff
}

.back-to-top {
    display: inline-block;
    transform: rotate(-90deg);
    position: absolute;
    right: 0;
    bottom: 88px;
    color: var(--main-color);
    cursor: pointer;
    font-weight: 700;
    z-index: 3
}

.video-play-btn,
.video-play-btn:hover {
    color: var(--white)
}

.back-to-top:after {
    content: "";
    position: absolute;
    left: -39px;
    top: 13px;
    background: var(--main-color);
    height: 2px;
    width: 25px
}

@-webkit-keyframes backto-top-bounce {

    0%,
    100% {
        -webkit-transform: translateY(-4px);
        transform: translateY(-5px)
    }

    50% {
        -webkit-transform: translateY(8px);
        transform: translateY(10px)
    }
}

@keyframes backto-top-bounce {

    0%,
    100% {
        -webkit-transform: translateY(-4px);
        transform: translateY(-5px)
    }

    50% {
        -webkit-transform: translateY(8px);
        transform: translateY(10px)
    }
}

.pre-wrap {
    position: fixed;
    content: "";
    transform: translate(-100%, -240%);
    font-size: 62px
}

.preloader-inner {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999999999999;
    background-color: #030724;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center
}

.preloader-inner .cancel-preloader {
    position: absolute;
    bottom: 30px;
    right: 30px
}

.preloader-inner .cancel-preloader a {
    background-color: #fff;
    font-weight: 600;
    text-transform: capitalize;
    color: var(--main-color);
    width: 200px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border-radius: 30px;
    display: block;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    transition: .4s
}

.preloader-inner .cancel-preloader a:hover {
    background-color: var(--heading-color);
    color: #fff
}

.spinner {
    margin: 120px auto;
    width: 60px;
    height: 60px;
    position: relative;
    text-align: center;
    -webkit-animation: 2s linear infinite sk-rotate;
    animation: 2s linear infinite sk-rotate
}

.dot1,
.dot2 {
    width: 60%;
    height: 60%;
    display: inline-block;
    position: absolute;
    top: 0;
    background-color: var(--main-color);
    border-radius: 100%;
    -webkit-animation: 2s ease-in-out infinite sk-bounce;
    animation: 2s ease-in-out infinite sk-bounce
}

.dot2 {
    top: auto;
    bottom: 0;
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

@-webkit-keyframes sk-rotate {
    100% {
        -webkit-transform: rotate(360deg)
    }
}

@keyframes sk-rotate {
    100% {
        transform: rotate(360deg);
        -webkit-transform: rotate(360deg)
    }
}

@-webkit-keyframes sk-bounce {

    0%,
    100% {
        -webkit-transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1)
    }
}

@keyframes sk-bounce {

    0%,
    100% {
        transform: scale(0);
        -webkit-transform: scale(0)
    }

    50% {
        transform: scale(1);
        -webkit-transform: scale(1)
    }
}

.circle-counter .number,
.video-thumb .video-play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.video-play-btn {
    border-radius: 50%;
    background: var(--main-color);
    width: 80px;
    height: 80px;
    display: inline-block;
    line-height: 80px;
    position: relative;
    text-align: center;
    animation: 2.5s linear infinite ripple-white3;
    z-index: 3
}

.video-play-btn:after {
    z-index: -1;
    content: "";
    position: absolute;
    width: 110px;
    height: 110px;
    border-radius: 50%;
    border: 1px solid var(--main-color);
    top: -15px;
    left: -15px
}

.video-play-btn i {
    color: var(--main-color);
    margin-left: 6px;
    font-size: 28px
}

.video-play-btn.small {
    width: 55px;
    height: 55px;
    line-height: 60px
}

.video-play-btn.small i,
.widget-video-inner .thumb .video-btn i {
    font-size: 18px
}

.breadcrumb-area {
    padding: 205px 0 115px;
    position: relative;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover
}

.breadcrumb-area .banner-bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-size: 102% 96%
}

.about-thumb-area .about-img-1,
.bg-overlay .bg-z-index,
.bg-overlay .container,
.bg-overlay-light .bg-z-index,
.bg-overlay-light .container,
.blog-page-btn img,
.breadcrumb-area .breadcrumb-inner,
.contact-inner .contact-from-inner .section-title,
.contact-inner .contact-from-inner form,
.single-service-inner-2 .details .details-inner {
    position: relative;
    z-index: 2
}

.breadcrumb-area .page-title {
    font-size: 75px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #fff
}

.breadcrumb-area .page-list,
.sidebar-menu .sidebar-inner .sidebar-address ul,
.single-list-inner {
    margin: 0;
    padding: 0
}

.breadcrumb-area .page-list li {
    font-size: 18px;
    font-weight: 500;
    list-style: none;
    display: inline-block;
    position: relative;
    padding-left: 8px;
    color: #fff
}

.breadcrumb-area .page-list li:after {
    position: absolute;
    left: 0;
    top: 1px;
    content: "/";
    font-family: fontawesome
}

.blog-comment .pingback .comment-body,
.blog-comment .trackback .comment-body,
.breadcrumb-area .page-list li:first-child {
    padding-left: 0
}

.social-media {
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 2
}

.social-media li {
    display: inline-block;
    margin: 0 2px
}

.social-media li a {
    line-height: 28px;
    border-radius: 50%;
    display: inline-block;
    background: #fff;
    color: var(--main-color);
    font-size: 12px
}

.social-media li a:hover {
    background: #fff;
    color: var(--main-color)
}

.social-media li a.facebook {
    color: #3b5999;
    border-color: #3b5999
}

.social-media li a.twitter {
    color: #55acee;
    border-color: #55acee
}

.social-media li a.instagram {
    color: #e4405f;
    border-color: #e4405f
}

.social-media li a.youtube {
    color: #cd201f;
    border-color: #cd201f
}

.navbar-top .topbar-right li a i,
.navbar-top .topbar-right li a svg,
.navbar-top .topbar-right li a:last-child,
.social-media li:last-child,
.tag-and-share .blog-share ul li:last-child {
    margin-right: 0
}

.social-media-light a {
    background: rgba(253, 74, 54, .15);
    display: inline-block;
    border-radius: 50%;
    line-height: 31px
}

.social-media-light a i {
    color: var(--main-color);
    font-size: 15px;
    transition: .4s
}

.slider-control-round .owl-nav button {
    height: 46px;
    width: 46px;
    line-height: 44px;
    margin: 0 10px;
    border-radius: 50%;
    border: 0;
    transition: .4s;
    box-shadow: none;
    color: var(--main-color);
    border: 1px solid var(--main-color);
    padding: 0;
    cursor: pointer;
    font-size: 24px;
    background: 0 0
}

.slider-control-round .owl-nav button:hover {
    background-image: linear-gradient(to right, #f99746, #ee0020);
    color: #fff
}

.slider-control-square .owl-nav button {
    height: 45px;
    width: 60px;
    line-height: 46px;
    margin: 0 8px;
    border-radius: 0;
    border: 1px solid var(--main-color);
    transition: .4s;
    box-shadow: none;
    color: var(--main-color);
    background: 0 0;
    font-size: 20px;
    padding: 0;
    cursor: pointer
}

.slider-control-right-top .owl-nav {
    position: absolute;
    right: 0;
    top: -95px
}

.bg-overlay-light:after,
.bg-overlay:after {
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
    content: ""
}

.slider-control-dots .owl-dots .owl-dot {
    margin: 0;
    border: 0;
    background: 0 0
}

.slider-control-dots .owl-dots .owl-dot span {
    display: block;
    border-radius: 25px;
    background-color: #f59faa;
    width: 15px;
    height: 6px;
    position: relative;
    -webkit-transition: .3s;
    -moz-transition: .3s;
    transition: .3s
}

.slider-control-dots .owl-dots .owl-dot.active span {
    background: var(--main-color);
    width: 30px
}

.banner-slider-control .slick-dots li.slick-active:after,
.slider-control-dots .owl-dots .owl-dot.active span:after {
    transform: scale(1)
}

.single-list-inner li {
    margin-bottom: 6px
}

.blog-comment .comment-content p:last-of-type,
.circle-counter .number h4,
.progressbar-media p,
.reply-form .form-submit,
.sidebar-menu .sidebar-inner .sidebar-address ul li:last-child,
.single-case-study-inner .details h4,
.single-list-inner li:last-child,
.single-list-media .media-body p,
.single-list-media.style-3 .media-body h4,
.single-testimonial-inner .designation,
.single-testimonial-inner h4,
.widget_contact .details li:last-child,
.widget_search .search-form .form-group {
    margin-bottom: 0
}

.single-list-inner.style-check li i {
    margin-right: 10px;
    color: var(--main-color)
}

.border-radius-6,
.border-radius-6 img {
    border-radius: 6px
}

.ratting-inner {
    color: #ffc107
}

.bg-green {
    background: #1dc295
}

.bg-blue {
    background: #2878eb
}

.bg-red {
    background: #f14d5d
}

.bg-gray {
    background: #f5f9ff
}

.bg-gray-2 {
    background: #f9f9f9
}

.bg-sky {
    background: #f3f8ff
}

.bg-light {
    background: #f9f8f4
}

.bg-light2,
.single-input-inner.style-bg input,
.single-input-inner.style-bg textarea {
    background: #f5f3ef
}

.bg-light-pink {
    background: #fff9f5
}

.bg-parallax,
.bg-parallex {
    background-attachment: fixed
}

.bg-cover {
    background-size: cover !important;
    background-position: center
}

.bg-overlay,
.bg-overlay-light {
    position: relative;
    background-size: cover !important
}

.bg-overlay:after {
    position: absolute;
    background: rgba(10, 24, 81, .88)
}

.bg-overlay-light:after {
    position: absolute;
    background: rgba(10, 24, 81, .5)
}

.bg-parallex {
    background-position: center 0
}

.bg-image,
.bg-parallax {
    background-position: center;
    background-repeat: no-repeat
}

.client-area,
.image-hover-animate {
    overflow: hidden
}

.image-hover-animate img {
    transform: scale(1.02);
    transition: .7s
}

.image-hover-animate:hover img,
.single-blog-inner:hover .thumb img,
.single-team-inner:hover .thumb img,
.widget_instagram .instagram-wrap .thumb:hover img {
    transform: scale(1.1)
}

.box-shadow {
    box-shadow: 0 8px 30px rgba(0, 0, 0, .1)
}

.pd-top-60 {
    padding-top: 60px
}

.pd-top-90 {
    padding-top: 90px
}

.pd-top-100 {
    padding-top: 100px
}

.pd-top-110 {
    padding-top: 110px
}

.pd-top-115 {
    padding-top: 115px
}

.pd-top-120 {
    padding-top: 120px
}

.pd-top-200 {
    padding-top: 200px
}

.mg-top-120 {
    margin-top: 120px
}

.mg-bottom-120 {
    margin-bottom: 120px
}

.pd-bottom-60 {
    padding-bottom: 60px
}

.pd-bottom-70 {
    padding-bottom: 70px
}

.pd-bottom-80 {
    padding-bottom: 80px
}

.pd-bottom-90 {
    padding-bottom: 90px
}

.pd-bottom-100 {
    padding-bottom: 100px
}

.pd-bottom-110 {
    padding-bottom: 110px
}

.pd-bottom-115 {
    padding-bottom: 115px
}

.pd-bottom-120 {
    padding-bottom: 120px
}

.mg-top--82 {
    margin-top: -100px
}

.border-right-1 {
    border-right: 1px solid #dddbd8
}

.border-right-1-light {
    border-right: 1px solid #303030
}

.mt--30 {
    margin-top: -30px
}

.mt--50,
.work-process .work-process-thumb {
    margin-top: -50px
}

.pt-55 {
    padding-top: 55px
}

.bg-parallax {
    background-size: 100% 90%
}

.position-top-right {
    position: absolute;
    top: 100px;
    right: 120px
}

.position-bottom-right {
    position: absolute;
    bottom: 90px;
    right: 100px
}

.position-bottom-left {
    position: absolute;
    bottom: 130px;
    left: 160px
}

.position-center-right {
    position: absolute;
    top: 45%;
    right: 160px;
    transform: translate(-50%)
}

.color-main-bg {
    background-color: var(--main-color) !important
}

.bg-image {
    background-size: cover
}

::selection {
    color: var(--white);
    background-color: var(--main-color)
}

.bor-top,
.service-tab-inner.style-2 {
    border-top: 1px solid #d9d9d9
}

.bor-left {
    border-left: 1px solid #d9d9d9
}

.bor-bottom,
.service-tab-inner.style-2 li {
    border-bottom: 1px solid #d9d9d9
}

.bor-right {
    border-right: 1px solid #d9d9d9
}

::-webkit-scrollbar {
    width: 10px
}

::-webkit-scrollbar-track {
    background-color: #d9d9d9
}

::-webkit-scrollbar-thumb {
    background-color: var(--main-color);
    border-radius: 8px;
    border: 1px solid transparent;
    background-clip: content-box
}

.top_image_bounce {
    animation: 3s ease-in-out infinite top-image-bounce
}

.left_image_bounce {
    animation: 3s ease-in-out infinite left-image-bounce
}

.right_image_bounce {
    animation: 3s ease-in-out infinite right-image-bounce
}

.spin_image {
    animation: 3s ease-in-out infinite spin
}

.rotation_image {
    animation: 90s ease-in-out infinite rotation
}

@-webkit-keyframes top-image-bounce {

    0%,
    100% {
        -webkit-transform: translateY(-8px);
        transform: translateY(-8px)
    }

    50% {
        -webkit-transform: translateY(12px);
        transform: translateY(12px)
    }
}

@keyframes top-image-bounce {

    0%,
    100% {
        -webkit-transform: translateY(-8px);
        transform: translateY(-8px)
    }

    50% {
        -webkit-transform: translateY(12px);
        transform: translateY(12px)
    }
}

@-webkit-keyframes left-image-bounce {

    0%,
    100% {
        -webkit-transform: translateX(-5px);
        transform: translateX(-5px)
    }

    50% {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
}

@keyframes left-image-bounce {

    0%,
    100% {
        -webkit-transform: translateX(-5px);
        transform: translateX(-5px)
    }

    50% {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
}

@-webkit-keyframes spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
        -webkit-transform-origin: 50%;
        transform-origin: 50%
    }
}

@keyframes spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
        -webkit-transform-origin: 50%;
        transform-origin: 50%
    }
}

@keyframes rotation {
    from {
        transform: rotate(0)
    }

    to {
        transform: rotate(700deg)
    }
}

@keyframes ripple-white3 {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, .1), 0 0 0 10px rgba(255, 255, 255, .1), 0 0 0 20px rgba(255, 255, 255, .1)
    }

    100% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, .1), 0 0 0 20px rgba(255, 255, 255, .1), 0 0 0 100px rgba(255, 255, 255, 0)
    }
}

.section-title {
    margin-bottom: 50px;
    position: relative
}

.bar1,
.bar2,
.bar3 {
    margin-bottom: 5px;
    right: -5px
}

.section-title .sub-title {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
    color: var(--main-color);
    text-transform: uppercase
}

.section-title .sub-title.left-line {
    padding-left: 60px
}

.section-title .sub-title.left-line:before {
    content: "";
    position: absolute;
    left: 0;
    top: 12px;
    height: 2px;
    width: 40px;
    background: var(--main-color)
}

.section-title .sub-title.double-line:after,
.section-title .sub-title.right-line:after {
    content: "";
    position: absolute;
    right: -50px;
    top: 12px;
    height: 2px;
    width: 40px;
    background: var(--main-color)
}

.section-title .sub-title.double-line:before {
    content: "";
    position: absolute;
    left: -50px;
    top: 12px;
    height: 2px;
    width: 40px;
    background: var(--main-color)
}

.section-title .title {
    margin-bottom: 0;
    margin-top: 0;
    text-transform: capitalize
}

.section-title .content {
    margin-top: 17px;
    margin-bottom: 0
}

.section-title .quote {
    border-left: 3px solid var(--main-color);
    padding-left: 15px;
    font-weight: 500
}

.section-title .section-counter span {
    font-size: 48px;
    margin-right: 5px
}

.attax-accordion .accordion-item .accordion-button.collapsed,
.banner-video-area-2 .banner-content-area:hover h2,
.banner-video-area-2 .banner-content-area:hover p,
.navbar-top ul li a:hover,
.section-title strong,
.service-tab-inner.style-2 button,
.single-pricing-inner p,
.widget_search .search-form button i,
.work-process-thumb:hover .quote-wrap h4 {
    color: var(--heading-color)
}

.section-title .small-title {
    margin-top: 18px;
    line-height: 36px
}

.section-title .phone {
    font-weight: 400;
    margin-top: 30px
}

.section-title .phone img {
    margin-right: 6px
}

.section-title .single-list-wrap {
    margin-top: 21px
}

.section-title .btn {
    margin-top: 30px
}

.section-title h4 svg {
    color: var(--main-color);
    margin-right: 10px
}

.section-title .widget-search input {
    border: 0;
    border-bottom: 1px solid var(--heading-color);
    width: 100%;
    max-width: 400px;
    background: 0 0;
    height: 40px
}

.section-title .widget-search a {
    margin-left: -25px
}

.banner-slider-control .slick-dots li.slick-active,
.section-title.style-white .sub-title.double-line:after,
.section-title.style-white .sub-title.double-line:before,
.section-title.style-white .sub-title.left-line:before,
.section-title.style-white .sub-title.right-line:after {
    background: #fff
}

.planning-section-title-left {
    white-space: nowrap;
    display: inline-block;
    -webkit-animation: 110s linear infinite marquee;
    animation: 110s linear infinite marquee
}

.planning-section-title-left a,
.planning-section-title-right a {
    font-size: 80px;
    color: var(--heading-color);
    line-height: initial;
    font-weight: 700;
    margin-right: 30px;
    text-transform: capitalize;
    font-family: var(--heading-font)
}

.banner-small-inner h4,
.banner-small-inner h6 {
    font-family: var(--body-font);
    margin-bottom: 20px
}

.planning-section-title-right {
    white-space: nowrap;
    display: inline-block;
    -webkit-animation: 130s linear infinite marquee-2;
    animation: 130s linear infinite marquee-2
}

@-webkit-keyframes marquee {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    100% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
}

@keyframes marquee {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    100% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
}

@-webkit-keyframes marquee-2 {
    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    0% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
}

@keyframes marquee-2 {
    100% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }

    0% {
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
}

.navbar-top {
    background: #091d3e;
    padding: 15px 0
}

.bar1,
.bar2,
.bar3,
.sticky-active {
    background: #fff
}

.navbar-top ul {
    margin: 0;
    padding: 0;
    line-height: initial
}

.navbar-top ul li {
    display: inline-block;
    list-style: none;
    margin-right: 25px
}

.navbar-top ul li:last-child {
    margin-right: 0;
    padding-right: 0;
    border-right: 0
}

.navbar-top ul li a,
.navbar-top ul li p {
    margin: 0;
    font-size: 14px;
    color: #fff
}

.navbar-top .topbar-right a {
    margin: 0 3px
}

.navbar-top .topbar-right li:last-child,
.single-team-inner .details .social a:last-child {
    border-right: 0
}

.navbar-area {
    z-index: 99;
    padding: 0;
    border-bottom: 1px solid #e3e1dd
}

.bar1,
.bar2,
.bar3,
.sticky-active {
    z-index: 9999
}

.header-cat-menu li .sub-menu li:last-child,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:last-child,
.sticky-active,
.widget-video-inner .details ul li:last-child {
    border-bottom: 0
}

.navbar-area .nav-container {
    transition: .4s;
    position: relative
}

.navbar-area .nav-container.navbar-bg {
    position: relative;
    padding-left: 30px
}

.navbar-area .nav-container .logo {
    line-height: .9;
    position: relative;
    padding-right: 35px
}

.navbar-area .nav-container .logo:after {
    content: "";
    position: absolute;
    right: 0;
    bottom: -21px;
    height: 81px;
    width: 1px;
    background: #e3e1dd
}

.navbar-area .nav-container .logo a {
    font-weight: 700;
    font-size: 24px;
    color: #fff
}

.navbar-area .nav-container .logo a img {
    height: auto;
    max-width: 140px
}

.navbar-area .nav-container .btn-transparent {
    font-size: 13px;
    font-weight: 700
}

.navbar-area .nav-container .navbar-collapse .navbar-nav {
    display: block;
    width: 100%;
    text-align: left
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li {
    display: inline-block;
    font-weight: 500;
    line-height: 80px;
    text-transform: capitalize
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li a {
    color: var(--heading-color);
    font-weight: 600;
    font-size: 18px;
    font-family: var(--heading-font)
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li+li {
    margin-left: 17px
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children {
    position: relative;
    z-index: 0;
    padding-right: 14px
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:before {
    content: "";
    position: absolute;
    right: 3px;
    top: 50%;
    height: 10px;
    width: 2px;
    background: var(--heading-color);
    transform: translateY(-50%);
    transition: .3s ease-in;
    margin-top: 0;
    z-index: -1
}

.bar1,
.bar2,
.bar3 {
    height: 2px;
    position: absolute
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:after {
    content: "";
    position: absolute;
    right: -1px;
    top: 41px;
    height: 2px;
    width: 10px;
    background: var(--heading-color);
    transform: translateY(-50%);
    transition: .3s ease-in;
    z-index: -1
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover,
.paginations .next,
.paginations .prev {
    -webkit-transition: .4s;
    -moz-transition: .4s;
    transition: .4s
}

.mfp-zoom-in.mfp-removing.mfp-bg,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover:before {
    opacity: 0
}

.header-cat-menu li .sub-menu li:hover a:before,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children:hover>.sub-menu,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:hover a:before,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:hover>.sub-menu,
.widget_instagram .instagram-wrap .thumb:hover i,
.widget_instagram .instagram-wrap .thumb:hover:after {
    visibility: visible;
    opacity: 1
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
    position: absolute;
    text-align: left;
    min-width: 210px;
    margin: 0;
    padding: 0;
    list-style: none;
    left: 0;
    top: 100%;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);
    background-color: #fff;
    z-index: 9;
    overflow: hidden;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    transition: .4s;
    border-radius: 0
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu.border-bt0 {
    border-bottom: 0 !important
}

.header-cat-menu li .sub-menu li,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li {
    display: block;
    margin-left: 0;
    line-height: 22px;
    font-size: 15px;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    transition: .4s;
    border-bottom: 1px solid #f5f5f5
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a {
    display: block;
    padding: 10px 20px;
    white-space: nowrap;
    transition: .4s;
    color: var(--heading-color);
    font-size: 15px;
    font-weight: 500;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    position: relative
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a:before {
    position: absolute;
    left: 17px;
    top: 50%;
    content: "\f054";
    font-family: "Font Awesome 5 Free";
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    visibility: hidden;
    opacity: 0;
    transition: .4s;
    color: #fff
}

.header-cat-menu li .sub-menu li:hover a,
.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:hover a {
    color: #fff;
    padding: 10px 20px 10px 30px
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children {
    position: relative;
    z-index: 0;
    padding-right: 0
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children:before {
    position: absolute;
    right: 15px;
    top: 50%;
    content: "\f105";
    font-family: fontawesome;
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children>.sub-menu {
    left: 100%;
    top: 20px
}

.navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .menu-item-has-children>.sub-menu .sub-menu .sub-menu {
    left: auto;
    right: 100%
}

.navbar-area .nav-container .navbar-collapse .navbar-nav>li {
    margin-right: 12px
}

.navbar-area-2 .nav-container {
    max-width: 100% !important;
    padding-left: 80px !important;
    padding-right: 0
}

.navbar-area-2 .nav-container .nav-right-part .menubar {
    margin-right: 40px
}

.navbar-area-2 .nav-container .nav-right-part .btn {
    border-radius: 0;
    height: 85px;
    line-height: 85px
}

.navbar-area-2 .nav-container .nav-right-part-desktop {
    margin-left: 55px
}

.navbar-nav {
    opacity: 0;
    margin-right: -30px;
    visibility: hidden;
    transition: .4s
}

.sticky-active {
    -webkit-animation: .3s ease-in-out fadeInDown;
    animation: .3s ease-in-out fadeInDown;
    left: 0;
    top: 0;
    width: 100%;
    -webkit-box-shadow: 0 10px 20px 0 rgba(46, 56, 220, .05);
    box-shadow: 0 10px 20px 0 rgba(46, 56, 220, .05)
}

.menu-open {
    opacity: 1;
    margin-right: 0;
    visibility: visible
}

.bar1 {
    width: 32px;
    top: 10px
}

.bar2 {
    width: 24px;
    top: 17px
}

.bar3 {
    width: 18px;
    top: 24px
}

.responsive-mobile-menu button:focus {
    outline: 0;
    border: none
}

.header-cat-menu {
    margin: 0 0 0 58px;
    padding: 0
}

.header-cat-menu li {
    position: relative;
    margin-right: 0;
    cursor: pointer;
    list-style: none
}

.header-cat-menu li img {
    margin-top: -3px;
    margin-right: 5px
}

.header-cat-menu li a {
    color: #022334;
    font-weight: 500;
    font-size: 18px
}

.header-cat-menu li .sub-menu {
    position: absolute;
    left: 0;
    top: 40px;
    list-style: none;
    padding: 0;
    margin: 0;
    z-index: 9;
    border: none;
    opacity: 0;
    visibility: hidden;
    transition: .4s;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);
    background-color: #fff;
    min-width: 210px
}

.header-cat-menu li .sub-menu li a {
    display: block;
    padding: 10px 20px;
    white-space: nowrap;
    transition: .4s;
    color: #050a30;
    font-size: 14px;
    font-weight: 500;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    position: relative
}

.header-cat-menu li .sub-menu li a:before {
    position: absolute;
    left: 17px;
    top: 50%;
    content: "\f105";
    font-family: fontawesome;
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    visibility: hidden;
    opacity: 0;
    transition: .4s;
    color: #fff
}

.header-cat-menu li .sub-menu li:hover {
    background: #061539
}

.header-cat-menu li:hover ul {
    opacity: 1;
    visibility: visible;
    animation: .3s menuslide
}

.nav-right-part .btn {
    height: 50px;
    line-height: 50px;
    color: #fff;
    border-radius: 50px
}

.nav-right-part .btn i {
    font-size: 14px;
    padding-top: 14px;
    padding-left: 6px
}

.nav-right-part a {
    margin-left: 15px;
    color: var(--heading-color)
}

.nav-right-part .search-bar-btn {
    position: relative;
    padding-right: 30px;
    margin-right: 18px
}

.nav-right-part .search-bar-btn:after {
    content: "";
    position: absolute;
    right: 0;
    bottom: -32px;
    height: 81px;
    width: 1px;
    background: #e3e1dd
}

.nav-right-part .search-bar:hover {
    background: var(--main-color);
    border: 1px solid var(--main-color)
}

.nav-right-part-desktop {
    margin-left: 20px
}

.sidebar-menu {
    width: 0;
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    background-color: #fff;
    z-index: 999;
    overflow-y: auto;
    transition: .5s;
    opacity: 0;
    visibility: hidden
}

.sidebar-menu .sidebar-inner {
    position: relative;
    top: 150px;
    opacity: 0;
    visibility: hidden;
    transition: .3s;
    padding: 90px 40px 50px
}

.sidebar-menu .sidebar-inner .thumb,
.single-testimonial-inner .thumb {
    margin-bottom: 15px
}

.sidebar-menu .sidebar-inner p,
.single-service-inner .details p {
    margin-bottom: 28px
}

.sidebar-menu .sidebar-inner .sidebar-address,
.single-service-inner .thumb {
    margin-bottom: 35px
}

.sidebar-menu .sidebar-inner .sidebar-address ul li {
    list-style: none;
    margin-bottom: 4px
}

.sidebar-menu .sidebar-inner .sidebar-address ul li svg {
    margin-right: 6px;
    width: 25px;
    color: var(--main-color)
}

.sidebar-menu .sidebar-inner .sidebar-subscribe {
    position: relative;
    margin-bottom: 40px
}

.sidebar-menu .sidebar-inner .sidebar-subscribe input {
    width: 100%;
    border: 1px solid #d6dadf;
    height: 42px;
    padding: 0 45px 0 12px
}

.sidebar-menu .sidebar-inner .sidebar-subscribe button {
    position: absolute;
    right: 0;
    top: 0;
    height: 42px;
    width: 42px;
    background: var(--heading-color);
    transition: .4s;
    border: 0;
    color: #fff;
    cursor: pointer
}

.sidebar-menu .sidebar-inner .social-media li a {
    background: var(--heading-color);
    color: #fff
}

.sidebar-menu .sidebar-menu-close {
    background-color: var(--main-color);
    border: 0;
    position: absolute;
    top: 0;
    right: 0;
    color: #fff;
    width: 50px;
    height: 50px;
    line-height: 50px;
    cursor: pointer;
    z-index: 999
}

.sidebar-menu.active {
    width: 360px;
    opacity: 1;
    visibility: visible
}

.toggle-btn .icon-left,
.toggle-btn .icon-left:after,
.toggle-btn .icon-right,
.toggle-btn .icon-right:after {
    height: 2px;
    width: 11px;
    background-color: var(--heading-color)
}

.sidebar-menu.active .sidebar-inner {
    top: 0;
    opacity: 1;
    visibility: visible;
    transition: .7s ease-out .8s
}

.toggle-btn,
.toggle-btn .icon-left,
.toggle-btn .icon-left:after,
.toggle-btn .icon-right,
.toggle-btn .icon-right:after {
    transition-duration: .5s;
    position: absolute
}

@media only screen and (max-width:991px) {
    .navbar-area {
        background: #fff;
        padding-bottom: 0
    }

    .navbar-area .nav-container {
        padding: 5px 0;
        position: relative;
        z-index: 0
    }

    .nav-right-part {
        margin-right: 60px
    }

    .nav-right-part .btn .right {
        padding-left: 5px;
        font-size: 13px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav>li {
        margin-right: 0;
        padding-right: 0
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li,
    .navbar-area .nav-container .navbar-toggler {
        padding: 0
    }

    .navbar-area .nav-container .navbar-collapse {
        margin-top: 13px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav {
        display: block;
        margin-top: 20px;
        margin-bottom: 20px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li {
        display: block;
        text-align: left;
        line-height: 30px;
        padding: 10px 0;
        border-bottom: 1px solid rgba(0, 0, 0, .1)
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li a {
        display: block;
        color: #333
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li:last-child {
        border-bottom: none
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li+li {
        margin-left: 0
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children:before {
        top: 26px;
        right: 3px !important
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu {
        position: initial;
        width: 100%;
        border-top: none;
        box-shadow: none;
        margin-left: 0;
        height: auto;
        overflow: hidden;
        max-height: 250px;
        overflow-y: scroll;
        background-color: transparent;
        border-radius: 10px;
        border-bottom: none;
        display: none;
        transition: none;
        visibility: visible;
        opacity: 1;
        padding: 0 0 0 20px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu .sub-menu .menu-item-has-children:before {
        content: "\f107"
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li.menu-item-has-children:hover:before {
        top: 30px;
        color: #fff
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li+li {
        border-top: none
    }

    .navbar-area .logo {
        padding-top: 0 !important
    }

    .widget ul {
        text-align: left
    }

    .navbar-collapse {
        background: #fff;
        margin-top: 0;
        width: 100%
    }

    .bar1,
    .bar2,
    .bar3 {
        background: #fff
    }

    .party-box-wrapper {
        padding: 50px 0
    }

    .party-box-wrapper .party-box-content h4 {
        font-size: 30px;
        line-height: 45px
    }

    .footer-area .copyright-area-inner {
        padding: 20px
    }

    .navbar-expand-lg .navbar-collapse {
        margin-top: 0
    }

    .contact-widget .contact_info_list li.single-info-item .details {
        padding-left: 25px
    }

    .footer-area .footer-top .widget.widget_nav_menu ul li a {
        font-size: 14px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a {
        padding: 12px 0
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:last-child a {
        padding-bottom: 3px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li a:hover i {
        opacity: 0;
        margin-right: -18px
    }

    .nav-right-part-mobile {
        display: block
    }

    .nav-right-part-desktop {
        display: none
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li.menu-item-has-children .sub-menu li:hover {
        background: 0 0
    }
}

.sopen {
    display: block !important
}

.toggle-btn {
    left: auto;
    right: 0;
    top: 8px;
    width: 40px;
    height: 40px;
    border: 0;
    background: 0 0
}

.toggle-btn .icon-left {
    top: 18px;
    left: 7px
}

.toggle-btn .icon-left:before,
.toggle-btn .icon-right:before {
    transition-duration: .5s;
    position: absolute;
    width: 11px;
    height: 2px;
    background-color: var(--heading-color);
    content: "";
    top: -7px;
    left: 0
}

.toggle-btn .icon-left:after,
.toggle-btn .icon-right:after {
    content: "";
    top: 7px;
    left: 0
}

.toggle-btn .icon-left:hover,
.toggle-btn .icon-right:hover,
.toggle-btn:hover {
    cursor: pointer
}

.toggle-btn .icon-right {
    top: 18px;
    left: 18px
}

.toggle-btn.open .icon-left,
.toggle-btn.open .icon-right {
    transition-duration: .5s;
    background: 0 0
}

.toggle-btn.open .icon-left:before {
    transform: rotateZ(45deg) scaleX(1.4) translate(2px, 1px)
}

.toggle-btn.open .icon-left:after {
    transform: rotateZ(-45deg) scaleX(1.4) translate(2px, -1px)
}

.toggle-btn.open .icon-right:before {
    transform: rotateZ(-45deg) scaleX(1.4) translate(-2px, 1px)
}

.toggle-btn.open .icon-right:after {
    transform: rotateZ(45deg) scaleX(1.4) translate(-2px, -1px)
}

@media only screen and (min-width:992px) and (max-width:1199px) {
    .navbar-area .nav-container .navbar-collapse .navbar-nav li {
        font-size: 16px
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li+li {
        margin-left: 5px
    }
}

@media only screen and (min-width:768px) and (max-width:991px) {
    .responsive-mobile-menu {
        display: block;
        width: 100%;
        position: relative
    }

    .navbar-area .nav-container .navbar-collapse .navbar-nav li {
        font-size: 14px
    }
}

@media only screen and (max-width:1199px) {
    .margin-xlt-80 {
        margin-top: 0
    }

    .contact-widget .contact_info_list li.single-info-item .details {
        padding-left: 25px
    }
}

@media only screen and (max-width:767px) {

    .logo-wrapper.mobile-logo,
    .responsive-mobile-menu {
        display: block;
        width: 100%
    }

    .responsive-mobile-menu {
        position: relative
    }

    .responsive-mobile-menu .navbar-toggler {
        position: absolute;
        left: calc(100% - 130px);
        top: 10px
    }

    .table-responsive {
        display: block !important
    }

    .btn-custom-default,
    .btn-custom-white {
        font-size: 14px;
        line-height: 33px;
        padding: 6px 20px
    }

    .navbar-area .logo {
        padding-top: 0 !important
    }
}

.body-overlay,
.td-search-popup .search-form {
    position: fixed;
    z-index: 999;
    opacity: 0;
    visibility: hidden
}

@media only screen and (max-width:575px) {
    .navbar-area .nav-container {
        margin: 0
    }

    .navbar-area .logo {
        padding-top: 10px
    }

    .widget.footer-widget .subscribe-form.subscribe-form-style2 .btn,
    .widget.footer-widget .subscribe-form.subscribe-form-style2 .form-control {
        padding: 15px 20px
    }

    .search-popup .search-form {
        min-width: 350px
    }
}

@media only screen and (max-width:375px) {

    .btn-custom-default,
    .btn-custom-white {
        padding: 5px 18px
    }

    .search-popup .search-form .form-group .form-control,
    .search-popup .search-form .submit-btn {
        height: 45px
    }

    .search-popup .search-form {
        min-width: 300px
    }
}

@media only screen and (max-width:320px) {
    .search-popup .search-form {
        min-width: 265px
    }

    .responsive-mobile-menu .navbar-toggler {
        left: calc(100% - 95px)
    }
}

.td-search-popup.active .search-form {
    visibility: visible;
    opacity: 1;
    width: 40%;
    z-index: 9999
}

.td-search-popup .search-form {
    width: 35%;
    top: 50%;
    left: 50%;
    -webkit-transition: .4s;
    -o-transition: .4s;
    transition: .4s;
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.td-search-popup .search-form .form-group .form-control {
    border: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    height: 54px;
    padding: 0 75px 0 25px;
    background: #f7f9fb
}

.td-search-popup .search-form .form-group .form-control:active,
.td-search-popup .search-form .form-group .form-control:focus,
.td-search-popup .search-form .form-group .form-control:hover,
.td-search-popup .search-form .submit-btn:active,
.td-search-popup .search-form .submit-btn:focus,
.td-search-popup .search-form .submit-btn:hover {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.td-search-popup .search-form .submit-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 60px;
    height: 54px;
    border: 0;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    color: #fff;
    -webkit-transition: .4s;
    -o-transition: .4s;
    transition: .4s;
    padding: 0;
    text-align: center;
    cursor: pointer;
    background: var(--main-color)
}

.single-input-inner.style-border-bottom .single-select,
.single-input-inner.style-border-bottom input,
.single-input-inner.style-border-bottom textarea {
    border-bottom: 1px solid rgba(234, 225, 214, .7) !important
}

.body-overlay,
.mfp-zoom-in .mfp-content {
    -webkit-transition: .4s;
    -o-transition: .4s
}

.body-overlay {
    width: 100%;
    height: 100%;
    display: block;
    background: rgba(0, 0, 0, .95);
    content: "";
    left: 0;
    top: 0;
    transition: .4s;
    cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAMFBMVEVMaXH////////////////////////////////////////////////////////////6w4mEAAAAD3RSTlMAlAX+BKLcA5+b6hJ7foD4ZP1OAAAAkUlEQVR4XkWPoQ3CUBQAL4SktoKAbCUjgAKLJZ2ABYosngTJCHSD6joUI6BZgqSoB/+Shqde7sS9x3OGk81fdO+texMtRVTia+TsQtHEUJLdohJfgNNPJHyEJPZTsWLoxShqsWITazEwqePAn69Sw2TUxk1+euPis3EwaXy8RMHSZBIlRcKKnC5hRctjMf57/wJbBlAIs9k1BAAAAABJRU5ErkJggg==), progress
}

.body-overlay.active {
    visibility: visible;
    opacity: .97
}

.mfp-zoom-in .mfp-content {
    opacity: 0;
    -moz-transition: .4s;
    transition: .4s;
    -webkit-transform: scale(.7);
    -ms-transform: scale(.7);
    transform: scale(.7)
}

.mfp-zoom-in.mfp-bg {
    opacity: 0;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    -o-transition: .4s;
    transition: .4s
}

.mfp-zoom-in.mfp-ready .mfp-content {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1)
}

.mfp-zoom-in.mfp-ready.mfp-bg {
    opacity: .7
}

.mfp-zoom-in.mfp-removing .mfp-content {
    -webkit-transform: scale(.7);
    -ms-transform: scale(.7);
    transform: scale(.7);
    opacity: 0
}

.single-input-inner {
    margin-bottom: 20px
}

.single-input-inner input {
    width: 100%;
    height: 50px;
    border: 0 !important;
    padding: 0 18px;
    font-weight: 300
}

.single-input-inner input::placeholder {
    color: #adadad
}

.single-input-inner textarea {
    width: 100%;
    border: 0 !important;
    height: 150px;
    border-radius: 0;
    padding: 14px 18px;
    font-weight: 300
}

.single-input-inner textarea::placeholder {
    color: #adadad
}

.single-input-inner .single-select {
    width: 100%;
    height: 46px;
    line-height: 46px;
    border: 0 !important;
    border-radius: 30px;
    padding: 0 18px;
    margin-bottom: 20px
}

.single-input-inner.style-border .single-select,
.single-input-inner.style-border input,
.single-input-inner.style-border textarea {
    border: 1px solid rgba(234, 225, 214, .7) !important
}

.single-input-inner .single-select .list {
    width: 100%
}

.single-input-inner.style-border input::placeholder {
    color: #adadad
}

.single-input-inner.style-border textarea {
    background: 0 0
}

.single-input-inner.style-border textarea::placeholder {
    color: #adadad
}

.single-input-inner.style-border .single-select {
    color: #adadad
}

.single-input-inner.style-border-bottom input {
    background: 0 0;
    padding: 0
}

.single-input-inner.style-border-bottom input::placeholder {
    color: #adadad
}

.single-input-inner.style-border-bottom textarea::placeholder {
    color: #adadad
}

.single-input-inner.style-border-bottom .single-select {
    background: 0 0;
    color: #adadad
}

.single-input-inner.style-bg input::placeholder {
    color: #65645f
}

.single-input-inner.style-bg textarea::placeholder {
    color: #65645f
}

.single-input-inner.style-bg .single-select {
    background: #f5f3ef;
    color: #65645f
}

.single-input-inner.style-bg-none .single-select,
.single-input-inner.style-bg-none input,
.single-input-inner.style-bg-none textarea {
    background: 0 0;
    color: #fff
}

.single-input-inner.style-bg-none input::placeholder {
    color: #fff
}

.single-input-inner.style-bg-none textarea::placeholder {
    color: #fff
}

.contact-form-inner {
    padding: 0 30px 30px;
    border-radius: 7px;
    overflow: hidden;
    background-size: cover
}

.contact-form-inner .single-input-inner.style-border-bottom input {
    border-bottom: 1px solid #fff !important
}

.contact-form-inner .single-input-inner.style-border-bottom input::placeholder {
    color: #fff
}

.blog-comment-form textarea {
    background: #fbfbfb;
    border: 1px solid #fbfbfb
}

.widget-title {
    position: relative;
    font-size: 20px
}

.td-sidebar .widget {
    margin-bottom: 34px;
    padding: 30px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, .03);
    background: #fff
}

.td-sidebar .widget .widget-title {
    font-size: 24px;
    position: relative;
    padding-bottom: 20px
}

.attax-accordion.style-white .accordion-item .accordion-button.collapsed,
.footer-area .widget-title,
.single-list-media.style-2 .media-body h4,
.single-list-media.style-2 .media-body p,
.single-service-inner-2:hover .details a,
.single-service-inner-2:hover .details h2,
.single-service-inner-2:hover .details p {
    color: #fff
}

.td-sidebar-service .widget {
    margin-bottom: 34px;
    padding: 0;
    border-radius: 0;
    box-shadow: none
}

.td-sidebar-service .widget .widget-title:after {
    border-bottom: 1px dashed var(--main-color);
    background: 0 0;
    width: 156px
}

.widget_search .search-form {
    position: relative;
    background: #f5f3ef;
    border-radius: 0;
    overflow: hidden;
    border: 0
}

.widget_search .search-form input {
    width: 100%;
    border: 0;
    height: 55px;
    padding: 0 60px 0 22px;
    background: #f5f3ef
}

.widget_search .search-form input:focus {
    border: 0;
    outline: 0
}

.widget_search .search-form button {
    position: absolute;
    right: 0;
    border: 0;
    background: var(--main-color);
    cursor: pointer;
    padding: 0 22px;
    top: 0;
    height: 55px;
    color: #fff
}

.widget_search .search-form button:active,
.widget_search .search-form button:focus {
    box-shadow: none;
    outline: 0
}

.widget-recent-post ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 9px;
    padding-bottom: 0
}

.widget-recent-post ul li {
    margin-bottom: 18px;
    border-bottom: 1px solid #ededed;
    padding-bottom: 18px
}

.widget-recent-post ul li .media {
    align-items: center
}

.progressbar-media .media-left,
.widget-recent-post ul li .media .media-left {
    margin-right: 15px
}

.widget-recent-post ul li .media .media-left img {
    border-radius: 50%
}

.widget-recent-post ul li .media .media-body .title {
    margin-top: 8px
}

.widget-recent-post ul li .media .media-body .post-info {
    font-size: 14px;
    color: var(--main-color)
}

.widget-recent-post ul li .media .media-body .post-info span {
    color: #3f517e
}

.widget-recent-post ul li .media .media-body .post-info i,
.widget-recent-post ul li .media .media-body .post-info svg {
    margin-right: 5px;
    color: var(--main-color)
}

.widget-recent-post ul li:last-child {
    border-bottom: 0 !important;
    padding-bottom: 0;
    margin-bottom: 0
}

.widget_catagory ul {
    padding-left: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    margin-top: -5px
}

.widget_catagory ul li {
    list-style: none;
    transition: .6s;
    padding-bottom: 5px;
    margin-bottom: 5px
}

.widget_catagory ul li:last-child {
    border-bottom: 0;
    padding-bottom: 0;
    margin-bottom: 0
}

.widget_catagory ul li a {
    position: relative;
    color: var(--heading-color);
    font-weight: 500
}

.widget_catagory ul li a span {
    float: right
}

.widget_catagory_2 ul,
.widget_contact .details {
    padding: 0
}

.widget_catagory_2 ul li a {
    border: 1px solid #d1d1d1;
    display: block;
    margin-bottom: 20px;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: 600
}

.widget_catagory_2 ul li a i {
    float: right;
    padding-top: 3px;
    font-size: 21px
}

.widget_catagory_2 ul li a span i {
    float: left;
    margin-right: 10px;
    color: var(--main-color);
    background: #ffe6e2;
    padding: 5px 5px 2px;
    border-radius: 3px
}

.widget_catagory_2 ul li a:hover {
    background-image: linear-gradient(to right, #f99746, #ee0020);
    border: 1px solid var(--main-color);
    color: #fff
}

.widget_tag_cloud .tagcloud a {
    background: #f5f3ef;
    height: 36px;
    line-height: 36px;
    padding: 0 18px;
    border-radius: 0;
    display: inline-block;
    margin: 0 5px 9px 0;
    font-family: Lato, sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #909090
}

.widget_tag_cloud .tagcloud a i {
    float: right;
    padding-top: 5px
}

.widget_tag_cloud .tagcloud a:hover {
    background: var(--main-color);
    color: #fff;
    border-color: var(--main-color)
}

.widget_checkbox_list .single-checkbox:last-child {
    margin-bottom: 0;
    border-bottom: 0;
    padding-bottom: 0
}

.single-checkbox {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 7px;
    padding-bottom: 7px;
    cursor: pointer;
    font-size: 16px;
    color: var(--heading-color);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-bottom: 1px solid #e3e3e3
}

.single-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.dot .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.image-arry:hover,
.single-checkbox input:checked~.checkmark {
    background-color: var(--main-color)
}

.single-checkbox .checkmark {
    position: absolute;
    top: 4px;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eae1d6
}

.single-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 8px;
    top: 4px;
    width: 5px;
    height: 10px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

.widget-video-inner {
    border-radius: 5px
}

.widget-video-inner .thumb {
    position: relative;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px
}

.widget-video-inner .thumb:after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .3)
}

.widget-video-inner .thumb .video-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    line-height: 52px;
    z-index: 1
}

.widget-video-inner .thumb .video-btn:after {
    width: 70px;
    height: 70px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.widget-video-inner .details {
    margin-top: 10px
}

.widget-video-inner .details ul {
    padding: 0;
    margin: 0
}

.widget-video-inner .details ul li {
    list-style: none;
    text-align: right;
    display: flex;
    align-items: baseline;
    border-bottom: 1px dashed #e3e3e3;
    padding-bottom: 10px;
    margin-bottom: 10px
}

.widget-video-inner .details ul li i {
    color: var(--main-color);
    margin-right: 7px
}

.widget-video-inner .details ul li span {
    margin-right: auto;
    font-weight: 500;
    color: var(--heading-color)
}

.widget_contact .details li {
    margin-bottom: 18px;
    position: relative;
    list-style: none
}

.widget_contact .details li i {
    margin-right: 14px;
    color: var(--main-color)
}

.widget_contact .details li .time {
    font-size: 12px;
    margin-top: 3px
}

.widget_instagram .instagram-wrap {
    margin: 0 10px
}

.widget_instagram .instagram-wrap .thumb {
    margin-bottom: 10px;
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer
}

.single-blog-inner .thumb img,
.widget_instagram .instagram-wrap .thumb img {
    transform: scale(1.05);
    transition: .9s
}

.widget_instagram .instagram-wrap .thumb:after {
    content: "";
    background: rgba(0, 0, 0, .5);
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    visibility: hidden;
    opacity: 0;
    transition: .4s
}

.widget_instagram .instagram-wrap .thumb i {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: 35px;
    width: 35px;
    line-height: 35px;
    text-align: center;
    background: rgba(253, 74, 54, .7);
    display: inline-block;
    z-index: 3;
    color: #fff;
    border-radius: 50%;
    visibility: hidden;
    opacity: 0;
    transition: .4s
}

.single-blog-inner {
    border-radius: 15px;
    background: #fff;
    box-shadow: 0 8px 18px 0 rgba(0, 0, 0, .1);
    padding: 35px 30px 25px;
    margin-bottom: 30px;
    position: relative;
    z-index: 2
}

.blog-comment .reply a,
.footer__copyright,
.footer__item-title-line,
.footer__item-title-line2 {
    background-color: var(--main-color)
}

.single-blog-inner h4 {
    text-transform: capitalize
}

.single-blog-inner .thumb {
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    border-radius: 12px
}

.error-area h3,
.project-info h3,
.single-blog-inner .details .cat-wrap {
    margin-bottom: 25px
}

.single-blog-inner .details .cat-wrap a {
    height: 28px;
    border-radius: 5px;
    line-height: 28px;
    background: var(--main-color);
    padding: 0 15px;
    color: #fff;
    display: inline-block;
    font-size: 14px;
    margin-right: 5px
}

.single-blog-inner .details h4 {
    margin-bottom: 17px;
    text-transform: capitalize
}

.single-blog-inner .details .date {
    margin-bottom: 25px;
    color: #65645f;
    font-weight: 500
}

.single-blog-inner .read-more {
    font-weight: 700;
    color: var(--heading-color);
    font-family: var(--heading-font);
    transition: .4s
}

.single-blog-inner .read-more img {
    margin-left: 5px;
    filter: brightness(.1);
    transition: .4s
}

.single-blog-inner .read-more:hover img {
    filter: invert(32%) sepia(96%) saturate(2884%) hue-rotate(219deg) brightness(83%) contrast(116%)
}

.single-blog-inner.style-2 {
    padding: 0;
    background: 0 0;
    box-shadow: none
}

.blog-details-page-content .single-blog-inner .thumb,
.blog-details-page-content .single-blog-inner .thumb img,
.single-blog-inner.style-2 .thumb {
    border-radius: 0
}

.paginations {
    display: inline-flex;
    margin: 10px 0 0
}

.dot .swiper-pagination-bullet.swiper-pagination-bullet-active,
.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active,
.paginations .next:hover,
.paginations .prev:hover,
.single-pricing-inner:hover {
    border: 1px solid var(--main-color)
}

.paginations .page-numbers {
    height: 50px;
    width: 50px;
    margin-right: 8px;
    border: 0;
    border-radius: 5px;
    line-height: 50px;
    text-align: center;
    text-decoration: none;
    -webkit-transition: .4s;
    -moz-transition: .4s;
    transition: .4s;
    display: block;
    color: #fff;
    font-size: 18px;
    background: var(--heading-color);
    font-weight: 700
}

.paginations .page-numbers.current {
    background: var(--main-color);
    color: #fff;
    border: 1px solid var(--main-color)
}

.blog-details-page-content .single-blog-inner {
    box-shadow: none !important;
    border-radius: 0;
    border-bottom: 0;
    padding: 0;
    background: 0 0
}

.blog-details-page-content blockquote {
    background: var(--main-color);
    padding: 40px;
    font-weight: 500;
    margin-top: 40px;
    margin-bottom: 40px;
    font-style: italic;
    position: relative
}

.blog-details-page-content blockquote p {
    font-weight: 300;
    color: #fff;
    font-size: 20px;
    margin-top: 25px;
    margin-bottom: 15px
}

.blog-details-page-content blockquote a {
    display: inline-block;
    color: #fff;
    font-size: 20px;
    text-decoration: underline
}

.tag-and-share {
    margin-top: 50px;
    border-bottom: 1px solid #e5e5e5;
    padding: 15px 0
}

.tag-and-share .tags a {
    display: inline-block;
    transition: .4s
}

.tag-and-share strong {
    color: var(--heading-color);
    margin-right: 6px;
    font-size: 20px
}

.banner-slider-control .slick-dots,
.tag-and-share .blog-share ul {
    margin: 0;
    padding: 0;
    display: inline-block
}

.tag-and-share .blog-share ul li {
    display: inline-block;
    list-style: none;
    margin-right: 4px;
    color: var(--heading-color)
}

.tag-and-share .blog-share ul li a {
    height: 42px;
    width: 42px;
    line-height: 42px;
    background: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
    font-size: 15px
}

.prev-next-post {
    padding-top: 20px;
    margin-top: 30px
}

.blog-comment-form {
    padding-top: 30px
}

.blog-comment-form h3 {
    position: relative;
    margin-bottom: 32px
}

.comment-content a {
    word-wrap: break-word
}

.blog-comment {
    position: relative;
    margin-top: 30px
}

.blog-comment .comment-list ul.children {
    margin: 0;
    list-style: none;
    padding-left: 65px
}

.blog-comment .comment-box {
    position: relative;
    margin-bottom: 60px;
    padding-bottom: 60px;
    border-bottom: 1px solid var(--aivons-light, #f2f4f8)
}

.blog-comment .comment {
    position: relative;
    min-height: 90px;
    background: #fff;
    padding: 40px;
    margin-bottom: 30px
}

.blog-comment .comment-box .author-thumb {
    position: absolute;
    left: 0;
    top: 0;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin-bottom: 20px;
    overflow: hidden
}

.blog-comment .comment-box .author-thumb img {
    width: 90px;
    height: 90px;
    display: block;
    border-radius: 50%
}

.blog-comment .comment-box .info {
    position: relative;
    margin-bottom: 25px;
    line-height: 24px
}

.blog-comment .comment-box .info .name {
    position: relative;
    display: block;
    font-size: 24px;
    line-height: 30px;
    font-weight: 800;
    color: var(--aivons-black, #0f0d1d);
    text-transform: uppercase
}

.blog-comment .comment-box .info .date {
    position: relative;
    display: inline-block;
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    color: var(--main-color)
}

.blog-comment .comment-box .text {
    position: relative;
    display: block;
    color: var(--aivons-gray, #726f84);
    font-weight: 300;
    line-height: 1.5em;
    letter-spacing: .1em;
    margin-bottom: 30px
}

.blog-comment .comment-box .reply-btn {
    position: relative;
    display: block
}

.blog-comment .comment-box .theme-btn .btn-title {
    padding: 6px 30px 4px;
    line-height: 30px
}

.blog-comment .comment-body {
    position: relative;
    padding-left: 110px
}

.blog-comment .comment-body .avatar {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    height: 90px;
    width: 90px
}

.blog-comment .comment-body .comment-author {
    font-size: 24px;
    font-family: var(--heading-font);
    color: var(--heading-color);
    font-weight: 700
}

.blog-comment .fn,
.blog-comment .fn a {
    position: relative;
    display: block;
    font-size: 18px;
    color: var(--heading-color);
    text-decoration: none;
    line-height: 1
}

.blog-comment .comment-body .comment-metadata {
    margin-bottom: 13px
}

.blog-comment .comment-body .comment-metadata a {
    position: relative;
    display: inline-block;
    font-size: 12px;
    text-decoration: none
}

.blog-comment .comment-body .comment-metadata a:hover {
    color: var(--aivons-black, #0f0d1d)
}

.blog-comment .comment-body .comment-metadata .edit-link a::before {
    content: ".";
    margin-left: 8px;
    margin-right: 10px
}

.blog-comment .comment-content p {
    position: relative;
    display: block;
    color: var(--aivons-gray, #726f84);
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 2em;
    font-family: var(--aivons-font, "Jost", sans-serif);
    letter-spacing: 0;
    font-weight: 500
}

.blog-comment .comment-content table a {
    color: var(--main-color);
    text-decoration: none;
    color: var(--aivons-black, #0f0d1d)
}

.blog-comment .comment-content {
    font-weight: 500
}

.blog-comment .reply a {
    position: relative;
    color: #fff;
    text-transform: uppercase;
    align-items: center;
    justify-content: center;
    text-align: center;
    text-decoration: none;
    height: 36px;
    line-height: 36px;
    transition: .5s;
    margin-top: 20px;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    border: none;
    outline: 0 !important;
    padding: 0 25px;
    border-radius: 30px;
    text-transform: capitalize
}

@media (min-width:992px) {
    .blog-comment .date {
        margin-top: 5px;
        position: absolute;
        top: 0;
        right: 0
    }
}

.blog-comment .reply a:hover {
    background-color: var(--aivons-black, #0f0d1d);
    color: #fff
}

.blog-comment .blog-pagination a,
.blog-comment .blog-pagination span {
    margin-bottom: 45px
}

.comment-respond .blog-details__content-title,
.service-details img,
.service-details p {
    margin-bottom: 50px
}

.reply-form .logged-in-as {
    display: flex;
    color: var(--main-color);
    margin-left: -10px;
    margin-top: -40px;
    margin-bottom: 20px
}

.reply-form .logged-in-as a {
    text-decoration: none;
    margin-left: 10px;
    margin-right: 10px
}

.comment-form__title small {
    font-size: 20px
}

.comment-form__title small a {
    color: var(--main-color);
    text-decoration: underline
}

.comment-form__title small a:hover {
    color: var(--aivons-black, #0f0d1d);
    opacity: .5
}

.comment-list .comment-respond,
.comment-list .reply-form .form-submit,
.footer__item-title,
.intro-area-inner,
.progressbar-media {
    margin-bottom: 30px
}

.our-info {
    overflow: hidden;
    padding: 65px 0
}

.our-info__input input {
    width: 100%;
    padding: 20px 65px;
    border: none !important
}

.our-info__input-envelope {
    position: absolute;
    left: 22px;
    top: 22px;
    font-size: 26px;
    color: #8e8e8e
}

.our-info__input-plane {
    position: absolute;
    right: 22px;
    top: 24px;
    font-size: 22px;
    color: var(--main-color);
    cursor: pointer
}

.our-info__social-icon a {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    text-align: center;
    color: var(--white);
    font-size: 22px;
    border: 1px solid var(--white);
    margin-left: 10px;
    display: inline-block
}

.our-info__social-icon a:hover {
    background-color: var(--white);
    color: var(--main-color)
}

.footer__item p {
    line-height: 30px;
    color: var(--heading-color)
}

.attax-accordion.style-white .accordion-item .accordion-button,
.banner-content-area a:hover,
.footer__item ul li a:hover,
.footer__item-blog span,
.footer__item-map-popup,
.footer__item-map-popup:hover,
.intro-area-inner:hover .read-more-text,
.pagi-wrp .fa-arrow-right:hover,
.service-tab-inner li button:hover,
.single-case-study-inner:hover .details h4,
.single-service-inner .details h4:hover,
.single-team-inner .details .social a:hover {
    color: var(--main-color)
}

.footer__item-title h4 {
    font-size: 20px;
    line-height: 10px;
    text-transform: capitalize
}

.footer__item-title-line {
    width: 33px;
    height: 2px;
    margin-right: 5px;
    display: inline-block
}

.footer__item-title-line2 {
    width: 0;
    height: 2px;
    transition: .3s ease-in;
    display: inline-block
}

.footer__item-map-popup {
    margin-top: 30px;
    font-weight: 500;
    font-size: 14px
}

.footer__item-blog li {
    display: flex;
    align-items: center
}

.footer__item-blog li h4 {
    font-size: 16px
}

.footer__item-blog span {
    font-size: 14px;
    margin-top: 15px
}

.footer__item:hover .footer__item-title-line2 {
    width: 89px
}

.footer__copyright {
    text-align: center;
    padding: 30px 0
}

.footer__copyright p {
    color: var(--white);
    margin: 0
}

.footer__copyright a {
    color: var(--white)
}

.banner-small-inner {
    margin-top: -100px;
    padding-top: 240px
}

.banner-small-inner h6 {
    font-size: 28px;
    position: relative;
    display: inline-block
}

.banner-small-inner h6:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 3px;
    height: 2px;
    width: 100%;
    background: var(--heading-color)
}

.banner-small-inner h4 {
    position: relative;
    color: var(--main-color);
    padding-left: 55px;
    display: inline-block;
    font-size: 18px;
    text-transform: uppercase
}

.banner-small-inner h4:after {
    content: "";
    position: absolute;
    left: 0;
    top: 10px;
    height: 3px;
    width: 40px;
    background: var(--main-color)
}

.banner-small-inner h2 {
    font-size: 78px;
    line-height: 1.1;
    text-transform: capitalize
}

.banner-two__content h1,
.blog-page-btn {
    line-height: 100px
}

.banner-small-inner h3 {
    font-size: 55px;
    text-transform: capitalize
}

.banner-small-inner .scroll-down {
    margin-top: 50px;
    transition: .4s;
    display: inline-block
}

.banner-small-inner .scroll-down:hover {
    filter: drop-shadow(3px 4px 5px #000)
}

.banner-small-inner-2 h6 {
    font-size: 18px;
    color: var(--main-color)
}

.banner-video-area .banner-animate-image {
    position: absolute;
    right: 0;
    bottom: -130px
}

.banner-slider .slide-item {
    background-size: cover;
    padding: 220px 0;
    position: relative
}

.banner-slider .slide-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, .8)
}

.banner-slider .slide-item:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(250, 100, 0, .2)
}

.banner-video-area-2 .banner-slider .slide-item {
    padding: 200px 0
}

.banner-video-area-2 .banner-slider .slide-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(10, 9, 9, .749)
}

.banner-video-area-2 .banner-content-area {
    background: var(--main-color);
    padding: 30px 100px 30px 50px;
    bottom: 0
}

.banner-video-area-2 .banner-content-area h2 {
    color: #fff;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
    transition: .4s
}

.banner-video-area-2 .banner-content-area p {
    color: #fff;
    position: relative;
    z-index: 2;
    transition: .4s;
    margin-bottom: 0
}

.banner-video-area-2 .banner-content-area:before,
.work-process-thumb .quote-wrap:before {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
    height: 10px;
    width: 10px;
    background: #fff;
    transition: .4s
}

.banner-video-area-2 .banner-content-area:hover:before,
.single-service-inner-2:hover .details:after,
.work-process-thumb:hover .quote-wrap:before {
    height: 100%;
    width: 100%
}

.banner-video-area-2 .banner-animate-image {
    right: -218px;
    bottom: -166px
}

.banner-video-area-2 .banner-animate-image-1 {
    position: absolute;
    left: -218px;
    top: -166px
}

.banner-video-area-3 .banner-content-area {
    left: 0;
    bottom: 0;
    width: 50%;
    padding: 43px
}

.banner-video-area-3 .banner-slider-control {
    position: absolute;
    right: 0
}

.banner-video-area-4 .banner-call {
    display: inline-block;
    position: absolute;
    bottom: 0;
    margin-bottom: 135px;
    left: -210px;
    transform: rotate(-90deg);
    color: var(--heading-color);
    padding-left: 120px
}

.banner-video-area-4 .banner-call:after {
    content: "";
    position: absolute;
    left: 0;
    top: 13px;
    height: 1px;
    width: 100px;
    background-color: var(--heading-color)
}

.banner-video-area-4 .banner-slider-control {
    display: inline-block;
    position: absolute;
    bottom: 50%;
    margin-bottom: -35px;
    right: -100px;
    transform: rotate(-90deg)
}

.banner-video-area-4 .banner-slider-control .slick-dots li:after {
    border-color: var(--heading-color)
}

.attax-accordion,
.service-tab-inner {
    border-top: 1px solid #232222
}

.banner-video-area-4 .scroll-down {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2
}

.banner-video-area-4 .scroll-down a {
    height: 85px;
    width: 85px;
    line-height: 80px;
    text-align: center;
    background: var(--main-color);
    display: inline-block
}

.banner-content-area {
    display: inline-block;
    background: #f5f3ef;
    position: absolute;
    right: 0;
    bottom: -40px;
    padding: 43px 70px
}

.banner-content-area a {
    font-size: 18px;
    font-weight: 500;
    color: var(--heading-color);
    text-decoration: underline;
    transition: .4s
}

.banner-slider-control {
    position: relative;
    display: flex;
    margin-top: -67px
}

.banner-slider-control .slider-controlprogress {
    display: inline-block;
    width: 90px;
    height: 2px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #c8c8c8;
    background-image: linear-gradient(to right, #fff, #fff);
    background-repeat: no-repeat;
    background-size: 0 100%;
    transition: background-size .4s ease-in-out;
    position: absolute;
    left: 22px;
    top: 13px
}

.banner-slider-control .slider__label_Progress,
.testimonial-slider-control .slider__label_Progress {
    width: 90px;
    height: 20px;
    padding: 0;
    margin: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.banner-slider-control .pagination {
    letter-spacing: 57px;
    margin: 0;
    color: #fff
}

.banner-slider-control .slider-dots {
    display: inline-block;
    margin-top: -8px
}

.banner-slider-control .slick-dots li {
    margin: 0 12px;
    border: 0;
    background: #fff;
    list-style: none;
    display: inline-block;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    position: relative;
    -webkit-transition: .3s;
    -moz-transition: .3s;
    transition: .3s;
    font-size: 0;
    cursor: pointer
}

.banner-slider-control .slick-dots li:after {
    position: absolute;
    content: "";
    top: -5px;
    left: -5px;
    border: 1px solid #fff;
    border-radius: 50%;
    width: calc(100% + 10px);
    height: calc(100% + 10px);
    transform: scale(0);
    -webkit-transition: .3s;
    -moz-transition: .3s;
    transition: .3s
}

.banner-slider-control .slick-dots li button {
    background: 0 0;
    border: 0;
    font-size: 0
}

.intro-area-inner img {
    transition: .7s
}

.intro-area-inner:hover .thumb img {
    transform: rotateY(180deg)
}

.intro-area-inner:hover .read-more-text img {
    filter: invert(20%) sepia(97%) saturate(6604%) hue-rotate(237deg) brightness(91%) contrast(95%)
}

.single-case-study-inner.style-2 .details .cs-read-more:hover img,
.single-service-inner:hover .thumb img {
    filter: invert(13%) sepia(92%) saturate(5353%) hue-rotate(240deg) brightness(89%) contrast(100%)
}

.banner-two {
    position: relative
}

.banner-two__image,
.banner-two__slider {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%
}

.banner-two__slider {
    width: 44%;
    z-index: 0
}

.banner-two__image {
    width: 100%
}

.banner-two__content {
    position: relative;
    z-index: 1;
    max-width: 760px;
    padding: 190px 0
}

.banner-two__content h4 {
    color: var(--main-color);
    margin-bottom: 20px;
    text-transform: uppercase;
    font-size: 18px;
    letter-spacing: 3px;
    font-weight: 500
}

.banner-two__dot {
    position: absolute;
    top: 50%;
    left: inherit !important;
    right: 50px;
    width: 30px !important;
    height: 90px;
    transform: translate(0, -30%)
}

.banner-two__video-btn {
    position: absolute;
    right: 30%;
    bottom: 45px
}

.banner-intro {
    margin-top: -165px;
    position: relative;
    z-index: 1;
    background-color: transparent
}

.banner-three__arry-next,
.banner-three__arry-prev {
    top: 50%;
    z-index: 3;
    opacity: 0;
    transition: .3s ease-in;
    visibility: hidden
}

.banner-three {
    position: relative;
    text-transform: capitalize
}

.banner-three__arry-prev {
    position: absolute;
    transform: translateY(-50%) translateX(-60px);
    left: 65px
}

.banner-three__arry-next {
    position: absolute;
    transform: translateY(-50%) translateX(60px);
    right: 65px
}

.banner-three__line-right {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2
}

.banner-three__content {
    padding-top: 300px;
    padding-bottom: 380px;
    text-align: center;
    position: relative
}

.banner-three__content h4 {
    font-weight: 500;
    color: var(--white);
    margin-bottom: 38px;
    text-transform: uppercase
}

.banner-three__content h1,
.image-arry:hover svg {
    color: var(--white)
}

.banner-three__slider .slide-bg:before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /* 轮播图遮罩部分 */
    /* background: linear-gradient(180deg, #1c30e2 0, rgba(0, 0, 0, 0) 100%); */
    z-index: -1
}
/*  */
.banner-three__slider .slide-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    z-index: -2;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    transform: scale(1);
    /* -webkit-transition: 8s ease-out;
    -moz-transition: 8s ease-out;
    -ms-transition: 8s ease-out;
    -o-transition: 8s ease-out;
    transition: 8s ease-out */
}

.banner-three__slider .swiper-slide-active .slide-bg {
    -webkit-transform: scale(1.12);
    -moz-transform: scale(1.12);
    transform: scale(1.12)
}

.banner-three:hover .banner-three__arry-next,
.banner-three:hover .banner-three__arry-prev {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateX(0)
}

@media screen and (max-width:1199px) {

    .banner-two__content .btn-one span,
    .banner-two__content h1,
    .banner-two__content h4,
    .banner-two__content p {
        color: var(--white)
    }

    .banner-two {
        position: relative
    }

    .banner-three br,
    .banner-two br {
        display: none
    }

    .banner-two__slider {
        width: 100%
    }

    .banner-two__content .btn-one {
        border: 2px solid var(--white);
        color: var(--white);
        font-weight: 300
    }
}

@media screen and (max-width:991px) {
    .banner-three__content {
        padding: 100px 0;
        text-align: center;
        position: relative
    }

    .banner-three__content h4 {
        margin-bottom: 28px
    }

    .banner-three__content h1 {
        color: var(--white);
        font-size: 40px;
        line-height: 50px
    }
}

@media screen and (max-width:767px) {
    .banner-two__content h1 {
        line-height: 50px;
        font-size: 40px
    }

    .banner-two__content h4 {
        margin-bottom: 20px
    }
}

@media screen and (max-width:575px) {

    .banner-three button,
    .banner-two .banner-two__dot {
        display: none
    }

    .banner-two__content {
        padding: 60px 0
    }

    .banner-two__content h1 {
        font-size: 30px
    }

    .banner-two__content h1 img {
        width: 40px
    }
}

.about-thumb-area {
    padding-bottom: 190px;
    position: relative;
    padding-right: 190px
}

.about-thumb-area .about-img-2 {
    position: absolute;
    right: 0;
    top: 185px;
    z-index: 1
}

.about-thumb-area .about-img-3 {
    position: absolute;
    right: -50px;
    bottom: -50px;
    z-index: 0
}

.about-thumb-area .about-img-4 {
    position: absolute;
    left: -50px;
    bottom: 50px;
    z-index: 0;
    width: 170px
}

.about-thumb-area .about-img-5 {
    position: absolute;
    right: -50px;
    top: -50px;
    z-index: 0
}

.about-thumb-area .about-img-6 {
    position: absolute;
    left: 40%;
    bottom: 40px;
    z-index: 0
}

.about-thumb-area .about-img-video {
    position: absolute;
    height: 200px;
    width: 200px;
    left: 50%;
    top: 50%;
    margin-left: -100px;
    margin-top: -100px;
    z-index: 3
}

.about-thumb-area .avg-ratting-wrap,
.about-thumb-area .exp-wrap {
    height: 160px;
    display: flex;
    align-items: center;
    position: absolute;
    text-align: center
}

.about-thumb-area .exp-wrap {
    background: var(--main-color);
    width: 170px;
    border: 8px solid #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, .08);
    right: 0;
    top: 0;
    padding: 12px
}

.about-thumb-area .exp-wrap h2 {
    color: #fff;
    font-size: 50px
}

.about-thumb-area .exp-wrap p {
    color: #fff;
    font-size: 15px
}

.about-thumb-area .avg-ratting-wrap {
    background: var(--heading-color);
    width: 170px;
    justify-content: center;
    bottom: 0;
    left: 0;
    z-index: 9
}

.about-thumb-area .avg-ratting-wrap h2 {
    color: #fff;
    font-size: 55px;
    margin-bottom: 0
}

.about-thumb-area .avg-ratting-wrap p {
    color: #fff;
    font-size: 20px
}

.about-thumb-area .left-content {
    position: absolute;
    left: -140px;
    bottom: 225px;
    transform: rotate(-90deg);
    color: var(--heading-color);
    font-size: 24px;
    font-weight: 600;
    padding-left: 20px
}

.about-thumb-area .left-content:after {
    content: "";
    position: absolute;
    left: 0;
    top: 18px;
    height: 8px;
    width: 8px;
    background: var(--heading-color)
}

.counter-area-inner .overlay-title {
    color: #e7e5e1;
    font-size: 200px;
    position: absolute;
    z-index: 0;
    top: -70px
}

.single-counter-inner {
    position: relative;
    z-index: 2;
    margin-bottom: 55px
}

.single-counter-inner h2 {
    font-size: 72px;
    color: var(--main-color);
    margin-bottom: 15px
}

.single-counter-inner h6 {
    margin-bottom: 0;
    font-weight: 600
}

.boxui1 ul,
.single-pricing-inner ul {
    padding: 25px 0 0;
    border-top: 1px solid #e6e6e6;
    margin-top: 25px;
    margin-bottom: 15px
}

.single-list-media {
    background: #fff;
    box-shadow: 0 2px 18px 0 rgba(0, 0, 0, .05);
    padding: 20px 20px 20px 0;
    margin-bottom: 18px
}

.single-list-media .media-left {
    height: 85px;
    width: 85px;
    margin-right: 20px;
    background: var(--heading-color);
    line-height: 85px;
    text-align: center;
    transition: .4s
}

.single-list-media .media-body h4 {
    margin-bottom: 5px
}

.single-list-media.style-2,
.single-list-media.style-3 {
    background: 0 0;
    box-shadow: none;
    padding: 0
}

.single-list-media.style-2 .media-left {
    background: #1c1a1a;
    font-size: 24px;
    color: #fff;
    font-family: var(--heading-font)
}

.single-list-media.style-3 .media-left {
    background: #1c1a1a;
    font-size: 24px;
    color: #fff;
    font-family: var(--heading-font);
    border-radius: 50%;
    height: 80px;
    width: 80px;
    line-height: 80px
}

.single-list-media.style-3 .media-body p {
    color: #65645f;
    font-weight: 600;
    margin-bottom: 5px
}

.single-list-media.style-3:hover .media-left img {
    transform: rotate(360deg)
}

.mission-vision-tab .btn {
    background: var(--heading-color);
    margin-right: 10px;
    margin-top: 0;
    margin-bottom: 10px
}

.mission-vision-content {
    background: #fff;
    padding: 30px 30px 18px
}

.about-section-title {
    margin-left: -200px;
    background: #f5f3ef;
    position: relative;
    z-index: 2;
    padding: 50px;
    margin-top: 65px
}

.quote-area {
    margin-top: -140px;
    position: relative
}

.single-service-inner {
    background: #fff;
    padding: 35px;
    margin-bottom: 30px
}

.single-service-inner .thumb img {
    transition: .5s
}

.single-service-inner .details h4 {
    margin-bottom: 45px;
    position: relative;
    transition: .4s
}

.single-service-inner .details h4:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -17px;
    height: 1px;
    width: 170px;
    background: var(--heading-color);
    transition: .4s
}

.single-service-inner .details .case-view-btn {
    width: 50px;
    height: 50px;
    line-height: 50px;
    color: #fff;
    font-family: var(heading-font);
    background: var(--heading-color);
    text-align: center;
    font-size: 25px;
    transition: .4s;
    display: inline-block;
    font-weight: 300
}

.single-service-inner .details .case-view-btn span {
    font-size: 0;
    transition: .4s;
    margin-top: 0;
    float: left;
    font-weight: 400
}

.single-service-inner:hover .thumb img {
    transform: rotateY(180deg)
}

.single-service-inner:hover .details .case-view-btn {
    width: 160px;
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    background: var(--main-color)
}

.single-service-inner:hover .details .case-view-btn span {
    font-size: 16px;
    margin-right: 12px
}

.service-tab-inner {
    display: block !important;
    border-bottom: 0
}

.service-tab-inner li {
    list-style: none;
    display: block;
    border-bottom: 1px solid #232222
}

.service-tab-inner li button {
    width: 100%;
    text-align: left;
    color: #fff;
    background-color: transparent !important;
    border: 0 !important;
    font-size: 24px;
    font-family: var(--heading-font);
    margin: 0 !important;
    padding: 20px 0 !important;
    font-weight: 600
}

.service-tab-inner li button.active {
    color: var(--main-color) !important
}

.service-tab-inner li button span {
    display: block;
    font-size: 16px;
    color: var(--heading-color)
}

.single-service-inner-2 .animate-img-1 {
    position: absolute;
    right: -38px;
    top: -38px;
    z-index: 0
}

.single-service-inner-2 .animate-img-2 {
    position: absolute;
    left: -30px;
    bottom: 70px;
    z-index: 0
}

.single-service-inner-2 .thumb {
    position: relative;
    z-index: 1
}

.single-service-inner-2 .details {
    background: #fff;
    padding: 40px;
    margin-top: -90px;
    z-index: 2;
    margin-left: 100px;
    margin-right: -100px;
    position: relative
}

.single-service-inner-2 .details a {
    font-weight: 500;
    color: var(--heading-color);
    font-family: var(--heading-font)
}

.single-service-inner-2 .details:after {
    content: "";
    position: absolute;
    right: 15px;
    bottom: 15px;
    height: 16px;
    width: 16px;
    background: var(--main-color);
    transition: .4s
}

.single-team-inner {
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 8px 18px 0 rgba(0, 0, 0, .07);
    padding: 35px;
    transition: .4s;
    margin-bottom: 30px;
    position: relative
}

.single-team-inner .hover-img {
    position: absolute;
    right: 15px;
    top: 15px;
    transition: .4s;
    visibility: hidden;
    opacity: 0
}

.call-to-action-area .btn-wrap,
.single-team-inner .thumb {
    position: relative;
    display: inline-block
}

.single-team-inner .thumb {
    margin-bottom: 20px;
    overflow: hidden;
    border-radius: 50%;
    z-index: 2
}

.single-team-inner .details .social {
    padding-top: 10px
}

.single-team-inner .details .social a {
    border-right: 1px solid #b7b7b7;
    color: var(--heading-color);
    padding: 0 9px 0 4px;
    transition: .4s
}

.single-team-inner:hover {
    box-shadow: 0 8px 18px 0 rgba(0, 0, 0, .1)
}

.single-team-inner:hover .hover-img {
    position: absolute;
    right: 8px;
    top: 8px;
    visibility: visible;
    opacity: 1
}

.call-to-action-area .btn-wrap .top_image_bounce {
    position: absolute;
    right: -122px;
    bottom: 10px
}

.single-testimonial-inner .content {
    margin-bottom: 20px;
    color: var(--heading-color)
}

.single-testimonial-inner.style-2 {
    background: #fff;
    padding: 30px;
    margin: 0 15px
}

.single-testimonial-inner.style-2 .media-left img {
    height: 80px;
    width: 80px;
    border-radius: 50%
}

.testimonial-slider-control {
    display: inline-block;
    position: absolute;
    right: -50px;
    margin-top: -40px
}

.testimonial-slider-control .slider-controlprogress {
    display: inline-block;
    width: 90px;
    height: 2px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #c8c8c8;
    background-image: linear-gradient(to right, var(--main-color), var(--main-color));
    background-repeat: no-repeat;
    background-size: 0 100%;
    transition: background-size .4s ease-in-out;
    position: absolute;
    left: 22px;
    top: 13px
}

.testimonial-slider-control .pagination {
    letter-spacing: 57px;
    margin: 0
}

.testimonial-area {
    position: relative;
    overflow: hidden
}

.testimonial-area .tm-img-animation-1 {
    position: absolute;
    top: -30%;
    left: -50px
}

.testimonial-area .tm-img-animation-2 {
    position: absolute;
    top: 40%;
    left: 50px
}

.single-case-study-inner {
    margin-bottom: 30px;
    position: relative
}

.single-case-study-inner .thumb {
    margin-bottom: 25px;
    position: relative
}

.single-case-study-inner .thumb .case-view-btn {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-weight: 500;
    color: var(--heading-color);
    font-family: var(heading-font);
    background: #fff;
    position: absolute;
    right: 0;
    bottom: 0;
    overflow: hidden;
    text-align: center;
    font-size: 25px;
    transition: .4s
}

.boxui1 ul li:before,
.image-arry svg,
.video-btn a {
    color: var(--main-color)
}

.single-case-study-inner .thumb .case-view-btn span {
    font-size: 0;
    transition: .4s
}

.single-case-study-inner:hover .thumb .case-view-btn {
    width: 180px;
    padding: 0 20px;
    height: 50px;
    line-height: 50px
}

.single-case-study-inner:hover .thumb .case-view-btn span {
    font-size: 16px;
    margin-right: 15px
}

.single-case-study-inner.style-2 {
    margin-bottom: 0;
    position: relative
}

.single-case-study-inner.style-2 .details {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 2;
    padding: 20px;
    width: 100%
}

.single-case-study-inner.style-2 .details .cs-read-more img {
    filter: brightness(100);
    transition: .4s
}

.single-case-study-inner.style-2 .thumb {
    position: relative;
    margin-bottom: 0
}

.single-case-study-inner.style-2 .thumb:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: var(--heading-color);
    transition: .8s;
    border: 1px solid #c4c4c4
}

.single-case-study-inner.style-2 .thumb:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    height: 0;
    width: 100%;
    background: linear-gradient(rgba(9, 9, 121, 0) 10%, #000);
    transition: .8s;
    border: 1px solid transparent
}

.single-case-study-inner.style-2:hover .thumb:before {
    top: -1px;
    height: 0
}

.single-case-study-inner.style-2:hover .thumb:after {
    bottom: 0;
    height: 100%
}

.attax-accordion .accordion-item {
    background: 0 0;
    border-bottom: 1px solid #232222
}

.attax-accordion .accordion-item .accordion-button {
    background: 0 0;
    font-size: 22px;
    padding: 25px 0;
    box-shadow: none;
    color: var(--main-color)
}

.attax-accordion .accordion-item .accordion-button.collapsed:after {
    content: "+"
}

.attax-accordion .accordion-item .accordion-button:after {
    content: "-";
    background-image: none !important;
    font-size: 25px;
    transform: rotate(0);
    width: auto;
    height: auto
}

.attax-accordion .accordion-item .accordion-body {
    padding: 0 0 30px;
    color: #bebebe
}

.attax-accordion.style-2 {
    border: 0 !important
}

.attax-accordion.style-2 .accordion-item {
    background: #fff;
    border: 0;
    padding: 0 30px;
    margin-bottom: 20px
}

.attax-accordion.style-2 .accordion-item .accordion-body {
    color: #65645f;
    border-top: 1px solid #ececec;
    padding-top: 20px
}

.contact-area {
    overflow: hidden;
    position: relative;
    background: #f3f8ff
}

.contact-area .bg-img-1 {
    position: absolute;
    right: 0;
    top: 0;
    width: 45%;
    height: 100%
}

.contact-area .bg-img-2 {
    position: absolute;
    left: 0;
    top: 0;
    width: 30%
}

.contact-area .bg-img-3 {
    position: absolute;
    left: 16%;
    bottom: 0
}

.contact-area .bg-img-4 {
    position: absolute;
    left: 0;
    bottom: 0
}

.contact-inner .contact-from-inner {
    background: #f3f8ff;
    position: relative;
    z-index: 2;
    padding: 50px;
    overflow: hidden
}

.contact-inner .contact-from-inner .ci-bg-img-1 {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 0
}

.blog-page-btn {
    height: 100px;
    width: 100px;
    text-align: center;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    background: var(--main-color)
}

.blog-page-btn:after {
    content: "";
    position: absolute;
    left: 5px;
    top: 5px;
    height: 100%;
    width: 100%;
    background: var(--main-color);
    border-radius: 50%;
    transition: .4s;
    z-index: 0;
    border: 2px solid var(--heading-color)
}

.blog-page-btn:hover:after {
    left: 0;
    top: 0
}

.client-slider .thumb {
    margin: 30px 0;
    text-align: center
}

.client-slider .thumb img:hover {
    box-shadow: 0 10px 20px 0 rgba(46, 56, 220, .05);
    filter: blur(1px)
}

.instagram-area {
    background-size: cover !important;
    background-position: bottom
}

.instagram-slider .thumb {
    position: relative;
    margin: 0 12px
}

.instagram-slider .thumb:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, .5);
    transition: .4s;
    z-index: 0;
    visibility: hidden;
    opacity: 0;
    border-radius: 50%
}

.instagram-slider .thumb a {
    position: absolute;
    left: 50%;
    top: 53%;
    transform: translate(-50%, -50%);
    height: 35px;
    line-height: 33px;
    color: #fff;
    background: var(--main-color);
    z-index: 2;
    display: inline-block;
    padding: 0 20px;
    border-radius: 30px;
    visibility: hidden;
    opacity: 0;
    transition: .4s
}

.instagram-slider .thumb:hover:after {
    visibility: visible;
    opacity: 1;
    border-radius: 0
}

.instagram-slider .thumb:hover a {
    visibility: visible;
    opacity: 1;
    top: 50%
}

.circle-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.circle-counter svg {
    position: relative;
    width: 100px;
    height: 100px;
    transform: rotate(-90deg)
}

.circle-counter svg circle {
    width: 100%;
    height: 100%;
    fill: none;
    stroke: #f0f0f0;
    stroke-width: 3;
    stroke-linecap: round
}

.circle-counter svg circle:last-of-type {
    stroke-dasharray: 325px;
    stroke-dashoffset: calc(325px - 325px * var(--percent)/ 100);
    stroke: var(--heading-color)
}

.work-process-thumb {
    position: relative;
    padding-right: 30px
}

.work-process-thumb .quote-wrap {
    background: var(--main-color);
    width: 300px;
    padding: 30px;
    position: absolute;
    right: 0;
    bottom: 0
}

.work-process-thumb .quote-wrap h4 {
    color: #fff;
    margin-bottom: 0;
    position: relative;
    z-index: 2
}

.single-pricing-inner {
    box-shadow: 0 0 15px rgba(0, 0, 0, .01);
    border: 1px solid #fff;
    padding: 50px 35px;
    background: #fff;
    border-radius: 15px;
    transition: .4s;
    margin-bottom: 30px
}

.single-pricing-inner h2 {
    font-size: 55px
}

.single-pricing-inner .month {
    color: #65645f;
    font-weight: 500
}

.single-pricing-inner ul li {
    list-style: none;
    display: inline-block;
    position: relative;
    padding-left: 20px;
    margin-bottom: 10px
}

.single-pricing-inner ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 9px;
    height: 10px;
    width: 10px;
    background: var(--main-color);
    transition: .4s;
    border-radius: 50%
}

.boxui1 ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px
}

.boxui1 ul li {
    list-style: none;
    display: inline-block;
    position: relative;
    padding-left: 12px;
    margin-bottom: 10px;
    width: calc(33.33% - 10px);
    box-sizing: border-box
}

.boxui1 ul li:before {
    content: "\2713";
    position: absolute;
    left: 0;
    top: 0;
    font-size: 14px;
    transition: .4s
}

.image-arry,
.image-arry svg,
.scroll-up {
    transition: .3s ease-in
}

.image-arry {
    width: 70px;
    height: 70px;
    line-height: 60px;
    text-align: center;
    border-radius: 50%;
    border: 2px solid transparent
}

.dot .swiper-pagination-bullet,
.dot-light .swiper-pagination-bullet {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    transition: .6s;
    background-color: transparent;
    opacity: 1;
    position: relative;
    border: 1px solid transparent
}

.dot .swiper-pagination-bullet::before,
.dot-light .swiper-pagination-bullet::before {
    position: absolute;
    content: "";
    top: 5px;
    left: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #858585;
    transition: .6s
}

.dot-light .swiper-pagination-bullet {
    background-color: transparent;
    border: 1px solid transparent
}

.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.dot-light .swiper-pagination-bullet::before {
    background-color: var(--white)
}

.dot-light .swiper-pagination-bullet.swiper-pagination-bullet-active {
    border: 1px solid var(--white)
}

.video-btn {
    position: relative;
    text-align: center;
    display: inline-block;
    z-index: 2
}

.video-btn a {
    position: relative;
    font-size: 28px;
    z-index: 1;
    background-color: rgba(255, 255, 255, .8);
    width: 90px;
    height: 90px;
    line-height: 90px;
    border-radius: 50%;
    display: block;
    transition: .4s
}

.video-pulse::after,
.video-pulse::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    border: 1px solid var(--white);
    opacity: .7;
    left: 0;
    top: 0;
    border-radius: 50%;
    -webkit-animation-duration: 2.5s;
    animation-duration: 2.5s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-name: video-animation;
    animation-name: video-animation;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

.video-pulse::before {
    -webkit-animation-delay: 1s;
    animation-delay: 1s
}

.pagi-btn {
    width: 50%;
    height: 50%;
    line-height: 50%;
    text-align: center;
    border-radius: 50%;
    border: 1px solid #d9d9d9
}

.pagi-btn.active,
.pagi-btn:hover {
    background-color: var(--main-color);
    color: var(--white);
    border: 1px solid var(--main-color)
}

.pagi-wrp {
    display: flex;
    align-items: center;
    gap: 10px
}

.scroll-up,
.scroll-up::after {
    height: 50px;
    width: 50px;
    cursor: pointer;
    display: block
}

.pagi-wrp .fa-arrow-right {
    font-size: 24px;
    margin-left: 5px;
    color: #888
}

.scroll-up {
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px #d9d9d9;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    position: fixed;
    right: 25px;
    bottom: 35px
}

.scroll-up::after {
    position: absolute;
    font-family: "Font Awesome 5 Free";
    content: "\f106";
    text-align: center;
    line-height: 50px;
    font-weight: 700;
    font-size: 18px;
    color: var(--main-color);
    left: 0;
    top: 0;
    z-index: 1;
    transition: var(--transition)
}

.scroll-up svg path {
    fill: none
}

.scroll-up svg.scroll-circle path {
    stroke: var(--main-color);
    stroke-width: 4px;
    box-sizing: border-box;
    transition: .3s ease-in
}

.scroll-up.active-scroll {
    opacity: 1;
    visibility: visible;
    transform: translateY(0)
}

.project-details h2 {
    margin-top: 50px
}

.project-details p {
    margin-top: 20px
}

.project-info {
    background: #fff;
    padding: 30px 30px 35px;
    margin-top: 55px
}

.project-info p {
    margin-top: 0;
    margin-bottom: 20px
}

.project-info .social-media {
    padding: 0;
    margin-top: 15px
}

.project-info .social-media li a {
    height: 48px;
    width: 48px;
    line-height: 48px;
    background: var(--heading-color);
    font-size: 16px;
    border-radius: 0;
    color: #fff
}

.error-area {
    padding-top: 70px
}

.error-area h2 {
    font-size: 250px;
    color: var(--main-color);
    line-height: 1;
    margin-bottom: 18px
}

.blog-details-area .cat {
    height: 28px;
    line-height: 28px;
    padding: 0 20px;
    background: var(--main-color);
    display: inline-block;
    color: #fff;
    border-radius: 5px;
    font-size: 15px;
    margin-bottom: 3px
}

.blog-details-area h2 {
    margin-top: 25px;
    margin-bottom: 22px
}

.blog-details-area .author-meta {
    display: flex;
    text-align: center
}

.blog-details-area .author-meta img {
    height: 33px;
    width: 33px;
    border-radius: 50%;
    margin-right: 10px
}

.blog-details-area .author-meta p {
    margin-bottom: 0;
    line-height: 1;
    align-self: center;
    font-weight: 500;
    text-align: left
}

.blog-details-area .author-meta p span {
    font-weight: 600;
    color: var(--heading-color)
}

.contact-g-map iframe {
    height: 400px;
    width: 100%;
    margin-bottom: -10px
}

.contact-page-inner {
    background: #fff;
    padding: 70px
}

.team-details-page .thumb {
    background: #f5f3ef;
    padding: 40px 0;
    margin-right: 40px
}

.team-details-page .thumb img {
    width: 100%;
    margin-right: -80px
}

.client-area h5::after,
.client-area h5::before {
    position: absolute;
    content: "";
    width: 250px;
    height: 2px;
    background-color: var(--main-color);
    top: 12px
}

.team-details-page .social-media li a {
    height: 46px;
    width: 46px;
    line-height: 46px;
    border-radius: 0;
    background: var(--heading-color);
    font-size: 16px;
    color: #fff
}

.client-area h5 {
    text-transform: uppercase;
    position: relative;
    display: inline-block
}

.client-area h5::before {
    left: -265px
}

.client-area h5::after {
    right: -265px
}