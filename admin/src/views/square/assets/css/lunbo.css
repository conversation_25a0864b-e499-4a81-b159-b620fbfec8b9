* {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }

    .carousel-container {
     width: 2200px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .icon-tabs {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 20px;
      justify-content: center;
      gap: 40px;
      width: 550px;
      height: 90px;
      margin-left: 35%;
      border-radius: 40px; /* 添加圆角 */
}

    .icon-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      padding: 12px 20px;
      border-radius: 8px;
      transition: all 0.3s ease;
      position: relative;
    }
    
    .icon-tab.active::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: #3b82f6;
      border-radius: 2px;
    }
    
    .icon-tab.active .icon {
      color: #3b82f6; /* 蓝色 */
      font-weight: bold;
      transition: color 0.3s;
    }
  

    .icon-label {
      font-size: 12px;
      color: #6b7280;
      font-weight: 500;
    }

  

    .text-tabs {
      display: flex;
      justify-content: center;
      gap: 60px;
      padding: 20px 40px;
      background: white;
      border-bottom: 1px solid #f1f3f4;
    }

    .text-tab {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.3s ease;
      position: relative;
    }

    .text-tab:hover {
      background: rgba(59, 130, 246, 0.05);
    }

    .text-tab.active {
      color: #3b82f6;
    }

    .text-tab.active .tab-text {
      position: relative;
    }

    .text-tab.active .tab-text::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: #3b82f6;
      border-radius: 1px;
    }

    .tab-bullet {
      font-size: 16px;
      margin-right: 8px;
      color: #3b82f6;
    }

    .text-tab:not(.active) .tab-bullet {
      color: #9ca3af;
    }

    .tab-text {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      white-space: nowrap;
    }

    .text-tab.active .tab-text {
      color: #3b82f6;
      font-weight: 600;
    }
    .text-tab.active .icon {
      color: #3b82f6;
      font-weight: 600;
    }

    .carousel-content {
      padding: 40px;
    }

    .carousel-header {
      text-align: center;
      margin-bottom: 40px;
    }

    .carousel-title {
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 12px;
    }

    .carousel-subtitle {
      font-size: 16px;
      color: #6b7280;
      line-height: 1.6;
    }

    .carousel-wrapper {
      position: relative;
      overflow: hidden;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .carousel-track {
      display: flex;
      transition: transform 0.5s ease;
    }

    .carousel-slide {
      min-width: 100%;
      position: relative;
    }

    .carousel-slide img {
      width: 100%;
      height: 400px;
      object-fit: cover;
      display: block;
    }

    .slide-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      /* background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); */
      color: rgb(0, 0, 0);
      padding: 30px;
    }

    .slide-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .slide-description {
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.4;
      margin: 0 auto;
      display: block;
      text-align: center;
    }

    .carousel-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.9);
      border: none;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-size: 18px;
      color: #374151;
    }

    .carousel-nav:hover {
      background: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .carousel-nav.prev {
      left: 15px;
    }

    .carousel-nav.next {
      right: 15px;
    }

    .carousel-dots {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 20px;
    }

    .carousel-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #d1d5db;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .carousel-dot.active {
      background: #3b82f6;
      transform: scale(1.2);
      color: #6b7280;
    }

    /* 图标默认灰色 */
    .icon-tab svg {
      fill: #b0b3b8;
      transition: fill 0.3s;
      width: 20px;
      height: 20px;
    }
    /* 激活时变主题色（蓝色，可根据需要调整） */
    .icon-tab.active svg {
      fill: #2563eb;
    }