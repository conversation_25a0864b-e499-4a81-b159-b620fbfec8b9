<template>
        <Menu style="background-color:#ffffff ;" />

  <div class="square-list">
    <div class="square-list-banner">
    </div>
    <div class="square-list-main body-center">
      <div class="menu">
        <div class="menu-list">
          <div
            class="menu-list-item"
            @click="queryParams.industry = null"
            :class="!queryParams.industry ? 'is-active' : ''"
            >全部行业
          </div>
          <div
            class="menu-list-item"
            v-for="item in industryList"
            :key="item"
            @click="queryParams.industry = item.value"
            :class="queryParams.industry === item.value ? 'is-active' : ''"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="menu-more is-link" @click="push({ name: 'SquareAllList' })"
          >更多
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="package" v-loading="loading" v-if="list.length">
        <div
          class="package-li"
          v-for="item in list"
          :key="item"
          @click="push({ name: 'SquareDetail', query: { id: item.id } })"
        >
          <div
            class="package-li-img"
            :style="{ 'background-image': 'url(' + item.effectImage + ')' }"
          ></div>
          <div class="package-li-name is-hover-link">{{ item.name }}</div>
          <div class="package-li-desc">{{ item.summary }}</div>
          <div class="package-li-footer">
            <div class="package-li-footer-tag">
              <el-tag type="info" v-for="t in item.tags" :key="t">{{ t }}</el-tag>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else class="!w-full h-500px" />
    </div>
  </div>
  <LoginPanel ref="loginPanel" />
</template>

<script setup lang="ts">
import Menu from './components1/Menu.vue'

import { ArrowRight } from '@element-plus/icons-vue'
import * as MainApi from '@/api/tenant/index'
import { getAccessToken } from '@/utils/auth'
import LoginPanel from '@/views/square/components/login.vue'

const { push } = useRouter() // 路由
const loginPanel = ref()
const industryList = ref([])
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const loading = ref(true)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  industry: null
})

/** 查询行业 */
const getIndustry = async () => {
  try {
    industryList.value = await MainApi.getIndustryList()
  } finally {
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MainApi.getApplicationPage(queryParams)
    if (data.list) {
      data.list.forEach((item: any) => {
        if (item.tags) item.tags = item.tags.split(',')
      })
    }

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const handleUse = function () {
  if (getAccessToken()) {
    push({ name: 'AdminIndex' })
  } else {
    loginPanel.value.openLogin()
  }
}

watch(
  () => queryParams.industry,
  () => {
    getList()
  }
)

onMounted(() => {
  getIndustry()
  getList()
})
</script>

<style scoped lang="scss">
@use '@/styles/square';

.square-list {
  .square-list-banner {
    width: 100%;
    height: 300px;
    background-image: url('@/assets/imgs/square.png');
    background-position: center;
    background-size: cover;
    min-width: 1200px;
    position: relative;

    .square-list-banner-button {
      position: absolute;
      top: 200px;
      left: 160px;
    }
  }
}
</style>
