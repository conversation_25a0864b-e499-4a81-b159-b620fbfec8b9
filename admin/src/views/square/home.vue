<template>
  <div class="home-page">
    <!-- 导航栏 -->
    <nav class="navbar navbar-area navbar-expand-lg">
      <div class="container nav-container navbar-bg">
        <div class="collapse navbar-collapse" id="itech_main_menu">
          <!-- 导航内容 -->
        </div>
        <div class="nav-right-part nav-right-part-desktop align-self-center">
          <!-- 导航右侧内容 -->
        </div>
      </div>
    </nav>

    <!-- Banner 轮播区域 -->
    <Menu />
    <BannerSection />

    <!-- 简介区域 -->
    <IntroSection />

    <!-- 轮播介绍区域 -->
    <CarouselSection />

    <!-- 业务流程区域 -->
    <BusinessProcessSection />

    <!-- 工作手机 -->
    <QRModal />

    <!-- 合作伙伴区域 -->
    <PartnerSection />

    <!-- 统计数据区域 -->
    <CounterSection />

    <!-- 核心功能区域 -->
    <CoreFeaturesSection />

    <!-- 页脚 -->
    <FooterSection />

    <!-- 返回顶部 -->
    <BackToTop />

    <!-- 二维码弹窗 -->
  </div>
</template>

<script setup lang="ts">
// 暂时使用同步导入确保组件正常显示
import Menu from './components1/Menu.vue'
import BannerSection from './components1/BannerSection.vue'
import IntroSection from './components1/IntroSection.vue'
import CarouselSection from './components1/CarouselSection.vue'
import BusinessProcessSection from './components1/BusinessProcessSection.vue'
import PartnerSection from './components1/PartnerSection.vue'
import CounterSection from './components1/CounterSection.vue'
import CoreFeaturesSection from './components1/CoreFeaturesSection.vue'
import FooterSection from './components1/FooterSection.vue'
import BackToTop from './components1/BackToTop.vue'
import QRModal from './components1/QRModal.vue'
</script>

<style scoped>
* {
  background: #ffffff;
}

/* 加载动画样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  background: #ffffff;
}

.loading-spinner {
  text-align: center;
  color: #007bff;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin: 0;
  font-size: 16px;
  color: #666;
}
</style>
