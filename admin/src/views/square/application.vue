<template>
  <div class="application" v-loading="loading">
    <div class="body-center">
      <div class="top-panel">
        <div class="logo">
          <img :src="detailInfo?.logo" />
        </div>
        <div class="detail">
          <div class="detail-name">{{ detailInfo?.name }}</div>
          <div class="detail-summary">{{ detailInfo?.summary }}</div>
          <div class="detail-ur">
            <div class="detail-ur-tag">
              <el-tag type="info" v-for="t in detailInfo?.tags" :key="t">{{ t }}</el-tag>
            </div>
          </div>
        </div>
        <div class="btn">
          <el-button type="primary" size="large" @click="handleOpenPackage">添加应用</el-button>
        </div>
      </div>
      <div v-if="detailInfo" class="preview-panel">
        <iframe v-if="detailInfo.preview" class="w-full h-full" :src="previewAppUrl" frameborder="0"
          sandbox="allow-scripts  allow-same-origin"></iframe>
        <ElImage v-else class="w-full h-full" fit="cover" :src="detailInfo.effectImage" />
      </div>
      <div class="detail-panel">
        <div class="detail-panel-title">系统介绍</div>
        <div class="detail-panel-content" v-html="detailInfo?.detail"></div>
      </div>
    </div>
  </div>
  <ApplicationPayPanel ref="payRef" />
  <LoginPanel ref="loginPanel" />
</template>

<script setup lang="ts">
import * as MainApi from '@/api/tenant/index'
import ApplicationPayPanel from "./components/applicationPay.vue"
import { getAccessToken } from '@/utils/auth'
import LoginPanel from '@/views/square/components/login.vue'

const { query } = useRoute() // 查询参数
const detailInfo = ref<any>() // 列表的数据
const loading = ref(true)
const payRef = ref()
const loginPanel = ref()

const previewAppUrl = computed(() => {
  return `http://devpf.vtuzx.com:43086/square-preview-app?id=${query.id}`
})

/** 查询列表 */
const getDetail = async () => {
  loading.value = true
  try {
    detailInfo.value = await MainApi.getApplicationDetail(query.id)
    if (detailInfo.value.tags) detailInfo.value.tags = detailInfo.value.tags.split(',')
  } finally {
    loading.value = false
  }
}

const handleOpenPackage = () => {
  if (getAccessToken()) {
    payRef.value.open(detailInfo.value.id)
  } else {
    loginPanel.value.openRegister()
  }
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
.application {
  padding-top: 50px;
  ;

  .top-panel {
    display: flex;
    width: 100%;
    margin-bottom: 30px;
    align-items: center;

    .logo {
      margin-right: 20px;

      img {
        width: 90px;
        height: 90px;
        border-radius: 10px;
      }
    }

    .detail {
      flex: 1;

      .detail-name {
        font-size: 26px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .detail-summary {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 5px;
      }

      .detail-ur {
        .detail-ur-tag {
          :deep(.el-tag) {
            margin-right: 5px;
          }
        }
      }
    }

    .btn {}
  }

  .preview-panel {
    width: 100%;
    height: 500px;
    border-radius: 5px;
    box-shadow: 0 2px 7px 0 #0000001a;
    margin-bottom: 50px;
    background: #eee;
  }

  .detail-panel {
    text-align: left;

    .detail-panel-title {
      border-bottom: 3px solid var(--el-color-primary);
      width: 80px;
      margin-bottom: 30px;
      color: var(--el-text-color);
      padding-bottom: 10px;
      font-weight: bold;
      text-align: center;
      font-size: 20px;
    }

    .detail-panel-content {
      padding-bottom: 50px;

      :deep(div) {
        width: 100% !important;
        margin: 0 auto !important;
        max-width: 100% !important;
      }
    }
  }
}
</style>
