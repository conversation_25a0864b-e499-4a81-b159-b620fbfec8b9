<template>
  <div class="square-price">
    <div class="square-price-h1">免费可使用，付费实现更大价值</div>
    <div class="square-price-h2">合适的投入，更高的产出</div>
    <div class="square-price-adv">
      <div class="square-price-adv-item">
        <div class="icon">
          <img src="@/assets/imgs/price-i3.png"/>
        </div>
        <div class="info">
          <div class="info-h1">灵活易用</div>
          <div class="info-h2">系统快速迭代，多端使用</div>
        </div>
      </div>
      <div class="square-price-adv-item">
        <div class="icon">
          <img src="@/assets/imgs/price-i2.png"/></div>
        <div class="info">
          <div class="info-h1">简单好学</div>
          <div class="info-h2">系统快速上线，人人易用</div>
        </div>
      </div>
      <div class="square-price-adv-item">
        <div class="icon">
          <img src="@/assets/imgs/price-i4.png"/></div>
        <div class="info">
          <div class="info-h1">超高性价比</div>
          <div class="info-h2">买一套鑫淼办公，搭多套系统</div>
        </div>
      </div>
      <div class="square-price-adv-item">
        <div class="icon">
          <img src="@/assets/imgs/price-i1.png"/></div>
        <div class="info">
          <div class="info-h1">安全可靠</div>
          <div class="info-h2">国家权威认证，安全有保障</div>
        </div>
      </div>
    </div>
    <div class="square-price-list" v-loading="loading">
      <div class="square-price-list-item" v-for="(item, index) in list" :key="item.id">
        <div class="top">
          <div class="top-h1">{{ item.name }}</div>
          <div class="top-h2">{{ item.summary }}</div>
        </div>
        <div class="price">
          ￥<font>{{ formatMoney((item.price || 0) / 100) }}</font>/人/年
        </div>
        <div class="button">
          <template  v-if="!currentPackageType || currentPackageType <= item.type">
            <template v-if="index === 0">
              <div class="button-li" v-if="!currentPackageType" @click="handleOpenPackage(item.id)">免费注册</div>
              <div class="button-disabled" v-else>当前套餐不可升级</div>
            </template>
            <div class="button-li second-btn" v-if="index === 1" @click="handleOpenPackage(item.id)">立即购买</div>
            <div class="button-li third-btn" v-if="index === 2" @click="handleOpenPackage(item.id)">立即购买</div>
          </template>
          <div class="button-disabled" v-else>当前套餐不可升级</div>
        </div>
        <div class="detail" v-html="item.detail"></div>
        <div class="current" v-if="tokenPackageDetail?.packageList.find((t:any) => t.packageId === item.id)">当前套餐</div>
      </div>
    </div>
  </div>
  <BasePayPanel ref="payRef"/>
  <LoginPanel ref="loginPanel"/>
</template>

<script setup lang="ts">
import BasePayPanel from "./components/basePay.vue"
import LoginPanel from "./components/login.vue"
import * as MainApi from "@/api/tenant/index";
import {formatMoney} from "../../utils";
import {getAccessToken} from "@/utils/auth";

const list = ref<any[]>([]) // 列表的数据
const loading = ref(true)
const payRef = ref()
const loginPanel = ref()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await MainApi.getBasicApplicationPage(queryParams)
  } finally {
    loading.value = false
  }
}

const tokenPackageDetail = ref()
const getTokenPackageDetail = async () => {
  try {
    tokenPackageDetail.value = await MainApi.getPackageDetail()
  } finally {
  }
}

const currentPackageType = computed(() => {
  let maxType = 0
  if(tokenPackageDetail.value && tokenPackageDetail.value.packageList){
    tokenPackageDetail.value.packageList.forEach((t:any) => {
      if(t.packageType > maxType) maxType = t.packageType
    })
  }
  return maxType
})

const handleOpenPackage = (id:any) => {
  if(getAccessToken()){
    payRef.value.open(id)
  } else {
    loginPanel.value.openRegister()
  }
}

onMounted(() => {
  getList()
  getTokenPackageDetail()
})
</script>

<style scoped lang="scss">
.square-price {
  text-align: center;
  padding-top: 50px;
  background-image: url("@/assets/imgs/price.png");
  background-position: top;
  background-repeat: no-repeat;

  .square-price-h1 {
    height: 55px;
    font-weight: bold;
    font-size: 40px;
  }

  .square-price-h2 {
    height: 22px;
    font-weight: 400;
    font-size: 16px;
    color: var(--el-text-color-secondary);
    letter-spacing: 0;
    margin-top: 10px;
  }

  .square-price-adv {
    display: flex;
    justify-content: center;
    text-align: left;
    align-items: center;
    margin: 50px 0;

    .square-price-adv-item {
      display: flex;
      align-items: center;
      width: 230px;
      margin: 0 10px;
      padding: 10px;
      background: #ffffff99;
      border: 1px solid #FFFFFF;
      border-radius: 5px;

      .icon {
        display: flex;
        align-items: center;
        margin-right: 10px;
      }

      .info {
        .info-h1 {
          font-size: 16px;
        }

        .info-h2 {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .square-price-list {
    display: flex;
    justify-content: center;
    min-height: 300px;

    .square-price-list-item {
      width: 300px;
      margin: 0 20px;
      background: #FFFFFF;
      box-shadow: 0 2px 7px 0 #0000001a;
      border-radius: 8px;
      min-height: 550px;
      position: relative;

      &:first-child .top{
        background-image: url("@/assets/imgs/price-p-1.png");
      }
      &:nth-child(2n+2) .top{
        background-image: url("@/assets/imgs/price-p-2.png");
      }
      &:last-child .top{
        background-image: url("@/assets/imgs/price-p-3.png");
      }

      .current{
        position: absolute;
        top: 0;
        left: 0;
        font-size: 12px;
        padding: 4px 15px;
        background: rgba(0,0,0,.2);
        border-radius: 8px 0 8px 0;
        color: #fff;
      }

      .top {
        display: flex;
        flex-direction: column;
        height: 120px;
        justify-content: center;
        color: #fff;
        .top-h1{
          font-weight: 600;
          font-size: 24px;
          margin-top: 10px;
        }
        .top-h2{
          font-size: 14px;
          margin-top: 5px;
        }
      }

      .price {
        margin: 30px 0;

        font {
          font-size: 36px;
          font-weight: bold;
        }
      }

      .button{
        padding: 0 40px;
        .button-disabled{
          color: var(--el-text-color-disabled);
        }
        .button-li{
          width: 100%;
          border: 1px solid var(--el-border-color);
          border-radius: 8px;
          padding: 10px 15px;
          font-size: 16px;
          cursor: pointer;
          &:hover{
            border-color: var(--el-color-primary);
            color: var(--el-color-primary);
          }
          &.second-btn{
            color: var(--el-color-primary);
            border-color: var(--el-color-primary);
            &:hover{
              color: #fff;
              background: var(--el-color-primary);
            }
          }
          &.third-btn{
            background-image: linear-gradient(98deg, #6E749E 0%, #1A2046 100%);
            border-color: transparent;
            color: #fff;
            &:hover{
              background: #1A2046 !important;
            }
          }
        }
      }
      .detail{
        margin-top: 40px;
        text-align: left;
        padding: 0 40px;
        :deep(ul){
          li{
            font-size: 14px;
            color: var(--el-text-color);
            padding: 5px 0;
          }
        }
        .detail-li{
        }
      }
    }
  }
}

</style>
