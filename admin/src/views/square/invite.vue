<template>
  <div class="invite" v-loading="loading">
    <LoginPanel ref="loginRef"/>
  </div>
</template>

<script setup lang="ts">
import * as TenantApi from "@/api/tenant/index";
import LoginPanel from "@/views/square/components/login.vue";

const loginRef = ref()
const loading = ref(true)
const inviteInfo = ref()
const {query} = useRoute() // 查询参数
const {push} = useRouter() // 路由跳转

/** 打开弹窗 */
const getDetail = async () => {
  loading.value = true
  try {
    inviteInfo.value = await TenantApi.getInvitationDetail({
      codeKey: query.key
    })
    loginRef.value.openInvite(inviteInfo.value)
  } catch {
    push({name: "Home"})
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await getDetail()
})
</script>

<style scoped lang="scss">
.invite {
  background: var(--el-color-primary) !important;
  height: 100%;

  .square-login {
    background: transparent !important;
  }
}
</style>
