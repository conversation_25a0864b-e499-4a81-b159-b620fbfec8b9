<template>
  <div class="square-login" :class="[model]" v-if="isShow">
    <div class="square-login-center">
      <div class="banner">
        <img src="@/assets/imgs/login-p.gif"/>
      </div>
      <div class="close" @click="isShow=false">
        <el-icon>
          <Close/>
        </el-icon>
      </div>
      <div class="form" v-if="model === 'login'">
        <LoginForm @change="changeModel"/>
      </div>
      <div class="form" v-else-if="model === 'invite'">
        <InviteForm @change="changeModel" @close="isShow=false" :invite-info="inviteInfo"/>
      </div>
      <div class="form" v-else-if="model === 'register'">
        <RegisterForm @change="changeModel" @close="isShow=false"/>
      </div>
      <div class="form" v-else-if="model === 'company'">
        <CompanyForm @change="changeModel" @close="isShow=false"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {Close} from "@element-plus/icons-vue";
import LoginForm from "./login/loginForm.vue"
import InviteForm from "./login/inviteForm.vue"
import RegisterForm from "./login/registerForm.vue"
import CompanyForm from "./login/companyForm.vue"

const isShow = ref(false)
const model = ref("login")
const inviteInfo = ref()

const changeModel = function (key: string) {
  model.value = key
}

const openLogin = () => {
  isShow.value = true
  model.value = "login"
}
const openRegister = () => {
  isShow.value = true
  model.value = "register"
}
const openInvite = (detail: any) => {
  isShow.value = true
  model.value = "invite"
  inviteInfo.value = detail
}

defineExpose({
  openLogin,
  openRegister,
  openInvite
})
</script>

<style scoped lang="scss">
.square-login {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 10;

  &.company {
    .square-login-center {
      height: 750px;
    }
  }

  .square-login-center {
    border-radius: 20px;
    width: 920px;
    height: 510px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    display: flex;
    overflow: hidden;

    .banner {
      width: 430px;
      height: 100%;
      background-image: url("@/assets/imgs/login.png");
      background-size: 100%;
      background-position: center;
      background-color: #d7e2ff;
      img{
        width: 100%;
      }
    }

    .close {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 20px;
      color: var(--el-text-color-secondary);
      cursor: pointer;

      &:hover {
        color: #000;
      }
    }

    .form {
      padding: 50px 0 70px 0;;
      width: 360px;
      margin-left: 70px;

      .form-title,
      :deep(.form-title) {
        font-weight: 500;
        font-size: 28px;
        color: #262626;
        letter-spacing: 0;
      }

      .form-info,
      :deep(.form-info) {
        height: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        padding-top: 10px;
        letter-spacing: 0;
      }

      .form-center,
      :deep(.form-center) {
        padding-top: 50px;

        .form-center-li {
          padding: 10px 0;
        }

        .form-center-tip {
          color: #8c8c8c;
          font-size: 12px;
          font-weight: 400;
          margin-bottom: 30px;
        }
      }
    }
  }
}
:deep(.el-checkbox){
  width: 100%;
}
</style>
