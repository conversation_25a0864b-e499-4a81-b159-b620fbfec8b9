<template>
  <div class="square-head">
    <div class="square-head-center">
      <div class="square-head-logo" @click="push({name: 'Home'})">
        <img src="@/assets/imgs/logo.png"/>
      </div>
      <div class="square-head-menu">
        <div class="square-head-menu-item" @click="push({name: 'SquareList'})">办公广场</div>
        <div class="square-head-menu-item" @click="push({name: 'SquarePrice'})">定价</div>
        <div class="square-head-menu-item" @click="push({name: 'AboutUs'})">关于我们</div>
      </div>
      <div class="square-head-user" v-if="userInfo && userInfo.id">
        <div class="work-space-btn" @click="push({name: 'AdminIndex'})">工作台</div>
        <ElDropdown class="custom-hover" trigger="click">
          <div class="square-head-user-avatar">
            <ElAvatar :src="avatar"/>
            <span>{{ userName }}</span>
          </div>
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem>
                <Icon icon="ep:tools"/>
                <div @click="toProfile">{{ t('common.profile') }}</div>
              </ElDropdownItem>
              <ElDropdownItem divided @click="loginOut">
                <Icon icon="ep:switch-button"/>
                <div>{{ t('common.loginOut') }}</div>
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
      <div class="square-head-btn" v-else>
        <el-button link @click="loginRef.openLogin()">登录</el-button>
        <el-button link @click="loginRef.openRegister()">注册</el-button>
      </div>
    </div>
  </div>
  <LoginPanel ref="loginRef"/>
</template>

<script setup lang="ts">
import LoginPanel from "./login.vue"
import avatarImg from "@/assets/imgs/avatar.jpg";
import {useUserStore} from "@/store/modules/user";
import {ElMessageBox} from "element-plus";
import {useTagsViewStore} from "@/store/modules/tagsView";

const loginRef = ref()
const {t} = useI18n()

const {push, replace} = useRouter()
const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()

const userInfo = computed(() => userStore.user || null)
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')

const loginOut = async () => {
  try {
    await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    })
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    replace('/square/list?redirect=/index')
  } catch {
  }
}
const toProfile = async () => {
  push('/user/profile')
}
</script>

<style scoped lang="scss">
.square-head {
  height: 54px;
  background: #fff;

  .square-head-center {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    height: 100%;
    align-items: center;

    .square-head-logo {
      display: flex;
      align-items: center;

      img {
        height: 40px;
      }
    }

    .square-head-menu {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: center;

      .square-head-menu-item {
        padding: 0 20px;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }

    .square-head-info {
    }

    .square-head-user {
      height: 100%;
      display: flex;
      align-items: center;
      .work-space-btn{
        border: 1px solid var(--el-color-primary);
        color: var(--el-color-primary);
        border-radius: 5px;
        padding: 3px 10px;
        cursor: pointer;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        &:hover{
          background: var(--el-color-primary);
          color: #fff;
        }
      }
      .square-head-user-avatar {
        display: flex;
        align-items: center;

        .el-avatar {
          width: 24px;
          height: 24px;
        }

        span {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
