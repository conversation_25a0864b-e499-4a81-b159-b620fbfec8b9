<template>
  <div class="square-pay" v-if="isShow">
    <div class="square-pay-center" v-loading="loading">
      <div class="top">
        <div class="user">
          <ElAvatar class="m-r-2" :src="avatar" />
          <span>{{ userName }}</span>
        </div>
        <div class="close" @click="close()">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="center">
        <div class="form" v-if="status === 1">
          <div class="form-top">
            <div class="logo">
              <img :src="detailInfo?.logo" />
            </div>
            <div class="detail">
              <div class="detail-name">{{ detailInfo?.name }}</div>
              <div class="detail-ur">
                <div class="detail-ur-tag">
                  <el-tag type="info">仓库</el-tag>
                  <el-tag type="info">采购</el-tag>
                  <el-tag type="info">销售</el-tag>
                </div>
              </div>
            </div>
            <div class="price">
              ￥<font>{{ formatMoney(detailInfo?.price / 100) }}</font
              >/年
            </div>
          </div>
          <div class="form-power">
            <div class="form-power-title">产品信息</div>
            <div class="form-power-detail" v-html="detailInfo?.detail"> </div>
          </div>
          <el-row :gutter="50" class="!w-full">
            <el-col :span="12">
              <div class="form-count !m-t-0 !m-b-0">
                <div class="form-count-label">购买时长</div>
                <div class="form-count-value">
                  <el-input-number
                    v-model="formData.renewalDuration"
                    :min="1"
                    @change="handleCalcPrice"
                  >
                    <template #suffix>年</template>
                  </el-input-number>
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="form-count">
            <div class="form-count-label">支付方式</div>
            <div class="form-count-value is-channel-code">
              <div
                class="is-channel-code-li"
                @click="payData.channelCode = 'wx_pub'"
                :class="payData.channelCode === 'wx_pub' ? 'is-active' : ''"
              >
                <img src="@/assets/imgs/wechatPay.png" />微信支付
              </div>
              <div
                class="is-channel-code-li"
                @click="payData.channelCode = 'alipay_pc'"
                :class="payData.channelCode === 'alipay_pc' ? 'is-active' : ''"
              >
                <img src="@/assets/imgs/aliPay.png" />支付宝支付
              </div>
            </div>
          </div>
          <div class="form-count">
            <div class="form-count-label">支付金额</div>
            <div class="form-count-value is-price" v-loading="calcLoading">
              ￥<font>{{ formatMoney(calcPriceInfo?.totalPrice / 100) }}</font>
            </div>
            <div class="form-count-btn">
              <el-button
                type="primary"
                size="large"
                :disabled="calcLoading"
                @click="handleCreateOrder"
                :loading="createOrderLoading"
                >确定下单
              </el-button>
            </div>
          </div>
        </div>
        <div class="pay" v-else-if="status === 2">
          <div class="pay-price">
            ￥<font>{{ formatMoney(calcPriceInfo?.totalPrice / 100) }}</font>
          </div>
          <div class="pay-qr">
            <canvas ref="canvasRef"></canvas>
          </div>
          <div class="pay-info">
            请使用{{ payData.channelCode === 'wx_native' ? '微信' : '支付宝' }}支付
          </div>
          <el-button type="primary" link class="m-t-2" @click="handlePayBack">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
        <div class="result" v-else-if="status === 3">
          <div class="result-icon">
            <el-icon class="success" v-if="orderPayResult.status === 10"><SuccessFilled /></el-icon>
            <el-icon class="cancel" v-if="orderPayResult.status === 30"
              ><CircleCloseFilled
            /></el-icon>
          </div>
          <div class="result-label">
            <font v-if="orderPayResult.status === 10">支付成功</font>
            <font v-else-if="orderPayResult.status === 30">支付关闭</font>
          </div>
          <template v-if="orderPayResult.status === 10">
            <div class="result-order">
              <div class="result-order-li">
                <div class="label">订单名称</div>
                <div class="value">{{ orderPayResult?.subject }}</div>
              </div>
              <div class="result-order-li">
                <div class="label">订单金额</div>
                <div class="value">{{ formatMoney(orderPayResult?.price) }}</div>
              </div>
            </div>
            <el-button type="primary" class="m-t-10" @click="close"> 确定 </el-button>
          </template>
          <el-button
            type="primary"
            link
            class="m-t-10"
            @click="handlePayBack"
            v-if="orderPayResult.status === 30">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, CircleCloseFilled, Close, SuccessFilled } from '@element-plus/icons-vue'
import avatarImg from '@/assets/imgs/avatar.jpg'
import { useUserStore } from '@/store/modules/user'
import * as MainApi from '@/api/tenant'
import { formatMoney } from '../../../utils'
import QRCode from 'qrcode'

const loading = ref(true)
const userStore = useUserStore()
const isShow = ref(false)
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')
const canvasRef = ref()
const status = ref(1)
const getOrderStatusTask = ref()

const formData = ref<any>({
  packageId: null,
  renewalDuration: 1
})
const payQrInfo = ref()
const orderInfo = ref()
const orderPayResult = ref()
const payData = ref<any>({
  id: null,
  channelCode: 'wx_native',
  displayMode: 'url'
})
const open = async (id: number) => {
  isShow.value = true
  formData.value = {
    packageId: id,
    renewalDuration: 1
  }
  payData.value = {
    id: null,
    channelCode: 'wx_pub',
    displayMode: 'url'
  }
  orderInfo.value = null
  orderPayResult.value = null
  payQrInfo.value = null
  calcPriceInfo.value = null
  status.value = 1
  await getDetail()
  handleCalcPrice()
}

const close = () => {
  isShow.value = false
  if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
}

const detailInfo = ref({})
/** 查询列表 */
const getDetail = async () => {
  loading.value = true
  try {
    detailInfo.value = await MainApi.getApplicationDetail(formData.value.packageId)
  } finally {
    loading.value = false
  }
}

/**
 * 计算价格
 */
const calcLoading = ref(false)
const calcPriceInfo = ref()
const handleCalcPrice = async () => {
  calcLoading.value = true
  try {
    calcPriceInfo.value = await MainApi.settlementApplicationPackage(formData.value)
  } finally {
    calcLoading.value = false
  }
}

/**
 * 创建订单
 */
const createOrderLoading = ref(false)
const handleCreateOrder = async () => {
  createOrderLoading.value = true
  try {
    orderInfo.value = await MainApi.createApplicationPackage(formData.value)
    await handleCreatePay()
  } finally {
    createOrderLoading.value = false
  }
}

/**
 * 发起支付
 */
const handleCreatePay = async () => {
  try {
    payQrInfo.value = await MainApi.payPackageOrderOrder({
      id: orderInfo.value.payOrderId,
      channelCode: payData.value.channelCode,
      displayMode: payData.value.displayMode,
      returnUrl: location.href
    })
    status.value = 2
    await nextTick(() => {
      QRCode.toCanvas(canvasRef.value, payQrInfo.value.displayContent)
      if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
      getOrderStatusTask.value = setInterval(() => {
        getOrderPayResult()
      }, 2000)
    })
  } finally {
  }
}

/**
 * 查询订单支付结果
 */
const getOrderPayResult = async () => {
  try {
    orderPayResult.value = await MainApi.getPayPackageOrderDetail(orderInfo.value.payOrderId)
    if (orderPayResult.value?.status !== 0) {
      status.value = 3
    }
  } finally {
  }
}

/**
 * 返回
 */
const handlePayBack = () => {
  status.value = 1
}

watch(
  () => status.value,
  () => {
    switch (status.value) {
      case 1:
        payQrInfo.value = null
        orderPayResult.value = null
        if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
        break
      case 2:
        break
      case 3:
        if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
        break
    }
  }
)

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.form-power-detail {
  font-size: 14px;
  :deep(div) {
    width: 100% !important;
    margin: 0 auto !important;
    max-width: 100% !important;
  }
}
</style>
