<template>
  <el-dialog title="新建应用" v-model="showDialog" width="1200px">
    <div class="square-list">
      <el-input
        placeholder="搜索应用"
        v-model="queryParams.name"
        class="!w-300px"
        @keyup.enter="getList"
      />
      <div class="square-list-main body-center">
        <div class="menu">
          <div class="menu-list">
            <div
              class="menu-list-item"
              @click="queryParams.industry = null"
              :class="!queryParams.industry ? 'is-active' : ''"
              >全部行业
            </div>
            <div
              class="menu-list-item"
              v-for="item in industryList"
              :key="item"
              @click="queryParams.industry = item.value"
              :class="queryParams.industry === item.value ? 'is-active' : ''"
              >{{ item.label }}
            </div>
          </div>
        </div>
        <div class="package" v-loading="loading" v-if="list.length">
          <div class="package-li" v-for="item in list" :key="item" @click="handleOpenPackage(item)">
            <div
              class="package-li-img"
              :style="{ 'background-image': 'url(' + item.effectImage + ')' }"
            ></div>
            <div class="package-li-name is-hover-link">{{ item.name }}</div>
            <div class="package-li-desc">{{ item.summary }}</div>
            <div class="package-li-footer">
              <div class="package-li-footer-tag">
                <el-tag type="info" v-for="t in item.tags" :key="t">{{ t }}</el-tag>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-else class="!w-full h-500px" />
      </div>
    </div>
    <ApplicationPayPanel ref="payRef" />
  </el-dialog>
</template>

<script setup lang="ts">
import * as MainApi from '@/api/tenant/index'
import ApplicationPayPanel from '@/views/square/components/applicationPay.vue'

const { push } = useRouter() // 路由
const showDialog = ref(false)
const industryList = ref([])
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const loading = ref(true)
const payRef = ref()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  industry: null,
  name: ''
})

/** 查询行业 */
const getIndustry = async () => {
  try {
    industryList.value = await MainApi.getIndustryList()
  } finally {
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MainApi.getApplicationPage(queryParams)
    if (data.list) {
      data.list.forEach((item: any) => {
        if (item.tags) item.tags = item.tags.split(',')
      })
    }

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleOpenPackage = (item:any) => {
  payRef.value.open(item.id)
}

watch(
  () => queryParams.industry,
  () => {
    getList()
  }
)
const open = async () => {
  showDialog.value = true
  getIndustry()
  getList()
}

const close = () => {
  showDialog.value = false
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
@use '@/styles/square';
.square-list {
}
</style>
