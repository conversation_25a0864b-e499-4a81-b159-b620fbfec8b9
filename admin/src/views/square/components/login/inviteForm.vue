<template>
  <template v-if="step === 1">
    <div class="form-title">{{ inviteInfo?.userName }} 邀请你加入企业</div>
    <div class="form-info">已有鑫淼办公账户？<span class="is-link" @click="emits('change', 'login')">直接登录</span>
    </div>
    <div class="form-center is-join" v-if="inviteInfo?.code === 400">
      <div class="is-join-icon">
        <el-icon><Warning /></el-icon>
      </div>
      <div class="is-join-label">已加入当前企业</div>
      <div class="is-join-home m-t-2">
        <el-button type="primary" @click="push({name: 'Index'})" link>返回首页</el-button>
      </div>
    </div>
    <div class="form-center" v-else-if="!getAccessToken()">
      <el-form
          ref="formRegisterRef"
          :model="formData"
          :rules="formRules"
          class="login-form"
          label-position="top"
          label-width="120px"
          size="large">
        <el-row style="margin-right: -10px; margin-left: -10px">
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="mobile">
              <el-input
                  v-model="formData.mobile"
                  placeholder="请输入手机号"
                  :prefix-icon="iconAvatar"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="smsCode">
              <el-input
                  v-model="formData.smsCode"
                  placeholder="请输入验证码"
                  :prefix-icon="iconLock"
                  type="number"
              />
              <div class="send-code-label" @click="handleSendCode">
                {{ codeSeconds < 60 ? (codeSeconds + 's后重新发送') : '发送验证码' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="userName">
              <el-input
                  v-model="formData.userName"
                  placeholder="请输入姓名"
                  :prefix-icon="iconLock"
              />
            </el-form-item>
          </el-col>
          <el-col
              :span="24"
              style="padding-right: 10px; padding-left: 10px; margin-top: -10px; margin-bottom: -20px">
            <el-form-item prop="agree">
              <el-checkbox v-model="formData.agree">
                注册即代表我已阅读并同意<span class="is-link" @click.stop="handleToProtocol(1)">《服务协议》</span>和<span
                  class="is-link" @click="handleToProtocol(2)">《隐私政策》</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px;margin-top: 30px;">
            <el-form-item>
              <XButton
                  :loading="loginLoading"
                  title="注册并加入"
                  class="w-[100%]"
                  type="primary"
                  @click="handleConfirm()"
              />
            </el-form-item>
          </el-col>
          <Verify
              ref="verify"
              :captchaType="captchaType"
              :imgSize="{ width: '400px', height: '200px' }"
              mode="pop"
              @success="sendCode"
          />
        </el-row>
      </el-form>
    </div>
    <div class="form-center" v-else>
      <div class="user-tip">当前登录</div>
      <div class="user-info">
        <ElAvatar :src="avatar"/>
        <span>
        {{ userName }}
      </span>
      </div>
      <XButton
          :loading="loginLoading"
          title="立即加入"
          class="w-[100%]"
          type="primary"
          size="large"
          @click="handleJoin()"
      />
    </div>
  </template>
</template>

<script setup lang="ts">
import {useIcon} from "@/hooks/web/useIcon";
import {propTypes} from "@/utils/propTypes";
import * as TenantApi from "@/api/tenant";
import {getAccessToken} from "@/utils/auth";
import {useFormValid} from "@/views/Login/components/useLogin";

import avatarImg from '@/assets/imgs/avatar.jpg'
import {useUserStore} from "@/store/modules/user";
import {Warning} from "@element-plus/icons-vue";
// 定义属性
const props = defineProps({
  inviteInfo: propTypes.object.def(''),
})
const emits = defineEmits(['change', 'close'])

const {push} = useRouter() // 路由跳转
const userStore = useUserStore()
const formRegisterRef = ref()
const {validForm} = useFormValid(formRegisterRef)
const iconAvatar = useIcon({icon: 'ep:avatar'})
const iconLock = useIcon({icon: 'ep:lock'})
const loginLoading = ref(false)
const step = ref(1)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字
const codeSeconds = ref(60)
const codeTimeOut = ref()
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')
const message = useMessage() // 消息弹窗

const formRules = {
  mobile: [
    {required: true, message: '手机不能为空', trigger: ['blur', 'change']},
    {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: ['blur', 'change']}
  ],
  smsCode: [
    {required: true, message: '验证码不能为空', trigger: ['blur', 'change']},
  ],
  userName: [
    {required: true, message: '姓名不能为空', trigger: ['blur', 'change']},
  ],
  agree: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback("请先阅读相关协议")
        }
        callback()
      }, trigger: ['blur', 'change']
    }
  ]
}
const formData = ref({
  formDataLoginForm: null,
  mobile: null,
  agree: false,
  smsCode: null,
  userName: null
})

// 获取验证码
const handleSendCode = async () => {
  if (codeSeconds.value === 60) {
    formRegisterRef.value.validateField(["mobile"], (validate: any) => {
      if (validate) {
        verify.value.show()
      }
    })
  }
}
const sendCode = async (params: any) => {
  try {
    formData.value.formDataLoginForm = params.captchaVerification
    if (codeTimeOut.value) clearTimeout(codeTimeOut.value)
    await TenantApi.sendSmsCode({
      mobile: formData.value.mobile
    })
    codeSeconds.value--
    let timeOut = () => {
      codeTimeOut.value = setTimeout(() => {
        if (codeSeconds.value > 0) {
          codeSeconds.value--
          timeOut()
        } else {
          codeSeconds.value = 60
          clearTimeout(codeTimeOut.value)
        }
      }, 1000)
    }
    timeOut()
  } finally {
  }
}

const handleConfirm = async function () {
  try {
    const data = await validForm()
    if (!data) {
      return
    }
    loginLoading.value = true
    const res = await TenantApi.joinInviteCompany({
      mobile: formData.value.mobile,
      smsCode: formData.value.smsCode,
      userName: formData.value.userName,
      codeKey: props.inviteInfo.codeKey
    })
    if (!res) {
      return
    }
    message.success("已成功加入企业！")
    emits("change", "login")
  } finally {
    loginLoading.value = false
  }
}

const handleJoin = async function () {
  try {
    loginLoading.value = true
    const res = await TenantApi.joinInviteCompany({
      codeKey: props.inviteInfo.codeKey
    })
    if (!res) {
      return
    }
    emits("change", "login")
  } finally {
    loginLoading.value = false
  }
}

const handleToProtocol = (type: any) => {
  push({name: 'Protocol', query: {type: type}})
  emits("close")
}

onMounted(() => {
})
</script>

<style scoped lang="scss">
:deep(.mask) {
  height: 100%;
}

.tenant-li {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 5px;
  width: calc(100% + 20px);
  margin-left: -10px;

  &:hover {
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }

  .tenant-li-logo {
    width: 30px;
    height: 30px;
    background: #eee;
    border-radius: 5px;
  }

  .tenant-li-name {
    flex: 1;
    margin: 0 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.send-code-label {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 14px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.user-tip{
  color: var(--el-text-color);
  margin-bottom: 10px;
  font-size: 14px;
}
.user-info{
  display: flex;
  align-items: center;
  background: #f6f6f6;
  padding: 10px 20px;
  margin-bottom: 50px;
  border-radius: 10px;
  span{
    margin-right: 10px;
  }
}
.is-join{
  background: #f6f6f6;
  margin: 50px 0;
  padding: 50px 0;
  text-align: center;
  .is-join-icon{
    color: var(--el-color-warning);
    font-size: 80px;
  }
  .is-join-label{
    font-size: 14px;
  }
}
</style>
