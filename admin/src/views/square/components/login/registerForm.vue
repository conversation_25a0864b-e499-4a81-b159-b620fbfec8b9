<template>
  <div class="form-title">注册鑫淼办公账户</div>
  <div class="form-info">已有鑫淼办公账户？<span class="is-link" @click="emits('change', 'login')">直接登录</span></div>
  <div class="form-center ">
    <el-form
        ref="formRegisterRef"
        :model="formData"
        :rules="formRules"
        class="login-form"
        label-position="top"
        label-width="120px"
        size="large">
      <el-row style="margin-right: -10px; margin-left: -10px">
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="mobile">
            <el-input
                v-model="formData.mobile"
                placeholder="请输入手机号"
                :prefix-icon="iconAvatar"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="code">
            <el-input
                v-model="formData.code"
                placeholder="请输入验证码"
                :prefix-icon="iconLock"
                type="number"
            />
            <div class="send-code-label" @click="handleSendCode">
              {{ codeSeconds < 60 ? (codeSeconds + 's后重新发送') : '发送验证码' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col
            :span="24"
            style="padding-right: 10px; padding-left: 10px; margin-top: -10px; margin-bottom: -20px"
        >
          <el-form-item prop="agree">
            <el-checkbox v-model="formData.agree">
              注册即代表我已阅读并同意<span class="is-link" @click.stop="handleToProtocol(1)">《服务协议》</span>和<span
                class="is-link" @click="handleToProtocol(2)">《隐私政策》</span>
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px;margin-top: 30px;">
          <el-form-item>
            <XButton
                :loading="loginLoading"
                :title="t('login.register')"
                class="w-[100%]"
                type="primary"
                @click="handleRegister"
            />
          </el-form-item>
        </el-col>
        <Verify
            ref="verify"
            :captchaType="captchaType"
            :imgSize="{ width: '400px', height: '200px' }"
            mode="pop"
            @success="sendCode"
        />
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import * as TenantApi from "@/api/tenant/index";
import {useIcon} from "@/hooks/web/useIcon";
import {useFormValid} from "@/views/Login/components/useLogin";
import * as authUtil from "@/utils/auth";

const emits = defineEmits(['change', 'close'])

const {t} = useI18n()
const iconAvatar = useIcon({icon: 'ep:avatar'})
const iconLock = useIcon({icon: 'ep:lock'})
const formRegisterRef = ref()
const {validForm} = useFormValid(formRegisterRef)
const loginLoading = ref(false)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字
const codeSeconds = ref(60)
const codeTimeOut = ref()
const {push} = useRouter() // 路由跳转

const formRules = {
  mobile: [
    {required: true, message: '手机不能为空', trigger: ['blur', 'change']},
    {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: ['blur', 'change']}
  ],
  code: [
    {required: true, message: '验证码不能为空', trigger: ['blur', 'change']},
  ],
  agree: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback("请先阅读相关协议")
        }
        callback()
      }, trigger: ['blur', 'change']
    }
  ]
}
const formData = ref({
  mobile: null,
  agree: false,
  formDataLoginForm: null,
  code: null
})

// 获取验证码
const handleSendCode = async () => {
  if (codeSeconds.value === 60) {
    formRegisterRef.value.validateField(["mobile"], (validate: any) => {
      if (validate) {
        verify.value.show()
      }
    })
  }
}
const sendCode = async (params: any) => {
  try {
    formData.value.formDataLoginForm = params.captchaVerification
    if (codeTimeOut.value) clearTimeout(codeTimeOut.value)
    await TenantApi.sendSmsCode({
      mobile: formData.value.mobile
    })
    codeSeconds.value--

    let timeOut = () => {
      codeTimeOut.value = setTimeout(() => {
        if (codeSeconds.value > 0) {
          codeSeconds.value--
          timeOut()
        } else {
          codeSeconds.value = 60
          clearTimeout(codeTimeOut.value)
        }
      }, 1000)
    }
    timeOut()
  } finally {
  }
}

/**
 * 确认注册
 */
const handleRegister = async () => {
  try {
    const data = await validForm()
    if (!data) {
      return
    }
    loginLoading.value = true
    const res = await TenantApi.mobileRegisterLogin({
      mobile: formData.value.mobile,
      code: formData.value.code
    })
    if (!res) {
      return
    }
    authUtil.setToken(res)
    authUtil.setTenantId(res?.tenantId)
    emits("change", "company")
  } finally {
    loginLoading.value = false
  }
}

const handleToProtocol = (type: any) => {
  push({name: 'Protocol', query: {type: type}})
  emits("close")
}
</script>

<style scoped lang="scss">
.send-code-label {
  position: absolute;
  top: 0px;
  right: 20px;
  font-size: 14px;
  cursor: pointer;
  color: var(--el-color-primary);
}

:deep(.mask) {
  height: 100%;
}
</style>
