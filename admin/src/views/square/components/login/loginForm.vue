<template>
  <template v-if="step === 1">
    <div class="form-title">登录</div>
    <div class="form-info"
    >还没有鑫淼办公账户？<span class="is-link" @click="emits('change', 'register')"
    >立即注册</span
    ></div
    >
    <div class="form-center ">
      <el-form
          ref="formLogin"
          :model="loginData.loginForm"
          :rules="LoginRules"
          class="login-form"
          label-position="top"
          label-width="120px"
          size="large"
      >
        <el-row style="margin-right: -10px; margin-left: -10px">
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="username">
              <el-input
                  v-model="loginData.loginForm.username"
                  :placeholder="t('login.usernamePlaceholder')"
                  :prefix-icon="iconAvatar"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="password">
              <el-input
                  v-model="loginData.loginForm.password"
                  :placeholder="t('login.passwordPlaceholder')"
                  :prefix-icon="iconLock"
                  show-password
                  type="password"
                  @keyup.enter="getCode()"
              />
            </el-form-item>
          </el-col>
          <el-col
              :span="24"
              style="padding-right: 10px; padding-left: 10px; margin-top: -20px; margin-bottom: -20px"
          >
            <el-form-item>
              <el-row justify="space-between" style="width: 100%;margin-top: 10px;">
                <el-col :span="6">
                  <el-checkbox v-model="loginData.loginForm.rememberMe">
                    {{ t('login.remember') }}
                  </el-checkbox>
                </el-col>
                <!--                <el-col :offset="6" :span="12">-->
                <!--                  <el-link-->
                <!--                    style="float: right"-->
                <!--                    type="primary"-->
                <!--                    @click="setLoginState(LoginStateEnum.RESET_PASSWORD)"-->
                <!--                  >-->
                <!--                    {{ t('login.forgetPassword') }}-->
                <!--                  </el-link>-->
                <!--                </el-col>-->
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px; margin-top: 30px">
            <el-form-item>
              <XButton
                  :loading="loginLoading"
                  :title="t('login.login')"
                  class="w-[100%]"
                  type="primary"
                  @click="getCode()"
              />
            </el-form-item>
          </el-col>
          <Verify
              v-if="loginData.captchaEnable === 'true'"
              ref="verify"
              :captchaType="captchaType"
              :imgSize="{ width: '400px', height: '200px' }"
              mode="pop"
              @success="handleLogin"
          />
        </el-row>
      </el-form>
    </div>
  </template>
  <template v-else-if="step === 2">
    <div class="form-title">选择企业</div>
    <div class="form-info">还没有鑫淼办公账户？<span class="is-link" @click="emits('change', 'register')">立即注册</span>
    </div>
    <div class="form-center">
      <div
          class="tenant-li"
          v-for="item in tenantList"
          :key="item"
          @click="handleChangeTenant(item.id)"
      >
        <div class="tenant-li-logo">
          <img :src="item.companyLogo" v-if="item.companyLogo"/>
          <img v-else src="@/assets/imgs/avatar.jpg"/>
        </div>
        <div class="tenant-li-name">{{ item.name }}</div>
        <div class="tenant-li-right">
          <el-icon size="14">
            <ArrowRight/>
          </el-icon>
        </div>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import {useIcon} from '@/hooks/web/useIcon'
import {useFormValid, useLoginState} from '@/views/Login/components/useLogin'
import {usePermissionStore} from '@/store/modules/permission'
import {ArrowRight} from '@element-plus/icons-vue'
import {getAccessToken, getUnionId} from "@/utils/auth";
import {useUserStore} from "@/store/modules/user";

const {push} = useRouter()
const {params, name, query} = useRoute() // 查询参数

const {t} = useI18n()
const iconAvatar = useIcon({icon: 'ep:avatar'})
const iconLock = useIcon({icon: 'ep:lock'})
const formLogin = ref()
const {validForm} = useFormValid(formLogin)
const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const loginLoading = ref(false)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字
const tenantList = ref([])
const step = ref(1)
const emits = defineEmits(['change', 'close'])
const userStore = useUserStore()

const LoginRules = {
  username: [
    {required: true, message: '用户名不能为空', trigger: ['blur', 'change']},
  ],
  password: [
    {required: true, message: '密码不能为空', trigger: ['blur', 'change']},
  ],
}
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    username: import.meta.env.VITE_APP_DEFAULT_LOGIN_USERNAME || '',
    password: import.meta.env.VITE_APP_DEFAULT_LOGIN_PASSWORD || '',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})

// 获取验证码
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}
// 记住我
const getLoginFormCache = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe
    }
  }
}

const loginToken = ref()
// 登录
const handleLogin = async (params: any) => {
  loginLoading.value = true
  try {
    const data = await validForm()
    if (!data) {
      return
    }
    const loginDataLoginForm = {...loginData.loginForm}
    loginDataLoginForm.captchaVerification = params.captchaVerification
    const res = await LoginApi.login(loginDataLoginForm)
    if (!res) {
      return
    }
    // 记住密码
    if (loginDataLoginForm.rememberMe) {
      authUtil.setLoginForm(loginDataLoginForm)
    } else {
      authUtil.removeLoginForm()
    }
    loginToken.value = res
    await getSelectTenantList(res.unionId)
  } finally {
    loginLoading.value = false
  }
}

// 查询租户列表
const getSelectTenantList = async (unionId: string) => {
  tenantList.value = await LoginApi.getTenantList(unionId)
  if (tenantList.value) {
    if (tenantList.value.length === 1) {
      await handleChangeTenant(tenantList.value[0].id)
    } else {
      step.value = 2
    }
  } else {
    ElMessage.error("无租户数据，登录失败！")
  }
}

// 选择租户
const handleChangeTenant = async (tenantId: any) => {
  try {
    // loading.value = ElLoading.service({
    //   lock: true,
    //   text: '正在加载系统中...',
    //   background: 'rgba(0, 0, 0, 0.7)'
    // })
    if(loginToken.value) authUtil.setToken(loginToken.value)
    let res = await LoginApi.changeTenant(tenantId)
    authUtil.setToken(res)
    authUtil.setTenantId(tenantId)

    if (name === "Invite") {
      await push({path: '/home'})
    }
    window.location.reload() // 重新加载当前页面

    // if (!redirect.value) {
    //   redirect.value = '/home'
    // }
    // if (redirect.value.indexOf('sso') !== -1) {
    //   window.location.href = window.location.href.replace('/login?redirect=', '')
    // } else {
    //   await push({ path: redirect.value || permissionStore.addRouters[0].path })
    // }
  } finally {
    loginLoading.value = false
    // loading.value.close()
  }
}

onMounted(() => {
  getLoginFormCache()

  if (getAccessToken()) {
    step.value = 2
    getSelectTenantList(getUnionId())
  }
})
</script>

<style scoped lang="scss">
:deep(.mask) {
  height: 100%;
}

.tenant-li {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 5px;
  width: calc(100% + 20px);
  margin-left: -10px;

  &:hover {
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }

  .tenant-li-logo {
    width: 30px;
    height: 30px;

    img {
      width: 30px;
      height: 30px;
      border-radius: 5px;
    }
  }

  .tenant-li-name {
    flex: 1;
    margin: 0 10px;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
