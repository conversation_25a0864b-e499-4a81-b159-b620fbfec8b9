<template>
  <div class="square-pay" v-if="isShow">
    <div class="square-pay-center" v-loading="loading">
      <div class="top">
        <div class="user">
          <ElAvatar class="m-r-2" :src="avatar" />
          <span>{{ userName }}</span>
        </div>
        <div class="close" @click="close">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="center">
        <div class="form" v-if="status === 1">
          <div class="form-token" v-if="currentPackage"
            >当前套餐：<font>{{ currentPackage.name }}</font></div
          >
          <div class="form-package">
            <template v-for="item in list" :key="item.id">
              <div
                class="form-package-item"
                :class="formData.newPackageId === item.id ? 'is-active' : ''"
                @click="handlePackage(item)"
              >
                <div class="name">{{ item.name }}</div>
                <div class="price"
                  >¥<font>{{ formatMoney(item.price / 100) }}</font
                  >/人/年
                </div>
                <div class="tip">{{ item.summary }}</div>
                <!--              <div class="badge">买一年送一个月</div>-->
              </div>
            </template>
          </div>
          <div class="form-power">
            <div class="form-power-title">套餐权益</div>
            <div class="form-power-detail" v-html="currentPackage?.detail"></div>
          </div>
          <el-row :gutter="50" class="!w-full">
            <el-col :span="12">
              <div class="form-count !m-t-0 !m-b-0">
                <div class="form-count-label">购买人数</div>
                <div class="form-count-value">
                  <el-input-number
                    v-model="formData.newAccountCount"
                    @change="handleCalcPrice"
                    :min="activePackageInfo?.minAccountCount || 0"
                    :max="activePackageInfo?.maxAccountCount">
                    <template #suffix>人</template>
                  </el-input-number>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="form-count !m-t-0 !m-b-0">
                <div class="form-count-label">购买时长</div>
                <div class="form-count-value">
                  <el-input-number
                    v-model="formData.renewalDuration"
                    :min="1"
                    @change="handleCalcPrice">
                    <template #suffix>年</template>
                  </el-input-number>
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="form-count">
            <div class="form-count-label">支付方式</div>
            <div class="form-count-value is-channel-code">
              <div
                class="is-channel-code-li"
                @click="payData.channelCode = 'wx_native'"
                :class="payData.channelCode === 'wx_native' ? 'is-active' : ''"
              >
                <img src="@/assets/imgs/wechatPay.png" />微信支付
              </div>
              <div
                class="is-channel-code-li"
                @click="payData.channelCode = 'alipay_qr'"
                :class="payData.channelCode === 'alipay_qr' ? 'is-active' : ''"
              >
                <img src="@/assets/imgs/aliPay.png" />支付宝支付
              </div>
            </div>
          </div>
          <div class="form-count">
            <div class="form-count-label">支付金额</div>
            <div class="form-count-value is-price" v-loading="calcLoading">
              ￥<font>{{ formatMoney(calcPriceInfo?.totalPrice / 100) }}</font>
            </div>
            <div class="form-count-btn">
              <el-button
                type="primary"
                size="large"
                :disabled="calcLoading"
                @click="handleCreateOrder"
                :loading="createOrderLoading"
                >确定下单
              </el-button>
            </div>
          </div>
        </div>
        <div class="pay" v-else-if="status === 2">
          <div class="pay-price">
            ￥<font>{{ formatMoney(calcPriceInfo?.totalPrice / 100) }}</font>
          </div>
          <div class="pay-qr">
            <canvas ref="canvasRef"></canvas>
          </div>
          <div class="pay-info">
            请使用{{ payData.channelCode === 'wx_native' ? '微信' : '支付宝' }}支付
          </div>
          <el-button type="primary" link class="m-t-2" @click="handlePayBack">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
        <div class="result" v-else-if="status === 3">
          <div class="result-icon">
            <el-icon class="success" v-if="orderPayResult.status === 10"><SuccessFilled /></el-icon>
            <el-icon class="cancel" v-if="orderPayResult.status === 30"><CircleCloseFilled /></el-icon>
          </div>
          <div class="result-label">
            <font v-if="orderPayResult.status === 10">支付成功</font>
            <font v-else-if="orderPayResult.status === 30">支付关闭</font>
          </div>
          <template v-if="orderPayResult.status === 10">
            <div class="result-order">
              <div class="result-order-li">
                <div class="label">订单名称</div>
                <div class="value">{{orderPayResult?.subject}}</div>
              </div>
              <div class="result-order-li">
                <div class="label">订单金额</div>
                <div class="value">{{formatMoney(orderPayResult?.price)}}</div>
              </div>
            </div>
            <el-button type="primary" class="m-t-10" @click="close">
              确定
            </el-button>
          </template>
          <el-button type="primary" link class="m-t-10" @click="handlePayBack" v-if="orderPayResult.status === 30">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, CircleCloseFilled, Close, SuccessFilled } from '@element-plus/icons-vue'
import avatarImg from '@/assets/imgs/avatar.jpg'
import { useUserStore } from '@/store/modules/user'
import * as MainApi from '@/api/tenant'
import { formatMoney } from '../../../utils'
import QRCode from 'qrcode'

const loading = ref(true)
const userStore = useUserStore()
const isShow = ref(false)
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')
const canvasRef = ref()
const status = ref(1)
const getOrderStatusTask = ref()
const list = ref<any[]>([]) // 列表的数据
const allList = ref<any[]>([]) // 列表的数据

const formData = ref<any>({
  newPackageId: null,
  newAccountCount: 0,
  renewalDuration: 1
})
const payQrInfo = ref()
const orderInfo = ref()
const orderPayResult = ref()
const payData = ref<any>({
  id: null,
  channelCode: 'wx_native',
  displayMode: 'url'
})
const activePackageInfo = ref<any>()
const open = async (id: number) => {
  isShow.value = true
  formData.value = {
    newPackageId: id,
    newAccountCount: 0,
    renewalDuration: 1
  }
  payData.value = {
    id: null,
    channelCode: 'wx_native',
    displayMode: 'url'
  }
  orderInfo.value = null
  orderPayResult.value = null
  payQrInfo.value = null
  calcPriceInfo.value = null
  status.value = 1
  list.value = []
  await getTokenPackageDetail()
  await getList()
}

const close = () => {
  isShow.value = false
  if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
}

/**
 * 查询基础套餐列表
 */
const getList = async () => {
  loading.value = true
  try {
    allList.value = await MainApi.getBasicApplicationPage()
    list.value = allList.value.filter((t: any) => t.price > 0)

    let activeItem = list.value.find((t: any) => t.id === formData.value.newPackageId)
    if (activeItem) handlePackage(activeItem)
  } finally {
    loading.value = false
  }
}

/**
 * 计算价格
 */
const calcLoading = ref(false)
const calcPriceInfo = ref()
const handleCalcPrice = async () => {
  calcLoading.value = true
  try {
    calcPriceInfo.value = await MainApi.settlementBasicPackage(formData.value)
  } finally {
    calcLoading.value = false
  }
}

/**
 * 选择套餐
 * @param item
 */
const handlePackage = (item: any) => {
  activePackageInfo.value = item
  formData.value.newPackageId = item.id
  formData.value.newAccountCount = Math.max(tokenPackageDetail.value.accountCount, activePackageInfo.value?.minAccountCount)

  handleCalcPrice()
}

/**
 * 创建订单
 */
const createOrderLoading = ref(false)
const handleCreateOrder = async () => {
  createOrderLoading.value = true
  try {
    orderInfo.value = await MainApi.createBasicPackage(formData.value)
    await handleCreatePay()
  } finally {
    createOrderLoading.value = false
  }
}

/**
 * 发起支付
 */
const handleCreatePay = async () => {
  try {
    payQrInfo.value = await MainApi.payPackageOrderOrder({
      id: orderInfo.value.payOrderId,
      channelCode: payData.value.channelCode,
      displayMode: payData.value.displayMode,
      returnUrl: location.href
    })
    status.value = 2
    await nextTick(() => {
      QRCode.toCanvas(canvasRef.value, payQrInfo.value.displayContent)
      if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
      getOrderStatusTask.value = setInterval(() => {
        getOrderPayResult()
      }, 2000)
    })
  } finally {
  }
}

/**
 * 查询订单支付结果
 */
const getOrderPayResult = async () => {
  try {
    orderPayResult.value = await MainApi.getPayPackageOrderDetail(orderInfo.value.payOrderId)
    if (orderPayResult.value?.status !== 0) {
      status.value = 3
    }
  } finally {
  }
}

/**
 * 返回
 */
const handlePayBack = () => {
  status.value = 1
}

/**
 * 查询当前套餐信息
 */
const tokenPackageDetail = ref<any>()
const getTokenPackageDetail = async () => {
  try {
    loading.value = true
    tokenPackageDetail.value = await MainApi.getPackageDetail()
    if (tokenPackageDetail.value) {
      formData.value.newAccountCount = tokenPackageDetail.value.accountCount
    }
  } finally {
  }
}

const currentPackage = computed(() => {
  let maxType = 0,
    packageId = null
  if (tokenPackageDetail.value && tokenPackageDetail.value.packageList) {
    tokenPackageDetail.value.packageList.forEach((t: any) => {
      if (t.packageType > maxType) {
        maxType = t.packageType
        packageId = t.packageId
      }
    })
  }
  return allList.value.find((t: any) => t.id === packageId)
})

const currentPackageType = computed(() => {
  let maxType = 0
  if (tokenPackageDetail.value && tokenPackageDetail.value.packageList) {
    tokenPackageDetail.value.packageList.forEach((t: any) => {
      if (t.packageType > maxType) maxType = t.packageType
    })
  }
  return maxType
})

watch(() => status.value, () => {
  switch (status.value) {
    case 1:
      payQrInfo.value = null
      orderPayResult.value = null
      if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
      break
    case 2:
      break
    case 3:
      if (getOrderStatusTask.value) clearInterval(getOrderStatusTask.value)
      break
  }
})

defineExpose({
  open
})
</script>

<style scoped lang="scss">
</style>
