<template>
  <div class="square-list">
    <div class="square-list-title">
      <el-button link @click="$router.back()" class=" m-b-2"><el-icon><ArrowLeft/></el-icon>返回</el-button>
    </div>
    <div class="square-list-title">
      <div class="square-list-title-h1">全部分类</div>
      <div class="square-list-title-search">
        <el-input placeholder="搜索应用" v-model="queryParams.name">
          <template #append>
            <el-button :icon="Search" type="primary" @click="getList" />
          </template>
        </el-input>
      </div>
    </div>
    <div class="square-list-main body-center">
      <div class="menu">
        <div class="menu-list">
          <div
            class="menu-list-item"
            @click="queryParams.industry = null"
            :class="!queryParams.industry ? 'is-active' : ''"
            >全部行业
          </div>
          <div
            class="menu-list-item"
            v-for="item in industryList"
            :key="item"
            @click="queryParams.industry = item.value"
            :class="queryParams.industry === item.value ? 'is-active' : ''"
            >{{ item.label }}
          </div>
        </div>
      </div>
      <div class="package" v-loading="loading" v-if="list.length">
        <div
          class="package-li"
          v-for="item in list"
          :key="item"
          @click="push({ name: 'SquareDetail', query: { id: item.id } })"
        >
          <div
            class="package-li-img"
            :style="{ 'background-image': 'url(' + item.effectImage + ')' }"
          ></div>
          <div class="package-li-name is-hover-link">{{ item.name }}</div>
          <div class="package-li-desc">{{ item.summary }}</div>
          <div class="package-li-footer">
            <div class="package-li-footer-tag">
              <el-tag type="info" v-for="t in item.tags" :key="t">{{ t }}</el-tag>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else class="!w-full h-500px" />
    </div>
  </div>
  <LoginPanel ref="loginPanel" />
</template>

<script setup lang="ts">
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import * as MainApi from '@/api/tenant/index'
import { getAccessToken } from '@/utils/auth'
import LoginPanel from '@/views/square/components/login.vue'

const { push } = useRouter() // 路由
const loginPanel = ref()
const industryList = ref([])
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const loading = ref(true)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  industry: null,
  name: ''
})

/** 查询行业 */
const getIndustry = async () => {
  try {
    industryList.value = await MainApi.getIndustryList()
  } finally {
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MainApi.getApplicationPage(queryParams)
    if (data.list) {
      data.list.forEach((item: any) => {
        if (item.tags) item.tags = item.tags.split(',')
      })
    }

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

watch(
  () => queryParams.industry,
  () => {
    getList()
  }
)

onMounted(() => {
  getIndustry()
  getList()
})
</script>

<style scoped lang="scss">
@use '@/styles/square';

.square-list {
  padding-top: 50px;
  border-top: 1px solid var(--el-border-color-light);

  .square-list-title {
    width: 1200px;
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;

    .square-list-title-h1 {
      flex: 1;
      font-weight: bold;
    }

    :deep(.el-input-group__append) {
      margin-left: -45px;

      .el-button {
        background: var(--el-color-primary) !important;
        color: #fff;
      }
    }
  }
}
</style>
