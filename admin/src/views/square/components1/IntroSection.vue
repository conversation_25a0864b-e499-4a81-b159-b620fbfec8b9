<template>
  <div class="intro-area banner-intro bg-sky" style="padding: 50px 0 30px 0;">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-3">
          <div class="intro-area-inner">
            <div class="thumb mb-4">
              <img src="https://abc.xinmiaoxietong.com/img/lunbo/1-06.png" alt="全场景一体化集成" style="width: 64px; height: 64px;"/>
            </div>
            <div class="details text-center">
              <h4 class="fw-bold mb-1">全场景一体化集成</h4>
              <h5 class="mb-3" style="color: #007bff;">打破数据孤岛</h5>
              <p>
                通过底层数据引擎整合企业各业务模块，打破"多系统并行、数据割裂"困局，实现业务流程高效协同与数据闭环流转。全面提升运营效率，增强管理透明度，为企业数字化转型提供坚实支撑。
              </p>
            </div>
          </div>
        </div>
        <div class="col-3">
          <div class="intro-area-inner">
            <div class="thumb mb-4">
              <img src="https://abc.xinmiaoxietong.com/img/lunbo/1-07.png" alt="全流程数据可视化" style="width: 64px; height: 64px;"/>
            </div>
            <div class="details text-center">
              <h4 class="fw-bold mb-1">全流程数据可视化</h4>
              <h5 class="mb-3" style="color: #007bff;">穿透式管理</h5>
              <p>
                支持销售全流程动态监控。通过实时追踪、业务进度、销售转化率、合同到款率等指标，助力决策者快速洞察运营瓶颈，优化资源配置，加速目标达成。
              </p>
            </div>
          </div>
        </div>
        <div class="col-3">
          <div class="intro-area-inner">
            <div class="thumb mb-4">
              <img src="https://abc.xinmiaoxietong.com/img/lunbo/1-08.png" alt="AI深度赋能" style="width: 64px; height: 64px;"/>
            </div>
            <div class="details text-center">
              <h4 class="fw-bold mb-1">AI深度赋能</h4>
              <h5 class="mb-3" style="color: #007bff;">驱动精准决策</h5>
              <ul class="text-start">
                <li>AI外呼+鑫线索：提升外呼率50%以上</li>
                <li>AI分析：销售漏斗实时预警丢单风险并提供策略建议</li>
                <li>自动化报表：分钟级生成多维度业绩分析报表</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="col-3">
          <div class="intro-area-inner">
            <div class="thumb mb-4">
              <img src="https://abc.xinmiaoxietong.com/img/lunbo/1-09.png" alt="灵活部署" style="width: 64px; height: 64px;"/>
            </div>
            <div class="details text-center">
              <h4 class="fw-bold mb-1">灵活部署</h4>
              <h5 class="mb-3" style="color: #007bff;">适配企业级扩展</h5>
              <p>
                支持公有云、私有云、本地化部署，模块化架构可按需扩展（新增ERP或定制风控规则），适配中小型企业到集团级客户。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 使用Emoji图标替代图片
import img1 from '../assets/img/lunbo/1-06.png'
import img2 from '../assets/img/lunbo/1-07.png'
import img3 from '../assets/img/lunbo/1-08.png'
import img4 from '../assets/img/lunbo/1-09.png'
</script>

<style scoped>
.intro-area {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  min-height: 400px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  gap: 20px;
}

.col-3 {
  flex: 1;
  max-width: 280px;
  min-width: 250px;
}

.intro-area-inner {
  background: white;
  padding: 2rem 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  min-height: 320px;
  transition: all 0.3s ease;
}

.intro-area-inner:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
}

.thumb {
  margin-bottom: 1rem;
}

.thumb img {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.details {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.details h4 {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.details h5 {
  font-size: 1em;
  color: #007bff;
  margin-bottom: 1rem;
}

.details p {
  font-size: 0.9em;
  color: #555;
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

.details ul {
  font-size: 0.9em;
  color: #555;
  padding-left: 1.2em;
  margin: 0;
  text-align: left;
  flex: 1;
}

.details ul li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.fw-bold {
  font-weight: bold;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-start {
  text-align: left;
}

.text-center {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .row {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .col-3 {
    flex: 0 0 calc(50% - 10px);
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .col-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .intro-area {
    padding: 30px 0 20px 0 !important;
  }
  
  .intro-area-inner {
    min-height: auto;
  }
}
</style>
