<template>
  <div class="nav-container">
    <el-menu :default-active="activeIndex" class="el-menu-horizontal" mode="horizontal" :ellipsis="false"
      @select="handleSelect">
      <!-- Logo -->
      <el-menu-item index="logo" class="logo-item">
        <img src="https://abc.xinmiaoxietong.com/img/logo.png" alt="鑫淼通讯" class="logo-img" />
      </el-menu-item>

      <!-- 首页 -->
      <el-menu-item index="home">首页</el-menu-item>

      <!-- 产品功能介绍 -->
      <el-sub-menu index="products">
        <template #title>产品功能介绍</template>
        <el-menu-item index="coll">外呼系统</el-menu-item>
        <el-menu-item index="Xinclue">鑫线索</el-menu-item>
        <el-menu-item index="workmobilephone">鑫淼工作手机</el-menu-item>
        <el-menu-item index="OAmanagementsystem">OA管理系统</el-menu-item>
        <el-menu-item index="CRMManagementSystem">CRM管理系统</el-menu-item>
        <el-menu-item index="ERPmanagementsystem">ERP管理系统</el-menu-item>
      </el-sub-menu>

      <!-- 客户案例 -->
      <el-sub-menu index="cases">
        <template #title>客户案例</template>
        <el-menu-item index="aboutus1">关于我们</el-menu-item>
        <el-menu-item index="casestudies">案例研究</el-menu-item>
        <el-menu-item index="Detailedcasestudy">案例研究详情</el-menu-item>
      </el-sub-menu>

      <!-- 关于鑫淼 -->
      <el-menu-item index="about-us">关于鑫淼</el-menu-item>
      <el-menu-item index="square-list">办公广场</el-menu-item>
      <el-menu-item index="price">价格</el-menu-item>

      <!-- 右侧用户信息/登录注册按钮 -->
      <template v-if="userInfo && userInfo.id">
        <el-menu-item class="contact-btn-item" style="border-bottom: none; background: none; cursor: default;">
          <el-button class="work-space-btn" @click="toWorkSpace">工作台</el-button>
          <el-dropdown trigger="click" class="custom-hover">
            <span class="square-head-user-avatar">
              <el-avatar :src="avatar" />
              <span style="margin-left: 10px;">{{ userName }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="toProfile">
                  <i class="el-icon-user"></i> 个人中心
                </el-dropdown-item>
                <el-dropdown-item divided @click="loginOut">
                  <i class="el-icon-switch-button"></i> 退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-menu-item>
      </template>
      <template v-else>
        <el-menu-item class="contact-btn-item" style="border-bottom: none;">
          <el-button link @click="loginRef.openLogin()">登录</el-button>
          <el-button link @click="loginRef.openRegister()">注册</el-button>
        </el-menu-item>
      </template>
    </el-menu>
    <LoginPanel ref="loginRef"/>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, computed } from 'vue'
import LoginPanel from '../components/login.vue'
import avatarImg from "@/assets/imgs/avatar.jpg"
import { useUserStore } from '@/store/modules/user'
import { ElMessageBox } from 'element-plus'
import { useTagsViewStore } from '@/store/modules/tagsView'

const loginRef = ref()
const router = useRouter()
const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()

const activeIndex = ref('home')

const userInfo = computed(() => userStore.user || null)
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')

const handleSelect = (key: string) => {
  activeIndex.value = key
  // 根据菜单项进行路由跳转
  switch (key) {
    case 'home':
      router.push('/home')
      break
    case 'coll':
      router.push('/coll')
      break
    case 'Xinclue':
      router.push('/Xinclue')
      break
    case 'workmobilephone':
      router.push('/workmobilephone')
      break
    case 'OAmanagementsystem':
      router.push('/OAmanagementsystem')
      break
    case 'CRMManagementSystem':
      router.push('/CRMManagementSystem')
      break
    case 'ERPmanagementsystem':
      router.push('/ERPmanagementsystem')
      break
    case 'about-us':
      router.push('/about-us')
      break
    case 'aboutus1':
      router.push('/aboutus1')
      break
    case 'casestudies':
      router.push('/casestudies')
      break
    case 'Detailedcasestudy':
      router.push('/Detailedcasestudy')
      break
    case 'square-list':
      router.push('/square-list')
      break
    case 'price':
      router.push('/square-price')
      break

    case 'logo':
      router.push('/home')
      break
  }
}

const loginOut = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    router.replace('/square-list?redirect=/home')
  } catch {}
}
const toProfile = () => {
  router.push('/user/profile')
}
const toWorkSpace = () => {
  router.push({ name: 'AdminIndex' })
}
</script>
<style scoped>
.nav-container {
  /* max-width: 1200px;
   */
  width: 100%;
   margin: 0 auto;
  padding: 0 20px;
}

/* Element Plus Menu 自定义样式 */
:deep(.el-menu-horizontal) {
  border-bottom: none;
  display: flex;
  align-items: center;
  height: 70px;
  --el-menu-bg-color: white;
  --el-menu-text-color: #333;
  --el-menu-hover-bg-color: #f5f5f5;
}

/* 全局菜单文字颜色重置 */
:deep(.el-menu) {
  color: #333 !important;
  --el-menu-bg-color: white !important;
  --el-menu-text-color: #333 !important;
}

:deep(.el-menu *) {
  color: #333 !important;
}

/* Logo 样式 */
:deep(.logo-item) {
  margin-right: auto;
  border-bottom: none !important;
}

.logo-img {
  height: 45px;
  width: auto;
}

/* 菜单项样式 */
:deep(.el-menu-item) {
  height: 70px;
  line-height: 70px;
  padding: 0 20px;
  font-weight: 500;
  color: #333 !important;
  border-bottom: none !important;
}

:deep(.el-menu-item:hover) {
  background-color: transparent !important;
  color: #333 !important;
}

:deep(.el-menu-item.is-active) {
  background-color: transparent !important;
  color: #333 !important;
  border-bottom: none !important;
}

/* 子菜单样式 */
:deep(.el-sub-menu) {
  height: 70px;
}

:deep(.el-sub-menu__title) {
  height: 70px;
  line-height: 70px;
  padding: 0 20px;
  font-weight: 500;
  color: #333 !important;
  border-bottom: none !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: transparent !important;
  color: #333 !important;
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: #333 !important;
  border-bottom: none !important;
}

:deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
  color: #333 !important;
}

/* 下拉菜单样式 */
:deep(.el-menu--popup) {
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid #e4e7ed;
  background-color: white !important;
}

:deep(.el-menu--popup .el-menu-item) {
  height: 45px;
  line-height: 45px;
  padding: 0 20px;
  color: #333 !important;
  background-color: white !important;
}

:deep(.el-menu--popup .el-menu-item:hover) {
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

/* 强制重置所有子菜单相关的颜色 */
:deep(.el-menu--popup),
:deep(.el-menu--popup *) {
  background-color: white !important;
  color: #333 !important;
}

:deep(.el-menu.el-menu--popup) {
  background-color: white !important;
}

:deep(.el-menu.el-menu--popup .el-menu-item) {
  background-color: white !important;
  color: #333 !important;
}

/* 额外的强制样式重置 */
:deep(.el-popper) {
  background-color: white !important;
}

:deep(.el-popper .el-menu) {
  background-color: white !important;
}

:deep(.el-popper .el-menu .el-menu-item) {
  background-color: white !important;
  color: #333 !important;
}

:deep(.el-sub-menu__popup) {
  background-color: white !important;
}

:deep(.el-sub-menu__popup .el-menu) {
  background-color: white !important;
}

:deep(.el-sub-menu__popup .el-menu-item) {
  background-color: white !important;
  color: #333 !important;
}

/* 联系我们按钮样式 */
.flex-grow {
  flex-grow: 1;
}

:deep(.contact-btn-item) {
  border-bottom: none !important;
  margin-left: 20px;
}

:deep(.contact-btn-item .el-button) {
  background: linear-gradient(45deg, #409eff, #0056b3);
  border: none;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

:deep(.contact-btn-item .el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 10px;
  }

  :deep(.el-menu-horizontal) {
    height: 60px;
  }

  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    height: 60px;
    line-height: 60px;
    padding: 0 15px;
  }

  .logo-img {
    height: 35px;
  }

  :deep(.contact-btn-item) {
    margin-left: 10px;
  }

  :deep(.contact-btn-item .el-button) {
    padding: 8px 16px;
    font-size: 14px;
  }
}
</style>

<!-- 全局样式，专门用来覆盖 Element Plus 的下拉菜单 -->
<style>
/* 强制覆盖所有 Element Plus 下拉菜单样式 */
.el-popper.is-light {
  background: white !important;
  color: #333 !important;
}

.el-popper.is-light .el-menu {
  background: white !important;
}

.el-popper.is-light .el-menu-item {
  background: white !important;
  color: #333 !important;
}

.el-popper.is-light .el-menu-item:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}

.el-menu.el-menu--popup {
  background: white !important;
}

.el-menu.el-menu--popup .el-menu-item {
  background: white !important;
  color: #333 !important;
}

.el-menu.el-menu--popup .el-menu-item:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}

/* 针对所有可能的下拉菜单容器 */
[class*="el-popper"] {
  background: white !important;
}

[class*="el-popper"] .el-menu {
  background: white !important;
}

[class*="el-popper"] .el-menu-item {
  background: white !important;
  color: #333 !important;
}

[class*="el-popper"] .el-menu-item:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}
.square-head-user-avatar{
  display: flex;
  align-items: center;
}
</style>
