<template>
  <div 
    v-show="showBackToTop"
    class="scroll-up"
    @click="scrollToTop"
  >
    <svg class="scroll-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path 
        d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"
        :style="{ strokeDashoffset: circleOffset }"
      />
    </svg>
    <i class="fas fa-arrow-up"></i>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const showBackToTop = ref(false)
const circleOffset = ref(307.919) // 圆周长约为 307.919

const updateScrollProgress = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const docHeight = document.documentElement.scrollHeight - window.innerHeight
  const scrollPercent = scrollTop / docHeight
  
  // 显示/隐藏按钮
  showBackToTop.value = scrollTop > 300
  
  // 更新圆形进度
  const offset = 307.919 - (scrollPercent * 307.919)
  circleOffset.value = offset
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

onMounted(() => {
  window.addEventListener('scroll', updateScrollProgress)
  updateScrollProgress()
})

onUnmounted(() => {
  window.removeEventListener('scroll', updateScrollProgress)
})
</script>

<style scoped>
.scroll-up {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 1;
}

.scroll-up:hover {
  transform: scale(1.1);
}

.scroll-circle {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.scroll-circle path {
  fill: none;
  stroke: #007bff;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-dasharray: 307.919;
  transition: stroke-dashoffset 0.1s ease;
}

.scroll-up i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #007bff;
  font-size: 18px;
}

@media (max-width: 768px) {
  .scroll-up {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
  
  .scroll-up i {
    font-size: 16px;
  }
}
</style>
