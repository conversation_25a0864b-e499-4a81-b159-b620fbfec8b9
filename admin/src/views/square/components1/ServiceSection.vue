<template>
  <div class="testimonial-area bg-sky pd-top-120 pd-bottom-120" :style="{ marginTop: index === 0 ? '0' : '-80px' }">
    <!-- <img class="tm-img-animation-1" src=".https://abc.xinmiaoxietong.com/img/about/10.png" alt="img" /> -->
    <!-- <img class="tm-img-animation-2 top_image_bounce" src=".https://abc.xinmiaoxietong.com/img/about/9.png" alt="img" /> -->
    <div class="container pd-bottom-120">
      <div class="row">
        <div class="col-lg-6">
          <div class="section-title mb-0">
            <h6 class="sub-title wow fadeInUp">// 我们的服务</h6>
            <h2 class="service-title wow fadeInUp" data-wow-delay=".3s">{{ index + 1 }}：{{ service.title }}：</h2>
            <div class="testimonial-slider mt-4">
              <div class="single-testimonial-inner">
                <div class="thumb">
                  <img src="https://abc.xinmiaoxietong.com/img/about/8.png" alt="img" />
                </div>
                <div>
                  <p class="service-desc">{{ service.description }}</p>
                  <h4 class="service-highlight">{{ service.highlight }}</h4>

                </div>
              </div>
            </div>

          </div>
        </div>
        <div class="col-lg-5 offset-lg-1 col-md-8 wow fadeInUp" data-wow-delay=".3s">
          <div class="about-thumb-area pb-0 pe-0 mt-4 mt-lg-0">
            <img class="about-img-1" :src="service.image" alt="img" />
            <!-- <img class="about-img-5 top_image_bounce" src=".https://abc.xinmiaoxietong.com/img/about/5.png" alt="img" /> -->
            <!-- <img class="about-img-6 top_image_bounce" src=".https://abc.xinmiaoxietong.com/img/about/7.png" alt="img" /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  service: {
    title: string;
    description: string;
    highlight: string;
    image: string;
  };
  index: number;
}>();
</script>

<style scoped>

.testimonial-area {
  background: #fff;
    padding-top: 200px;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
}
.row {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
}
.col-lg-6, .col-lg-5 {
  flex: 1 1 0;
  min-width: 320px;
}
.section-title {
  margin-bottom: 0;
}
.sub-title {
  font-size: 1.1rem;
  color: #007bff;
  font-weight: 500;
  margin-bottom: 8px;
}
.service-title {
  font-size: 2rem;
  font-weight: bold;
  color: #222;
  margin-bottom: 16px;
}
.testimonial-slider {
  margin-top: 12px;
}
.single-testimonial-inner {
  display: flex;
  align-items: flex-start;
  background: #f6f8fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24px 20px;
}
.thumb {
  margin-right: 18px;
  flex-shrink: 0;
}
.thumb img {
  width: 56px;
  height: 56px;
  object-fit: cover;
  border-radius: 8px;
}
.service-desc {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.7;
  margin-bottom: 8px;
}
.service-highlight {
  font-size: 1.15rem;
  color: #007bff;
  font-weight: bold;
  margin-top: 4px;
}
.about-thumb-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}
.about-thumb-area img {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  max-width: 220px;
}
@media (max-width: 900px) {
  .testimonial-area {
    padding: 32px 8px 24px 8px;
  }
  .row {
    gap: 16px;
    flex-direction: column;
  }
  .about-thumb-area img {
    max-width: 100%;
  }
}
</style>
