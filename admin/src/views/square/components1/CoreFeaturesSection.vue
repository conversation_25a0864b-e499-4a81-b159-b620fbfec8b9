<template>
  <div class="core-features-section">
    <div class="section-title text-center mb-5">
      <h2 class="title">鑫淼工作手机核心功能</h2>
      <h6 class="subtitle">
        鑫淼工作手机赋能销售高效对接客户、提升签单能力，同时助力企业优化流程、沉淀资源，驱动经营效益与竞争力双向提升。
      </h6>
    </div>

    <!-- 一行三个功能模块 -->
    <div class="container">
      <div class="row g-4 justify-content-center">
        <div 
          v-for="(feature, index) in features"
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <div class="feature-card bg-white p-4 rounded-4 shadow-sm text-center h-100">
            <div class="feature-icon mb-3">
              <img :src="feature.image" :alt="feature.title" />
            </div>
            <h4 class="fw-bold mb-3">{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 行动号召区域 -->
      <div class="cta-section">
        <!-- 背景装饰圆圈 -->
        <div class="bg-circle-1"></div>
        <div class="bg-circle-2"></div>

        <div class="cta-content">
          <h3 class="cta-title">自主研发核心技术，专注企业数智化变革</h3>
          <p class="cta-description">专业团队倾力打造，多项核心技术自主研发</p>
          <p class="cta-subdescription">
            我们拥有多项自主知识产权的核心技术，致力于为企业提供高效、智能的数字化解决方案，助力企业实现数智化转型。
          </p>
        </div>

        <div class="cta-button-wrapper">
          <button 
            class="cta-button"
            @click="handleContact"
            @mouseover="handleButtonHover"
            @mouseout="handleButtonLeave"
          >
            <span class="button-text">立即联系</span>
            <span class="button-shine"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import img1 from '../assets/img/lunbo/内部系统.png'
import img2 from '../assets/img/lunbo/客户系统.png'
import img3 from '../assets/img/lunbo/外部市场系统.png'

interface Feature {
  icon: string
  title: string
  description: string
  image: string
}
const features: Feature[] = [
  {
    icon: '🔗',
    title: '内部生态系统互联',
    description: '内部生态系统互联打通部门数据与流程，消孤岛、促流转，让协作高效、决策敏捷。',
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/%E5%86%85%E9%83%A8%E7%B3%BB%E7%BB%9F.png'
  },
  {
    icon: '🤝',
    title: '客户系统兼容',
    description: '客户系统兼容打通多端数据壁垒，无缝对接需求，让服务响应快、协作顺、体验优。',
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/%E5%AE%A2%E6%88%B7%E7%B3%BB%E7%BB%9F.png'
  },
  {
    icon: '📊',
    title: '市场系统对接',
    description: '市场系统对接整合渠道数据与资源，消壁垒促联动，让营销精准、转化提效、决策有据。',
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/%E5%A4%96%E9%83%A8%E5%B8%82%E5%9C%BA%E7%B3%BB%E7%BB%9F.png'
  }
]

const handleContact = () => {
  window.location.href = 'contact.html'
}

const handleButtonHover = (event: Event) => {
  const button = event.target as HTMLElement
  button.style.transform = 'translateY(-2px)'
  button.style.boxShadow = '0 12px 35px rgba(0,123,255,0.4)'
}

const handleButtonLeave = (event: Event) => {
  const button = event.target as HTMLElement
  button.style.transform = 'translateY(0)'
  button.style.boxShadow = '0 8px 25px rgba(0,123,255,0.3)'
}
</script>

<style scoped>
.core-features-section {
  height: 700px;
  padding: 40px 0;
}

.section-title {
  margin-bottom: 50px;
}

.title {
  font-size: 2.2em;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.subtitle {
  color: #666;
  font-size: 1.1em;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin: 0 -15px;
  margin-bottom: 60px;
  gap: 20px;
}

.col-lg-4,
.col-md-6 {
  padding: 0 15px;
  margin-bottom: 30px;
  flex: 1;
  min-width: 0;
}

.col-lg-4 {
  flex: 1;
  max-width: none;
}

.col-md-6 {
  flex: 1;
  max-width: none;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  text-align: center;
  height: 100%;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
}

.feature-icon .icon-emoji {
  font-size: 4rem;
  display: block;
}

.feature-icon img {
  width: 250px;
  height: 150px;
  object-fit: contain;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon img {
  transform: scale(1.1);
}

.feature-card h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 50px;
  padding: 40px 60px;
  margin: 40px auto;
  max-width: 1000px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.bg-circle-1 {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(138, 43, 226, 0.1));
  border-radius: 50%;
  z-index: 1;
}

.bg-circle-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(0, 123, 255, 0.1));
  border-radius: 50%;
  z-index: 1;
}

.cta-content {
  flex: 1;
  z-index: 2;
}

.cta-title {
  font-size: 1.8em;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #007bff, #8a2be2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-description {
  font-size: 1.1em;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.cta-subdescription {
  font-size: 1em;
  color: #777;
  line-height: 1.6;
  margin: 0;
}

.cta-button-wrapper {
  z-index: 2;
  margin-left: 30px;
}

.cta-button {
  background: linear-gradient(45deg, #007bff, #8a2be2);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 35px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.button-text {
  position: relative;
  z-index: 2;
}

.button-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
  z-index: 1;
}

.cta-button:hover .button-shine {
  left: 100%;
}

@media (max-width: 992px) {
  .row {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .col-lg-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .feature-icon img {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 768px) {
  .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .row {
    gap: 10px;
  }
  
  .cta-section {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
  }
  
  .cta-button-wrapper {
    margin-left: 0;
    margin-top: 20px;
  }
  
  .title {
    font-size: 1.8em;
  }
  
  .cta-title {
    font-size: 1.4em;
  }
  
  .feature-icon img {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 576px) {
  .row {
    gap: 5px;
  }
  
  .col-lg-4,
  .col-md-6 {
    padding: 0 10px;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .feature-icon img {
    width: 70px;
    height: 70px;
  }
}
</style>
