<template>
  <div class="testimonial-area bg-sky pd-top-120 pd-bottom-120" :style="blockStyle">
    <img class="tm-img-animation-1" :src="bgImg1" alt="img" />
    <img class="tm-img-animation-2 top_image_bounce" :src="bgImg2" alt="img" />
    <div class="container pd-bottom-120">
      <div class="row">
        <div class="col-lg-6">
          <div class="section-title mb-0">
            <h6 class="sub-title wow fadeInUp">// {{ subtitle }}</h6>
            <h2 class="title wow fadeInUp" data-wow-delay=".3s">{{ title }}</h2>
            <div class="testimonial-slider mt-4">
              <div class="single-testimonial-inner">
                <div class="thumb">
                  <img :src="thumbImg" alt="img" />
                </div>
                <div class="boxui1">
                  <ul>
                    <li v-for="(item, idx) in features" :key="idx">{{ item }}</li>
                  </ul>
                </div>
                <p class="content" v-if="desc1">{{ desc1 }}</p>
                <h4 v-if="desc2">{{ desc2 }}</h4>
                <p class="content" v-if="desc3">{{ desc3 }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-5 offset-lg-1 col-md-8 wow fadeInUp" data-wow-delay=".3s">
          <div class="about-thumb-area pb-0 pe-0 mt-4 mt-lg-0">
            <img class="about-img-1" :src="mainImg" alt="img" />
            <img class="about-img-5 top_image_bounce" :src="sideImg1" alt="img" />
            <img class="about-img-6 top_image_bounce" :src="sideImg2" alt="img" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps({
  subtitle: String,
  title: String,
  features: Array,
  desc1: String,
  desc2: String,
  desc3: String,
  thumbImg: String,
  mainImg: String,
  sideImg1: String,
  sideImg2: String,
  bgImg1: { type: String, default: () => 'https://abc.xinmiaoxietong.com/img/about/10.png' },
  bgImg2: { type: String, default: () => 'https://abc.xinmiaoxietong.com/img/about/9.png' },
  blockStyle: { type: String, default: '' }
})
</script>
