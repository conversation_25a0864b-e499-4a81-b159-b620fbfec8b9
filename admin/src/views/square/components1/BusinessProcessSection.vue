<template>
  <div class="business-process-section">
    <div class="container">
      <div class="section-title text-center mb-5">
        <h2 class="title">只有把控好每一个业务过程</h2>
        <h2 class="title">才能轻松收获目标结果</h2>
      </div>

      <!-- 业务流程按钮 -->
      <div class="business-tabs">
        <button 
          v-for="(process, index) in businessProcesses"
          :key="index"
          class="business-tab-btn"
          :class="{ active: currentProcess === index }"
          @click="switchProcess(index)"
        >
          {{ process.title }}
        </button>
      </div>

      <!-- 动态内容区域 -->
      <div 
        class="business-content-wrapper"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div class="business-content-left">
          <h3>{{ currentProcessData.subtitle }}</h3>
          <div class="business-description">
            <div 
              v-for="(point, index) in currentProcessData.points"
              :key="index"
              class="business-point"
            >
              <span class="tab-bullet">•</span>
              <span>{{ point }}</span>
            </div>
          </div>
          <br /><br />
          <button 
            class="contact-btn"
            @click="goToContact"
          >
            联系我们
          </button>
        </div>
        <div class="business-content-right">
          <img 
            :src="currentProcessData.image" 
            :alt="currentProcessData.title"
          />
          <!-- 轮播控制按钮 -->
          <div class="carousel-controls">
            <button class="carousel-btn prev-btn" @click="prevProcess">‹</button>
            <button class="carousel-btn next-btn" @click="nextProcess">›</button>
          </div>
          <!-- 轮播指示器 -->
          <div class="carousel-indicators">
            <span 
              v-for="(_, index) in businessProcesses"
              :key="index"
              class="indicator"
              :class="{ active: currentProcess === index }"
              @click="switchProcess(index)"
            ></span>
          </div>
          
          <!-- 自动播放状态指示 -->
          <div class="auto-play-indicator" v-if="isHovered">
            <span>⏸️ 已暂停自动播放</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'


interface BusinessProcess {
  title: string
  subtitle: string
  points: string[]
  image: string
}

const currentProcess = ref(0)
let autoPlayTimer: NodeJS.Timeout | null = null
const isHovered = ref(false)

// 自动轮播间隔（毫秒）
const AUTO_PLAY_INTERVAL = 15000

const businessProcesses: BusinessProcess[] = [
  {
    title: '线索转化可控',
    subtitle: '全渠道线索统一接入 | 商机转化率 | 客户转化率',
    points: [
      '万级海量客资线索客户自动导入CRM系统，方便销售进一步对接',
      '销售线索在经过筛选和初步接洽，获取到更进一步信息后，便转化为客户，客户转化率越高商机的获取将越多',
      '商机即销售机会，是目标实现的重要一步，中小微企业更应重视销售过程中的商机获取量以及商机转化率'
    ],
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/线索转化.png'
  },
  {
    title: '客户交易可控',
    subtitle: '交易流程管理 | 订单跟踪 | 成交转化',
    points: [
      '客户沟通过程、交易的凭证、合同、订单记录，是客户资产中重要的环节，也是企业信息化的体现之一',
      ' 从销售合同的建立到应收款、回款，再到发票的开具，为中小微企业提供了轻松实现流程化管理的工具，让企业用更多的时间从事转化和服务客户',
      '个性化的流程设计器可以让处于不同阶段的企业实现在交易环节中的审批、知晓等管理动作'
    ],
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/客户交易.png'
  },
  {
    title: '客户服务可控',
    subtitle: '服务质量监控 | 客户满意度 | 售后跟进',
    points: [
      '7*12小时在线售后服务，为客户提供最全最及时的售后服务',
      ' 需求信息与客户资料、联系人、订单情况等信息相互关联，同步相关服务岗，让客户像购物一样对售后服务处理的每一个环节和进度清晰可见',
      '通过权限控制敏感信息的读取，让跨部门协作轻松实现，无需担心再服务过程中暴露客户核心信息'
    ],
    image:  'https://abc.xinmiaoxietong.com/img/lunbo/客户服务.png'
  },
  {
    title: '工作任务可控',
    subtitle: '任务分配 | 进度跟踪 | 效率统计',
    points: [
      ' 日程待办、流程任务、日常任务相聚合',
      '为企业经营过程中的任务通过不同的需求类型、模版进行设计，将口头吩咐变为需求发布，轻松实现让每一件事都有闭环，过程可追溯，大幅提高工作效率',
      ' 为不同事件设计不同的流程，在流程节点中设置审核等批复工具'
    ],
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/工作任务.png'
  },
  {
    title: '数据汇总',
    subtitle: '数据统计 | 报表分析 | 决策支持',
    points: [
      '让中小微企业轻松实现和驾驭的多维度可视化数据驾驶舱',
      ' 数字化驱动业务增长是大势所趋，也是企业核心竞争力的转变，通过数据不仅能找到在销售过程中的不足，更应该能看到产品、服务的趋势和营销ROI',
      ' 翼企办持续、不断的为中小微企业输出更多维度降本增效的解决方案，支持企业定制不同维度的数据分析及呈现方式'
    ],
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/数据汇总.png'
  }
]

const currentProcessData = computed(() => businessProcesses[currentProcess.value])

// 开始自动播放
const startAutoPlay = () => {
  if (autoPlayTimer) clearInterval(autoPlayTimer)
  autoPlayTimer = setInterval(() => {
    if (!isHovered.value) {
      nextProcess()
    }
  }, AUTO_PLAY_INTERVAL)
}

// 停止自动播放
const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 下一个流程
const nextProcess = () => {
  currentProcess.value = (currentProcess.value + 1) % businessProcesses.length
}

// 上一个流程
const prevProcess = () => {
  currentProcess.value = currentProcess.value === 0 
    ? businessProcesses.length - 1 
    : currentProcess.value - 1
}

const switchProcess = (index: number) => {
  currentProcess.value = index
  // 用户手动切换时重新开始自动播放
  startAutoPlay()
}

// 鼠标悬停处理
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 生命周期
onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})

const goToContact = () => {
  window.location.href = 'contact.html'
}
</script>

<style scoped>
.business-process-section {
  min-height: 800px;
  padding: 60px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  margin-bottom: 50px;
}

.title {
  font-size: 2.2em;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.business-tabs {
  text-align: center;
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.business-tab-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.2);
}

.business-tab-btn.active {
  background: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.business-tab-btn:not(.active) {
  background: white;
  color: #007bff;
}

.business-tab-btn:hover:not(.active) {
  background: rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.business-content-wrapper {
  display: flex;
  gap: 50px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
}

.business-content-left {
  flex: 2;
  padding-right: 30px;
  color: #333;
  transition: all 0.5s ease;
}

.business-content-left h3 {
  font-size: 1.9em;
  color: #333;
  margin-bottom: 25px;
  font-weight: bold;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.business-description {
  margin-bottom: 35px;
  font-size: 16px;
  line-height: 1.7;
  color: #666;
}

.business-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;
  line-height: 1.7;
  font-size: 15px;
}

.business-point span:last-child {
  flex: 1;
  word-wrap: break-word;
  hyphens: auto;
}

.tab-bullet {
  color: #007bff;
  margin-right: 10px;
  font-size: 1.2em;
  font-weight: bold;
  margin-top: 2px;
}

.contact-btn {
  padding: 12px 30px;
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.business-content-right {
  flex: 2;
  max-width: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.business-content-right img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
  opacity: 1;
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.business-content-right img:hover {
  transform: scale(1.02);
}

/* 轮播控制按钮 */
.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.carousel-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 123, 255, 0.8);
  color: white;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  pointer-events: auto;
  opacity: 0;
}

.business-content-wrapper:hover .carousel-btn {
  opacity: 1;
}

.carousel-btn:hover {
  background: rgba(0, 123, 255, 1);
  transform: scale(1.1);
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #007bff;
  transform: scale(1.2);
}

.indicator:hover {
  background: rgba(0, 123, 255, 0.8);
}

/* 自动播放状态指示 */
.auto-play-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  opacity: 0;
  animation: fadeInIndicator 0.3s ease-in-out forwards;
}

@keyframes fadeInIndicator {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .business-content-wrapper {
    flex-direction: column;
    gap: 30px;
  }
  
  .business-content-left {
    padding-right: 0;
    text-align: center;
  }
  
  .business-tabs {
    flex-direction: column;
    align-items: center;
  }
  
  .business-tab-btn {
    width: 200px;
  }
  
  .title {
    font-size: 1.8em;
  }
}
</style>
