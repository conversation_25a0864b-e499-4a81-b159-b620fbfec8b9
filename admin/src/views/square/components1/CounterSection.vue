<template>
  <div class="counter-area bg-sky pd-top-110 pd-bottom-60">
    <div class="container">
      <div class="counter-area-inner">
        <h2 class="overlay-title">鑫淼通讯</h2>
        <div class="row">
          <div 
            v-for="(counter, index) in counters"
            :key="index"
            class="col-lg-3 col-md-6 counter-item"
            :data-wow-delay="`${index * 0.1}s`"
          >
            <div class="single-counter-inner text-center">
              <h2>
                <span 
                  class="counter"
                  :data-target="counter.number"
                >
                  {{ displayCounters[index] }}
                </span>
                {{ counter.suffix }}
              </h2>
              <h6>{{ counter.title }}</h6>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Counter {
  number: number
  suffix: string
  title: string
}

const counters: Counter[] = [
  { number: 8000, suffix: ' +', title: '项目成功完成' },
  { number: 7500, suffix: '+', title: '满意客户' },
  { number: 60000, suffix: ' +', title: '可检索目标' },
  { number: 7500, suffix: ' +', title: '收获好评' }
]

const displayCounters = ref(counters.map(() => 0))

const animateCounters = () => {
  counters.forEach((counter, index) => {
    let current = 0
    const increment = counter.number / 100
    const timer = setInterval(() => {
      current += increment
      if (current >= counter.number) {
        displayCounters.value[index] = counter.number
        clearInterval(timer)
      } else {
        displayCounters.value[index] = Math.floor(current)
      }
    }, 20)
  })
}

onMounted(() => {
  // 使用 Intersection Observer 在元素进入视口时开始动画
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          animateCounters()
          observer.disconnect()
        }
      })
    },
    { threshold: 0.5 }
  )

  const counterElement = document.querySelector('.counter-area')
  if (counterElement) {
    observer.observe(counterElement)
  }
})
</script>

<style scoped>
.counter-area {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  margin-top: 40px;
  position: relative;
  height: 150px;
  display: flex;
  align-items: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.counter-area-inner {
  position: relative;
}

.overlay-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 5rem;
  font-weight: bold;
  color: rgba(0, 4, 8, 0.05);
  z-index: 1;
  white-space: nowrap;
}

.row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin: 0 -15px;
  position: relative;
  z-index: 2;
  gap: 20px;
}

.col-lg-3,
.col-md-6 {
  padding: 0 15px;
  margin-bottom: 0;
  flex: 1;
  min-width: 0;
}

.col-lg-3 {
  flex: 1;
  max-width: none;
}

.col-md-6 {
  flex: 1;
  max-width: none;
}

.counter-item {
  animation: fadeInUp 1.5s ease;
}

.single-counter-inner {
  text-align: center;
  padding: 10px;
}

.single-counter-inner h2 {
  font-size: 2rem;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 5px;
}

.counter {
  display: inline-block;
}

.single-counter-inner h6 {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 992px) {
  .row {
    gap: 15px;
  }
  
  .overlay-title {
    font-size: 2.5rem;
    font-weight: bold;
  }
  
  .single-counter-inner h2 {
    font-size: 1.8rem;
  }
  
  .single-counter-inner h6 {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .row {
    gap: 10px;
  }
  
  .col-lg-3,
  .col-md-6 {
    padding: 0 8px;
  }
  
  .overlay-title {
    font-size: 2rem;
    font-weight: bold;
  }
  
  .single-counter-inner {
    padding: 8px 5px;
  }
  
  .single-counter-inner h2 {
    font-size: 1.5rem;
  }
  
  .single-counter-inner h6 {
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .row {
    gap: 5px;
  }
  
  .col-lg-3,
  .col-md-6 {
    padding: 0 5px;
  }
  
  .single-counter-inner {
    padding: 5px 2px;
  }
  
  .single-counter-inner h2 {
    font-size: 1.2rem;
  }
  
  .single-counter-inner h6 {
    font-size: 0.6rem;
  }
}
</style>
