<template>

  <div class="carousel-section">
    <div class="container">
      <div class="carousel-container">
        <!-- 动态文字内容区域 -->
        <div class="text-content">
          <div class="main-title">鑫淼为您提供更优解</div>
          <div class="description">
            通过8大集成系统帮助保险、房产、生产制造业、服务业等10000+销售型企业完成业务增长，
            先后与中国电信、华为、OPPO、小米等达成战略合作。
          </div>
        </div>

        <!-- 文字按钮栏 -->
        <div class="text-tabs">
          <div v-for="(tab, index) in tabs" :key="index" class="text-tab" :class="{ active: currentSlide === index }"
            @click="goToSlide(index)">
            <span class="tab-bullet">•</span>
            <span class="tab-text">{{ tab.title }}</span>
          </div>
        </div>

        <!-- 轮播图内容 -->
        <div class="carousel-content">
          <div class="carousel-wrapper">
            <div v-for="(slide, index) in slideDescriptions" :key="index">
              <p v-show="currentSlide === index" class="carousel-extra-text-1">
                {{ slide.line1 }}
              </p>
              <p v-show="currentSlide === index" class="carousel-extra-text-2">
                {{ slide.line2 }}
              </p>
            </div>

            <div class="carousel-track-container">
              <div class="carousel-track" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                <div v-for="(slide, index) in slides" :key="index" class="carousel-slide">
                  <img :src="slide.image" :alt="slide.alt" @click="slide.onClick" @mouseover="handleMouseOver"
                    @mouseout="handleMouseOut" />
                </div>
              </div>
            </div>

            <!-- 导航按钮 -->
            <button class="carousel-nav prev" @click="prevSlide">‹</button>
            <button class="carousel-nav next" @click="nextSlide">›</button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'


interface Tab {
  title: string
}

interface Slide {
  image: string
  alt: string
  onClick?: () => void
}

interface SlideDescription {
  line1: string
  line2: string
}

const currentSlide = ref(0)

const tabs: Tab[] = [
  { title: '工作手机' },
  { title: 'OA系统' },
  { title: 'CRM系统' },
  { title: 'ERP系统' },
  { title: '鑫线索' },
  { title: '外呼系统' }
]

const slides: Slide[] = [
  { image: 'https://abc.xinmiaoxietong.com/img/lunbo/1.png', alt: '工作手机' },
  { image: 'https://abc.xinmiaoxietong.com/img/lunbo/2.png', alt: 'OA系统' },
  { image: 'https://abc.xinmiaoxietong.com/img/lunbo/3.png', alt: 'CRM系统' },
  { image: 'https://abc.xinmiaoxietong.com/img/lunbo/4.png', alt: 'ERP系统' },
  {
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/5.png',
    alt: '鑫线索',
    onClick: () => window.location.href = '/service1.html'
  },
  {
    image: 'https://abc.xinmiaoxietong.com/img/lunbo/6.png',
    alt: '外呼系统',
    onClick: () => window.location.href = '/service.html'
  }
]

const slideDescriptions: SlideDescription[] = [
  { line1: '针对销售全流程业务管理，打造以销售为本，透明化，数字化', line2: '一体化行业解决方案，为销售赋能，助力企业业绩提升提供全方位解决方案' },
  { line1: '为企业提供数字化的管理流程，满足企业多元化办公需求，', line2: '助力掌控全局，决策零延迟，降本增效一步落地' },
  { line1: '对企业客户进行精细化运营管理，通过防流失、提客价、控团队、', line2: '盯回款抓牢销售命脉，客户变印钞机，商机一个不漏' },
  { line1: '为企业砍库存、保交付、堵漏洞、节成本，资金周转快如闪电，', line2: '助力企业从销售、采购、库存、供应链等多方面实现线上线下一体化管理' },
  { line1: '助力企业实现数智化转型，让找客户变的简单，助力获客效率50倍提升，', line2: '快速搭建你的专属商城，支持多平台同步管理，让你的生意无处不在' },
  { line1: '从拓客到客户管理到销售一体化过程管理，', line2: '有效解决销售外呼管理难的问题' }
]

let autoSlideInterval: ReturnType<typeof setInterval> | null = null

const goToSlide = (index: number) => {
  currentSlide.value = index
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? slides.length - 1 : currentSlide.value - 1
}

const startAutoSlide = () => {
  autoSlideInterval = setInterval(nextSlide, 15000)
}

const stopAutoSlide = () => {
  if (autoSlideInterval) {
    clearInterval(autoSlideInterval)
    autoSlideInterval = null
  }
}

const handleMouseOver = (event: Event) => {
  const target = event.target as HTMLElement
  target.style.transform = 'scale(1.02)'
  stopAutoSlide()
}

const handleMouseOut = (event: Event) => {
  const target = event.target as HTMLElement
  target.style.transform = 'scale(1)'
  startAutoSlide()
}

onMounted(() => {
  startAutoSlide()
})

onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
.carousel-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  padding: 60px 0;
  height: 1000px;
}

.carousel-container {
  width: 100%;
  margin: 0 auto;
}

.text-content {
  color: #222;
  text-align: center;
  margin-bottom: 24px;
  padding-top: 40px;
}

.main-title {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 12px;
}

.text-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  color: #222;
}

.text-tab {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 15px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.text-tab:hover,
.text-tab.active {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.tab-bullet {
  margin-right: 8px;
  font-size: 1.2em;
}

.carousel-content {
  position: relative;
  height: 600px;
}

.carousel-extra-text-1,
.carousel-extra-text-2 {
  text-align: center;
  margin: 10px 0;
  font-size: 1.1em;
}

.carousel-extra-text-1 {
  color: #333;
}

.carousel-extra-text-2 {
  color: #666;
}

.carousel-track-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
}

.carousel-slide {
  min-width: 100%;
  height: 510px;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-nav:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
  left: 20px;
}

.carousel-nav.next {
  right: 20px;
}

@media (max-width: 768px) {
  .carousel-container {
    width: 95%;
  }

  .text-tabs {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .carousel-slide {
    height: 250px;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}
</style>
