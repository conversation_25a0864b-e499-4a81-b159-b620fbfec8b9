<template>
  <footer class="footer">
    <div class="container">
      <div class="pd-top-60 pd-bottom-60">
        <div class="row g-4">
          <div class="col-xl-3 col-md-6 footer-column wow fadeInUp" data-wow-duration="1.2s" data-wow-delay=".2s">
            <div class="footer__item">
              <div class="footer__item-title">
                <h4>关于鑫淼</h4>
                <span class="footer__item-title-line"></span>
                <span class="footer__item-title-line2"></span>
              </div>
              <p>
                通过8大集成系统帮助保险、房产、生产制造业、服务业等100+销售型企业完成业务增长，
                先后与中国电信、华为、OPPO、小米等达成战略合作。
              </p>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 footer-column wow fadeInUp" data-wow-duration="1.4s" data-wow-delay=".4s">
            <div class="footer__item">
              <div class="footer__item-title">
                <h4>产品服务</h4>
                <span class="footer__item-title-line"></span>
                <span class="footer__item-title-line2"></span>
              </div>
              <ul>
                <li class="pb-4"><a href="/service.html">外呼系统</a></li>
                <li class="pb-4"><a href="/service1.html">鑫线索</a></li>
                <li class="pb-4"><a href="/service2.html">鑫淼工作手机</a></li>
                <li class="pb-4"><a href="/service3.html">OA管理系统</a></li>
                <li class="pb-4"><a href="/service4.html">CRM管理系统</a></li>
                <li class="pb-4"><a href="/service5.html">ERP管理系统</a></li>
              </ul>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 footer-column wow fadeInUp" data-wow-duration="1.6s" data-wow-delay=".6s">
            <div class="footer__item">
              <div class="footer__item-title">
                <h4>联系我们</h4>
                <span class="footer__item-title-line"></span>
                <span class="footer__item-title-line2"></span>
              </div>
              <ul>
                <li class="pb-3">
                  <a href="#">公司地址：宁波鄞州区中兴路138号明晨大厦12楼1203室</a>
                </li>
                <li class="pb-3">
                  <a href="mailto:<EMAIL>">邮箱：<EMAIL></a>
                </li>
              </ul>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 footer-column wow fadeInUp" data-wow-duration="1.8s" data-wow-delay=".8s">
            <div class="footer__item">
              <div class="footer__item-title">
                <h4>联系方式</h4>
                <span class="footer__item-title-line"></span>
                <span class="footer__item-title-line2"></span>
              </div>
              <ul class="footer__item-blog">
                <li class="pb-3">
                  <img src="https://abc.xinmiaoxietong.com/img/footer/01.png" alt="鑫淼联系人" />
                  <div class="ms-3">
                    <h4><a href="#0">鑫淼联系人</a></h4>
                  </div>
                </li>
                <li>
                  <img src="https://abc.xinmiaoxietong.com/img/footer/02.png" alt="鑫淼公众号" />
                  <div class="ms-3">
                    <h4><a href="#0">鑫淼公众号</a></h4>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer__copyright">
      <p>
        &copy;
        <a href="https://beian.miit.gov.cn/#/Integrated/index">浙ICP备2022029204号-1</a>
        浙江鑫淼通讯有限责任公司 版权所有
      </p>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// 简单的动画观察器
onMounted(() => {
  const observeElements = () => {
    const elements = document.querySelectorAll('.wow')

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement
            element.style.visibility = 'visible'
            element.style.animationDelay = element.dataset.wowDelay || '0s'
            element.style.animationDuration = element.dataset.wowDuration || '1s'
            element.classList.add('animate')
            observer.unobserve(element)
          }
        })
      },
      { threshold: 0.1 }
    )

    elements.forEach((element) => observer.observe(element))
  }

  // 延迟执行以确保DOM已完全渲染
  setTimeout(observeElements, 100)
})
</script>

<style scoped>
.footer {
  background: #ffffff;
  color: rgb(0, 0, 0);
  margin-top: 200px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.pd-top-60 {
  padding-top: 60px;
}

.pd-bottom-60 {
  padding-bottom: 60px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.footer-column {
  padding: 0 15px;
  margin-bottom: 30px;
}

.col-xl-3,
.col-md-6 {
  flex: 0 0 25%;
  max-width: 25%;
}

.footer__item-title {
  position: relative;
  margin-bottom: 25px;
}

.footer__item-title h4 {
  color: black;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.footer__item-title-line,
.footer__item-title-line2 {
  display: block;
  height: 2px;
  background: #007bff;
  margin-bottom: 5px;
}

.footer__item-title-line {
  width: 40px;
}

.footer__item-title-line2 {
  width: 20px;
}

.footer__item p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.footer__item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__item ul li {
  margin-bottom: 12px;
}

.footer__item ul li.pb-3 {
  padding-bottom: 12px;
}

.footer__item ul li.pb-4 {
  padding-bottom: 16px;
}

.footer__item ul li a {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
  line-height: 1.6;
}

.footer__item ul li a:hover {
  color: #007bff;
}

/* 联系方式区域样式 */
.footer__item-blog {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__item-blog li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.footer__item-blog li img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  transition: transform 0.3s ease;
  background: white;
  padding: 2px;
}

.footer__item-blog li:hover img {
  transform: scale(1.15);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

.footer__item-blog .ms-3 {
  margin-left: 15px;
}

.footer__item-blog h4 {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}

.footer__item-blog h4 a {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer__item-blog h4 a:hover {
  color: #007bff;
}

/* 动画效果 */
.wow {
  visibility: hidden;
  animation-fill-mode: both;
}

.wow.animate {
  visibility: visible;
}

.fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.footer__item-map-popup {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #007bff;
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer__item-map-popup:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.footer__copyright {
  background: linear-gradient(135deg, #1a365d 0%, #1e40af 100%);
  text-align: center;
  padding: 20px 0;
}

.footer__copyright p {
  margin: 0;
  color: white;
}

.footer__copyright a {
  color: #007bff;
  text-decoration: none;
}

.footer__copyright a:hover {
  text-decoration: underline;
}

@media (max-width: 992px) {
  .col-xl-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {

  .col-xl-3,
  .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .footer {
    margin-top: 100px;
  }

  .footer__item-blog li {
    justify-content: center;
    text-align: center;
  }

  .footer__item-blog .ms-3 {
    margin-left: 10px;
  }

  .footer__item-blog li img {
    width: 70px;
    height: 70px;
  }

  .wow {
    animation-duration: 0.8s !important;
    animation-delay: 0.2s !important;
  }
}
</style>
