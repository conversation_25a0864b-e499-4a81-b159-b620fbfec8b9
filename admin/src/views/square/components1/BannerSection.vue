<template>
  <section class="banner-three">
    <el-carousel :interval="5000" height="500px" indicator-position="outside" arrow="hover" :autoplay="true"
      :loop="true" trigger="click">
      <el-carousel-item v-for="(slide, index) in slides" :key="index">
        <div class="banner-slide">
          <img :src="slide.image" :alt="slide.alt" />
          <div class="slide-content">
            <!-- <h1 v-if="slide.title" class="slide-title">{{ slide.title }}</h1> -->
            <!-- <p v-if="slide.description" class="slide-description">{{ slide.description }}</p> -->
            <button v-if="slide.buttonText" class="slide-button" @click="slide.onClick">
              {{ slide.buttonText }}
            </button>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    
    <!-- 二维码弹框 -->
    <div v-if="showQrModal" class="qr-modal" @click="closeModal">
      <div class="qr-modal-content" @click.stop>
        <!-- 关闭按钮 -->
        <button class="close-modal" @click="closeModal">×</button>
        
        <!-- 弹窗内容 -->
        <h3 class="modal-title">扫码免费试用</h3>
        
        <p class="modal-description">
          扫描下方二维码，联系我们，立即体验
        </p>
        
        <!-- 二维码图片 -->
        <div class="qr-code-container">
          <img src="https://abc.xinmiaoxietong.com/img/footer/01.png" alt="免费试用二维码" class="qr-code-image"/>
        </div>
        
        <p class="modal-features">
          • 专业客服一对一指导<br/>
        </p>
        
        <div class="contact-info">
          客服热线：13605743366
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// 正确导入图片 - 使用绝对路径从src开始


interface Slide {
  image: string
  alt: string
  title?: string
  description?: string
  buttonText?: string
  onClick?: () => void
}

// 控制二维码弹框显示
const showQrModal = ref(false)

// 打开二维码弹框
const openQrModal = () => {
  showQrModal.value = true
}

// 关闭二维码弹框
const closeModal = () => {
  showQrModal.value = false
}

const slides: Slide[] = [
  {
    image: 'https://abc.xinmiaoxietong.com/img/banner/1.png',
    alt: '鑫淼通讯主页',
    title: '专业的企业数字化解决方案',
    description: '通过8大集成系统帮助企业完成业务增长',
    buttonText: '联系我们',
    onClick: openQrModal
  },
  {
    image:  'https://abc.xinmiaoxietong.com/img/banner/2.png',
    alt: '工作手机解决方案',
    title: '智能工作手机系统',
    description: '提升销售效率，管控客户资源',
    buttonText: '联系我们',
    onClick: openQrModal
  }
]
</script>

<style scoped>
.banner-three {
  margin: 0;
  padding: 0;
  position: relative;
  border: none;
  outline: none;
}

/* Element Plus轮播图容器 */
:deep(.el-carousel) {
  height: 500px;
  margin: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

:deep(.el-carousel__container) {
  height: 500px;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

.banner-slide {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.banner-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-content {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  color: white;
  z-index: 2;
  max-width: 500px;
  margin-top: 150px;
}

.slide-title {
  font-size: 3em;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.slide-description {
  font-size: 1.2em;
  margin-bottom: 30px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.slide-button {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.slide-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* 自定义Element Plus轮播图样式 */
:deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(255, 255, 255, 1);
  transform: scale(1.05);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.08);
}

:deep(.el-carousel__arrow--left) {
  left: 30px;
}

:deep(.el-carousel__arrow--right) {
  right: 30px;
}

:deep(.el-carousel__indicators) {
  bottom: 20px;
}

:deep(.el-carousel__indicator) {
  padding: 12px 4px;
}

:deep(.el-carousel__button) {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid transparent;
}

:deep(.el-carousel__indicator.is-active .el-carousel__button) {
  background-color: #007bff;
  border-color: rgba(255, 255, 255, 0.8);
}

@media (max-width: 768px) {
  :deep(.el-carousel) {
    height: 400px;
  }

  :deep(.el-carousel__container) {
    height: 400px;
  }

  .slide-content {
    left: 5%;
    right: 5%;
    max-width: none;
    text-align: center;
  }

  .slide-title {
    font-size: 2em;
  }

  .slide-description {
    font-size: 1em;
  }

  :deep(.el-carousel__arrow) {
    width: 50px;
    height: 50px;
  }

  :deep(.el-carousel__arrow--left) {
    left: 15px;
  }

  :deep(.el-carousel__arrow--right) {
    right: 15px;
  }
}

/* 二维码弹框样式 */
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-modal-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: #333;
}

.modal-title {
  color: #333;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #007bff, #8a2be2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-description {
  color: #666;
  margin-bottom: 25px;
  font-size: 0.95em;
}

.qr-code-container {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  display: inline-block;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 10px;
  display: block;
}

.modal-features {
  color: #555;
  font-size: 0.9em;
  line-height: 1.5;
  margin-bottom: 15px;
}

.contact-info {
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(138, 43, 226, 0.1));
  border-radius: 10px;
  padding: 12px;
  font-size: 0.85em;
  color: #666;
}
</style>
