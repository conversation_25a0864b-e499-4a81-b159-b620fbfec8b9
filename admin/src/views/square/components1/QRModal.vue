<template>
   <div id="business-process-section">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2 class="title">只有把控好每一个业务过程</h2>
                <h2 class="title">才能轻松收获目标结果</h2>
            </div>
            <div class="images-container">
                <img src="https://abc.xinmiaoxietong.com/img/lunbo/A_1%2C%E6%95%B0%E6%8D%AE%E7%95%99%E5%AD%98.png" alt="数据留存"/>
                <img src="https://abc.xinmiaoxietong.com/img/lunbo/A_2%EF%BC%8C%E4%BF%9D%E6%8A%A4%E5%AE%A2%E6%88%B7%E6%95%B0%E6%8D%AE.png" alt="保护客户数据"/>
                <img src="https://abc.xinmiaoxietong.com/img/lunbo/A_3%EF%BC%8C%E9%94%80%E5%94%AE%E8%AE%B0%E5%BD%95.png" alt="销售记录"/>
                <img src="https://abc.xinmiaoxietong.com/img/lunbo/A_4%EF%BC%8C%E5%AE%9E%E6%97%B6%E7%9B%91%E7%AE%A1.png" alt="实时监管"/>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
#business-process-section {
  padding: 60px 0;
  min-height: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  /* padding: 0 20px; */

}



.title {
  font-size: 2.2em;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.images-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.images-container img {
  width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .title {
    font-size: 1.8em;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .images-container {
    gap: 15px;
  }
}
</style>
