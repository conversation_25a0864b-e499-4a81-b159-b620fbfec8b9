<template>
  df
</template>
<script lang="ts" setup>
import * as TenantApi from '@/api/tenant/index'
const {query} = useRoute() // 查询参数
const token = query?.token

const loading = ref(false)
const tenantId = ref()

const getDetail = async () => {
  loading.value = true
  try {
    tenantId.value = await TenantApi.getIdByToken()
  } finally {
    loading.value = false
  }
}

 console.log("xxxxxx", token)
</script>

<style lang="scss" scoped>
</style>
