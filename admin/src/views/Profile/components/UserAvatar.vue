<template>
  <div class="change-avatar">
    <CropperAvatar
        ref="cropperRef"
        :btnProps="{ preIcon: 'ant-design:cloud-upload-outlined' }"
        :showBtn="false"
        :value="avatar"
        width="120px"
        @change="handelUpload"
        :path="ExtraPathEnum.TEAMIFY"
    />
  </div>
</template>
<script lang="ts" setup>
import {propTypes} from '@/utils/propTypes'
import {uploadAvatar} from '@/api/system/user/profile'
import {CropperAvatar} from '@/components/Cropper'
import {useUserStore} from '@/store/modules/user'
import {ExtraPathEnum} from "@/components/UploadFile/src/extraPathEnum";


defineOptions({name: 'UserAvatar'})

let props = defineProps({
  img: propTypes.string.def('')
})

const avatar = ref(props.img)
const userStore = useUserStore()

const cropperRef = ref()
const handelUpload = async (data) => {
  const res = await uploadAvatar({avatar: data})
  cropperRef.value.close()

  userStore.setUserAvatarAction(data)
  avatar.value = data
}

watch(() => props.img, function (){
  avatar.value = props.img
})
</script>

<style lang="scss" scoped>
.change-avatar {
  width: 100px;
  height: 100px;
  margin: 10px auto;

  :deep(.img-lg) {
    width: 100%;
    height: 100%;
  }

  img {
    display: block;
    margin-bottom: 15px;
    border-radius: 50%;
  }
}
</style>
