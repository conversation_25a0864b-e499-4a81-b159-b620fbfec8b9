<template>
  <el-dialog
    :title="formData.id ? '编辑公海' : '新增公海'"
    v-model="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" label-width="150px" :rules="formRules">
      <!-- 基本信息部分 -->
      <div class="basic-info">
        <h3 style="font-weight: bold">基本信息</h3>
        <el-form-item label="公海名称" prop="name" required>
          <el-input v-model="formData.name" placeholder="请输入公海名称" />
        </el-form-item>
        <el-form-item label="公海管理员" prop="customerAdminId" required>
          <ZUserInput
            v-model="formData.customerAdminId"
            :is-multiple="false"
            :is-init-search="true"
          />
        </el-form-item>
        <el-form-item label="公海成员" prop="customerMemberId" required>
          <ZUserInput
            v-model="formData.customerMemberId"
            :is-multiple="true"
            :is-init-search="true"
          />
        </el-form-item>
      </div>

      <!-- 规则设置部分 -->
      <div class="rule-settings">
        <h3 style="font-weight: bold">规则设置</h3>
        <el-form-item label="前负责人领取规则" prop="isPreOwnerReceive">
          <el-radio-group v-model="formData.isPreOwnerReceive">
            <el-radio :label="false">不限制</el-radio>
            <el-radio :label="true">限制</el-radio>
          </el-radio-group>
          <div class="input-cell" v-if="formData.isPreOwnerReceive">
            前负责人
            <el-input-number :min="0" v-model="formData.preOwnerReceiveDay" />
            天内不允许领取该客户
          </div>
        </el-form-item>

        <el-form-item label="领取频率规则" prop="isReceiveRule">
          <el-radio-group v-model="formData.isReceiveRule">
            <el-radio :label="false">不限制</el-radio>
            <el-radio :label="true">限制</el-radio>
          </el-radio-group>
          <div class="input-cell" v-if="formData.isReceiveRule">
            每天最多领取
            <el-input-number :min="0" v-model="formData.receiveRuleNum" />
            个公海客户
          </div>
        </el-form-item>

        <el-form-item label="提醒规则" prop="isRemind">
          <el-radio-group v-model="formData.isRemind">
            <el-radio :label="false">不提醒</el-radio>
            <el-radio :label="true">提醒</el-radio>
          </el-radio-group>
          <div class="input-cell" v-if="formData.isRemind">
            提前
            <el-input-number :min="0" v-model="formData.remindDay" />
            天提醒负责人
          </div>
        </el-form-item>

        <el-form-item label="回收规则" prop="isRecycle">
          <el-checkbox v-model="formData.isRecycle">启用回收规则</el-checkbox>
        </el-form-item>

        <!-- 无新建跟进规则 -->
        <el-form-item label="无新建跟进规则" v-if="formData.isRecycle">
          <div class="recycle">
            <div class="recycle-li">
              <el-checkbox v-model="formData.notRecords.effect">
                超过N天"无新建跟进（跟进记录）"的客户，由系统定时退回公海客户池
              </el-checkbox>
              <div class="recycle-li-table" v-if="formData.notRecords.effect">
                <div class="recycle-li-table-th">
                  <div class="recycle-li-table-th-label">选择不进入公海客户</div>
                  <div class="recycle-li-table-th-form">
                    <el-checkbox-group v-model="formData.notRecords.notInCustomer">
                      <el-checkbox value="deal">已成交的客户</el-checkbox>
                      <el-checkbox value="business">有商机的客户</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                <el-radio-group style="width: 100%" v-model="formData.notRecords.customerRule">
                  <div class="recycle-li-table-tr">
                    <el-radio :value="1">所有客户统一设置</el-radio>
                    <el-form-item label="所有客户">
                      超过
                      <el-input-number :min="0" v-model="formData.notRecords.customerRuleAllDays" />
                      天未跟进，进入公海
                    </el-form-item>
                  </div>
                  <div class="recycle-li-table-tr">
                    <el-radio :value="2">根据客户级别分别设置</el-radio>
                    <el-form-item label="A（重点客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notRecords.customerRuleLevelADays"
                      />
                      天未跟进，进入公海
                    </el-form-item>
                    <el-form-item label="B（普通客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notRecords.customerRuleLevelBDays"
                      />
                      天未跟进，进入公海
                    </el-form-item>
                    <el-form-item label="C（非优先客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notRecords.customerRuleLevelCDays"
                      />
                      天未跟进，进入公海
                    </el-form-item>
                  </div>
                </el-radio-group>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 无新建商机规则 -->
        <el-form-item label="无新建商机规则" v-if="formData.isRecycle">
          <div class="recycle">
            <div class="recycle-li">
              <el-checkbox v-model="formData.notBusiness.effect"
                >超过N天"无新建商机"的客户，由系统定时退回公海客户池
              </el-checkbox>
              <div class="recycle-li-table" v-if="formData.notBusiness.effect">
                <div class="recycle-li-table-th">
                  <div class="recycle-li-table-th-label">选择不进入公海客户</div>
                  <div class="recycle-li-table-th-form">
                    <el-checkbox-group v-model="formData.notBusiness.notInCustomer">
                      <el-checkbox value="deal">已成交的客户</el-checkbox>
                      <el-checkbox value="business">有商机的客户</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                <el-radio-group style="width: 100%" v-model="formData.notBusiness.customerRule">
                  <div class="recycle-li-table-tr">
                    <el-radio :value="1">所有客户统一设置</el-radio>
                    <el-form-item label="所有客户">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notBusiness.customerRuleAllDays"
                      />
                      天未新建商机，进入公海
                    </el-form-item>
                  </div>
                  <div class="recycle-li-table-tr">
                    <el-radio :value="2">根据客户级别分别设置</el-radio>
                    <el-form-item label="A（重点客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notBusiness.customerRuleLevelADays"
                      />
                      天未新建商机，进入公海
                    </el-form-item>
                    <el-form-item label="B（普通客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notBusiness.customerRuleLevelBDays"
                      />
                      天未新建商机，进入公海
                    </el-form-item>
                    <el-form-item label="C（非优先客户）">
                      超过
                      <el-input-number
                        :min="0"
                        v-model="formData.notBusiness.customerRuleLevelCDays"
                      />
                      天未新建商机，进入公海
                    </el-form-item>
                  </div>
                </el-radio-group>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 未成交规则 -->
        <el-form-item label="未成交规则" v-if="formData.isRecycle">
          <div class="recycle">
            <div class="recycle-li">
              <el-checkbox v-model="formData.notDeal.effect"
                >超过N天"未成交"的客户，由系统定时退回公海客户池
              </el-checkbox>
              <div class="recycle-li-table" v-if="formData.notDeal.effect">
                <div class="recycle-li-table-th">
                  <div class="recycle-li-table-th-label">选择不进入公海客户</div>
                  <div class="recycle-li-table-th-form">
                    <el-checkbox-group v-model="formData.notDeal.notInCustomer">
                      <el-checkbox value="deal">已成交的客户</el-checkbox>
                      <el-checkbox value="business">有商机的客户</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                <el-radio-group style="width: 100%" v-model="formData.notDeal.customerRule">
                  <div class="recycle-li-table-tr">
                    <el-radio :value="1">所有客户统一设置</el-radio>
                    <el-form-item label="所有客户">
                      超过
                      <el-input-number :min="0" v-model="formData.notDeal.customerRuleAllDays" />
                      天未成交，进入公海
                    </el-form-item>
                  </div>
                  <div class="recycle-li-table-tr">
                    <el-radio :value="2">根据客户级别分别设置</el-radio>
                    <el-form-item label="A（重点客户）">
                      超过
                      <el-input-number :min="0" v-model="formData.notDeal.customerRuleLevelADays" />
                      天未成交，进入公海
                    </el-form-item>
                    <el-form-item label="B（普通客户）">
                      超过
                      <el-input-number :min="0" v-model="formData.notDeal.customerRuleLevelBDays" />
                      天未成交，进入公海
                    </el-form-item>
                    <el-form-item label="C（非优先客户）">
                      超过
                      <el-input-number :min="0" v-model="formData.notDeal.customerRuleLevelCDays" />
                      天未成交，进入公海
                    </el-form-item>
                  </div>
                </el-radio-group>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import * as MainApi from '@/api/crm/customer/seas'

const emits = defineEmits(['complete'])
const formRef = ref()
const formRules = ref({
  name: [{ required: true, message: '请输入公海名称', trigger: 'blur' }],
  customerAdminId: [{ required: true, message: '请选择公海管理员', trigger: 'change' }],
  customerMemberId: [{ required: true, message: '请选择公海成员', trigger: 'change' }]
})
const dialogVisible = ref(false)

// 表单数据
const formData = ref({
  id: null, // ID
  name: '', // 公海名称
  customerAdminId: null, // 公海管理员
  customerMemberId: [], // 公海成员
  isPreOwnerReceive: false, // 是否限制前负责人领取
  preOwnerReceiveDay: 0, // 前负责人领取规则限制天数
  isReceiveRule: false, // 是否限制领取频率
  receiveRuleNum: 0, // 领取频率规则
  isRemind: false, // 是否设置提前提醒
  remindDay: 0, // 提醒规则天数
  isRecycle: true, // 收回规则
  status: true, // 是否启用
  notRecords: {
    effect: false,
    notInCustomer: [],
    customerRule: 1,
    customerRuleAllDays: 0,
    customerRuleLevelADays: 0,
    customerRuleLevelBDays: 0,
    customerRuleLevelCDays: 0
  },
  notBusiness: {
    effect: false,
    notInCustomer: [],
    customerRule: 1,
    customerRuleAllDays: 0,
    customerRuleLevelADays: 0,
    customerRuleLevelBDays: 0,
    customerRuleLevelCDays: 0
  },
  notDeal: {
    effect: false,
    notInCustomer: [],
    customerRule: 1,
    customerRuleAllDays: 0,
    customerRuleLevelADays: 0,
    customerRuleLevelBDays: 0,
    customerRuleLevelCDays: 0
  }
})

/** 查询详情 */
const getDetail = async (id: any) => {
  const data = await MainApi.getCustomerSeasRule(id)
  if (data.notRecords) {
    data.notRecords = JSON.parse(data.notRecords)
  }
  if (data.notBusiness) {
    data.notBusiness = JSON.parse(data.notBusiness)
  }
  if (data.notDeal) {
    data.notDeal = JSON.parse(data.notDeal)
  }
  formData.value = data
}

/**提交表单*/
const submitForm = async () => {
  formRef.value.validate(async (valid: any) => {
    if (!valid) return

    try {
      // 创建提交数据的副本，避免修改原始表单数据
      const submitData = JSON.parse(JSON.stringify(formData.value))

      // 将对象转换为JSON字符串
      submitData.notRecords = JSON.stringify(submitData.notRecords)
      submitData.notBusiness = JSON.stringify(submitData.notBusiness)
      submitData.notDeal = JSON.stringify(submitData.notDeal)

      if (!submitData.id) {
        await MainApi.createCustomerSeasRule(submitData)
      } else {
        await MainApi.updateCustomerSeasRule(submitData)
      }

      dialogVisible.value = false
      emits('complete')
    } catch (error) {
      console.error(error)
    }
  })
}

// 打开弹窗
const open = async (id: any) => {
  dialogVisible.value = true

  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 初始化默认值
  formData.value = {
    id: null,
    name: '',
    customerAdminId: null,
    customerMemberId: [],
    isPreOwnerReceive: false,
    preOwnerReceiveDay: 0,
    isReceiveRule: false,
    receiveRuleNum: 0,
    isRemind: false,
    remindDay: 0,
    isRecycle: true,
    status: true,
    notRecords: {
      effect: false,
      notInCustomer: [],
      customerRule: 1,
      customerRuleAllDays: 0,
      customerRuleLevelADays: 0,
      customerRuleLevelBDays: 0,
      customerRuleLevelCDays: 0
    },
    notBusiness: {
      effect: false,
      notInCustomer: [],
      customerRule: 1,
      customerRuleAllDays: 0,
      customerRuleLevelADays: 0,
      customerRuleLevelBDays: 0,
      customerRuleLevelCDays: 0
    },
    notDeal: {
      effect: false,
      notInCustomer: [],
      customerRule: 1,
      customerRuleAllDays: 0,
      customerRuleLevelADays: 0,
      customerRuleLevelBDays: 0,
      customerRuleLevelCDays: 0
    }
  }

  // 如果有ID，查询详情
  if (id) {
    await getDetail(id)
  }
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.basic-info,
.rule-settings {
  margin-bottom: 20px;

  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
}

.input-cell {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .el-input-number {
    width: 180px;
    margin: 0 10px;
  }
}

.recycle {
  width: 100%;

  .recycle-li {
    .recycle-li-table {
      border: 1px solid var(--el-border-color);
      padding: 10px 20px;
      margin-top: 10px;

      .recycle-li-table-th {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .recycle-li-table-th-label {
          width: 140px;
        }
      }

      .recycle-li-table-tr {
        width: 100%;
        margin-bottom: 10px;
        background: var(--el-border-color-extra-light);
        padding: 5px 10px;
        border-radius: 5px;
        line-height: 0;

        :deep(.el-form-item) {
          padding: 5px 0;

          .el-form-item__content {
            display: flex;

            .el-input-number {
              width: 180px;
              margin: 0 10px;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
