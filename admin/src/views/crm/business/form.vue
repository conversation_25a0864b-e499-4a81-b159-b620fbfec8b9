<template>
  <ZCustomFormDialog ref="formRef" v-model:rules="formRules" page-key="crmBusiness"
                     :config="{form: {defaultVal: defaultVal, disabledProps: disabledProps}}"
                     @get-detail="getDetail" @on-save="onSave" :is-custom="true"
                     @before-set-columns="onBeforeSetColumns">
    <!-- 阶段 -->
    <template #form-prop-stageId="scope">
      <el-select v-model="scope.form.stageId" filterable show-arrow clearable placeholder="请选择阶段"
                 :disabled="scope.disabled" remote :remote-method="(query) => getStageSelectList(query)">
        <el-option v-for="item in getSelectRemoteFullList(stageSelectList, scope.form.stageId, scope.form.stageName, 'stageName')"
                   :key="item.id" :label="item.stageName" :value="item.id"/>
      </el-select>
    </template>
    <!-- 客户名称 -->
    <template #form-prop-customerId="scope">
      <el-select v-model="scope.form.customerId" filterable show-arrow clearable placeholder="请选择客户名称"
                 :disabled="scope.disabled" remote :remote-method="(query) => getCustomerSelectList(query)">
        <el-option
            v-for="item in getSelectRemoteFullList(customerSelectList, scope.form.customerId, scope.form.customerName)"
            :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>
    <!-- 商机状态组 -->
    <template #form-prop-stepId="scope">
      <el-select v-model="scope.form.stepId" filterable show-arrow clearable placeholder="请选择商机状态组"
                 :disabled="scope.disabled" remote :remote-method="(query) => getStepSelectList(query)">
        <el-option v-for="item in getSelectRemoteFullList(stepSelectList, scope.form.stepId, scope.form.stepName)"
                   :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>
    <!-- 产品 -->
    <template #form-prop-productDetailList="scope">
      <ProductList
          v-model="scope.form.productDetailList"
          v-model:totalDiscount="scope.form.totalDiscount"
          v-model:totalAmount="scope.form.amount"
      />
    </template>
  </ZCustomFormDialog>
</template>

<script lang="ts" setup>
import * as MainApi from "@/api/crm/business";
import {getSelectRemoteFullList} from "@/utils/dict";
import { getStageList, getStagePage } from '@/api/crm/stage'
import {CrmModelTypeEnum, customerSelectList, getCustomerSelectList} from '@/api/crm/common'
import { getStePage, getStepList } from '@/api/crm/step'
import {ColumnVo} from '@/components/Zeadoor/interface'
import ProductList from '@/views/crm/common/product/product-list.vue'
import {propTypes} from "@/utils/propTypes";

const emits = defineEmits(['complete'])
const formRef = ref()
const formRules = ref()
const props = defineProps({
  defaultVal: propTypes.object.def(),
  disabledProps: propTypes.object.def([])
})

const onBeforeSetColumns = (columns: ColumnVo[]) => {
  columns.push({
    label: '产品',
    prop: 'productDetailList',
    span: 24
  })
}

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getBusiness(id)
    done(data)
  } catch {
    error()
  }
}

/**保存数据*/
const onSave = async (formData: any, done: Function, error: Function) => {
  try {
    if (!formData.id) {
      await MainApi.createBusiness(formData)
    } else {
      await MainApi.updateBusiness(formData)
    }
    emits("complete")
    done()
  } catch {
    error()
  }
}

const stageSelectList = ref([])
const getStageSelectList = async (searchKey?: string) => {
  try {
    const res = await getStagePage({
      stageName: searchKey,
      targetType: CrmModelTypeEnum.BUSINESS
    })
    stageSelectList.value = res.list
  } finally {
  }
}

// 获取商机状态组选择列表
const stepSelectList = ref([])
const getStepSelectList = async (searchKey?: string) => {
  try {
    const data = await getStePage({
      name: searchKey,
      targetType: CrmModelTypeEnum.BUSINESS,
      isEffect: true
    })
    stepSelectList.value = data.list
  } finally {
  }
}

defineExpose({
  open: (id: number) => {
    formRef.value.open(id)
    // getStageSelectList()
    // getCustomerSelectList()
    // getStepSelectList()
  }
})
</script>
