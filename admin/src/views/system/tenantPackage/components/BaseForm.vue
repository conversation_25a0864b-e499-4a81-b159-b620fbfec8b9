<template>
  <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="110px">
    <div grid="~ cols-2 gap-x-80px">
      <ElFormItem label="套餐价格(元)" prop="price">
        <ZealInputNumber
          v-model="formData.price"
          placeholder="请输入套餐价格"
          :min="0"
          :max="999999"
          :precision="2"
          class="!w-300px"
        />
      </ElFormItem>
      <ElFormItem label="套餐原价(元)" prop="originalPrice">
        <ZealInputNumber
          v-model="formData.originalPrice"
          placeholder="请输入套餐原价"
          :min="0"
          :max="999999"
          :precision="2"
          class="!w-300px"
        />
      </ElFormItem>
      <ElFormItem label="最小账号数量" prop="minAccountCount">
        <ZealInputNumber
          v-model="formData.minAccountCount"
          placeholder="请输入最小账号数量"
          :min="0"
          :max="999999"
          :precision="0"
          class="!w-300px"
        />
      </ElFormItem>
      <ElFormItem label="最大账号数量" prop="maxAccountCount">
        <ZealInputNumber
          v-model="formData.maxAccountCount"
          placeholder="请输入最大账号数量"
          :min="0"
          :max="999999"
          :precision="0"
          class="!w-300px"
        />
      </ElFormItem>
    </div>
    <ElFormItem label="是否推荐" prop="recommend">
      <ElRadioGroup v-model="formData.recommend" class="!w-full">
        <ElRadio :value="false"> 否 </ElRadio>
        <ElRadio :value="true"> 是 </ElRadio>
      </ElRadioGroup>
    </ElFormItem>

    <ElFormItem label="套餐功能" prop="valueAdded">
      <div class="w-full rounded-4px" style="border: 1px solid #d9d9d9">
        <VueDraggable
          handle=".drag-handler"
          target="tbody"
          v-model="tableData"
          @end="onDraggableEnd"
        >
          <ElTable class="!w-full" :data="tableData" :row-class-name="rowDraggableClass">
            <ElTableColumn type="index" label="序号" width="100" />
            <ElTableColumn label="套餐功能" prop="name">
              <template #default="{ row }">
                <!-- <span>{{ row.name }}</span> -->
                <ElInput
                  v-model="row.name"
                  placeholder="请输入套餐功能"
                  :maxlength="50"
                  show-word-limit
                />
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="80" fixed="right">
              <template #default="{ row, $index }">
                <ElButton link type="danger" @click="handleDeleteTable(row, $index)">
                  删除
                </ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </VueDraggable>
        <div
          class="text-14px text-[--el-color-primary] leading-20px my-10px ml-8px cursor-pointer"
          flex="~ row items-center gap-4px"
          @click="handleAddTable"
        >
          <ElIcon><CirclePlus /></ElIcon>
          <div>增加</div>
        </div>
      </div>
    </ElFormItem>
  </ElForm>
</template>
<script setup lang="ts">
import { TenantPackageBaseVO } from '@/api/system/tenantPackage'
import { VueDraggable, DraggableEvent } from 'vue-draggable-plus'
import { CirclePlus } from '@element-plus/icons-vue'

defineComponent({
  name: 'MultipleTenantPackageBaseForm'
})

const message = useMessage()

// 自定义最小账号数校验
const validateMinAccountCount = (_rule: any, value: any, callback: any) => {
  if (value === 0) {
    callback(new Error('最小账号数量不能为0'))
  } else if (!value) {
    callback(new Error('最小账号数量不能为空'))
  } else if (Number(formData.value.maxAccountCount) === value) {
    callback(new Error('最小账号数量不能等于最大账号数量'))
  } else if (Number(formData.value.maxAccountCount) < value) {
    callback(new Error('最小账号数量不能大于最大账号数量'))
  } else {
    callback()
  }
}

// 自定义最大账号数校验
const validateMaxAccountCount = (_rule: any, value: any, callback: any) => {
  if (value === 0) {
    callback(new Error('最大账号数量不能为0'))
  } else if (!value) {
    callback(new Error('最大账号数量不能为空'))
  } else if (Number(formData.value.minAccountCount) === value) {
    callback(new Error('最大账号数量不能等于最小账号数量'))
  } else if (Number(formData.value.minAccountCount) > value) {
    callback(new Error('最大账号数量不能小于最小账号数量'))
  } else {
    callback()
  }
}

// 自定义套餐功能校验
const validateValueAdded = (_rule: any, _value: any, callback: any) => {
  if (tableData.value.length <= 0) {
    callback(new Error('套餐功能不能为空'))
  } else {
    for (let i = 0; i < tableData.value.length; i++) {
      if (tableData.value[i].name.length === 0) {
        callback(new Error(`第${i + 1}行套餐功能不能为空`))
        return
      }
    }
    callback()
  }
}

const formRules = reactive({
  price: [{ required: true, message: '套餐价格不能为空', trigger: 'change' }],
  originalPrice: [{ required: true, message: '套餐原价不能为空', trigger: 'change' }],
  minAccountCount: [{ required: true, validator: validateMinAccountCount, trigger: 'change' }],
  maxAccountCount: [{ required: true, validator: validateMaxAccountCount, trigger: 'change' }],
  recommend: [{ required: true, message: '是否推荐不能为空', trigger: 'change' }],
  valueAdded: [{ required: true, validator: validateValueAdded, trigger: 'change' }]
})

const formRef = ref() // 表单 Ref

const defineFormData: TenantPackageBaseVO = {
  price: undefined,
  originalPrice: undefined,
  minAccountCount: undefined,
  maxAccountCount: undefined,
  recommend: undefined,
  valueAdded: ''
}

const formData = ref<TenantPackageBaseVO>({ ...defineFormData })

interface TableData {
  name: string
}
const defineTableData = [
  {
    name: ''
  }
]
const tableData = ref<TableData[]>([...defineTableData])

/**
 * 让每一行表格可以拖动
 */
function rowDraggableClass() {
  return 'drag-handler'
}
/**
 * 拖动换位置
 */
function onDraggableEnd(event: DraggableEvent) {
  console.log(event)
}
/**
 * 删除套餐功能
 */
async function handleDeleteTable(row: any, rowIndex: number) {
  await message.delConfirm(`确认删除‘${row.name}’？`)
  tableData.value.splice(rowIndex, 1)
}

/**
 * 增加套餐功能
 */
function handleAddTable() {
  tableData.value.push({
    name: ''
  })
}

/**
 * 赋值表单
 * @param data
 */
function setFormData(data: TenantPackageBaseVO) {
  // PS: 金额不在这里解析，再父类解析
  for (const key in formData.value) {
    if (data.hasOwnProperty(key)) {
      // 确保是对象自身的属性，而不是继承的属性
      formData.value[key] = data[key]
    }
  }
  if (formData.value.valueAdded) {
    try {
      tableData.value = JSON.parse(formData.value.valueAdded)
    } catch (error) {
      tableData.value = [...defineTableData]
    }
  }
}

/**
 * 获取表单数据
 * @returns
 */
function getFormData(): TenantPackageBaseVO {
  // PS: 金额不在这里解析，再父类解析
  formData.value.valueAdded = JSON.stringify(tableData.value)
  return formData.value
}

/**
 * 校验表单
 */
async function validate(): Promise<boolean> {
  if (!formRef) return false
  const valid = await formRef.value.validate()
  if (!valid) return false

  return true
}

/**
 * 重置表单
 */
function resetForm() {
  formData.value = { ...defineFormData }
  formRef.value?.resetFields()
}

defineExpose({
  setFormData,
  getFormData,
  validate,
  resetForm
})
</script>
<style scoped lang="scss"></style>
