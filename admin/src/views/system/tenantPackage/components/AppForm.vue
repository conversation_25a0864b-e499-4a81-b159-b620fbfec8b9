<template>
  <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="110px">
    <div grid="~ cols-2 gap-x-80px">
      <ElFormItem label="应用价格(元)" prop="price">
        <ZealInputNumber
          v-model="formData.price"
          placeholder="请输入应用价格"
          :min="0"
          :max="999999"
          :precision="2"
          class="!w-300px"
        />
      </ElFormItem>
      <ElFormItem label="应用原价(元)" prop="originalPrice">
        <ZealInputNumber
          v-model="formData.originalPrice"
          placeholder="请输入应用原价"
          :min="0"
          :max="999999"
          :precision="2"
          class="!w-300px"
        />
      </ElFormItem>
    </div>
    <ElFormItem label="应用标签" prop="tags">
      <ElSelect
        v-model="packageTags"
        multiple
        filterable
        allow-create
        default-first-option
        :reserve-keyword="false"
        placeholder="请输入应用标签，按回车添加"
      >
        <ElOption
          v-for="(item, index) in packageTagOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </ElSelect>
    </ElFormItem>
    <el-form-item label="行业" prop="industry">
      <ElSelect
        v-model="packageIndustries"
        multiple
        filterable
        :reserve-keyword="false"
        placeholder="选择行业"
      >
        <ElOption
          v-for="(item, index) in packageIndustriesOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </ElSelect>
    </el-form-item>
    <ElFormItem label="应用简介" prop="summary">
      <ElInput
        v-model="formData.summary"
        placeholder="请输入应用简介"
        type="textarea"
        show-word-limit
        :maxlength="200"
        :rows="2"
      />
    </ElFormItem>
    <div grid="~ cols-2 gap-x-80px">
      <ElFormItem label="应用logo" prop="logo">
        <UploadImageById
          v-model="logo"
          width="80px"
          height="80px"
          :file-size="0.3"
          :path="ExtraPathEnum.TENANT_PACKAGE_APP"
        >
          <template #tip>
            <div style="color: #8c8c8c"> 支持jpg、jpeg、png格式，大小不超过300KB</div>
          </template>
        </UploadImageById>
      </ElFormItem>
      <ElFormItem label="应用介绍图" prop="effectImage">
        <UploadImageById
          v-model="effectImage"
          width="80px"
          height="80px"
          :file-size="1"
          :path="ExtraPathEnum.TENANT_PACKAGE_APP"
        >
          <template #tip>
            <div style="color: #8c8c8c"> 支持jpg、jpeg、png格式，大小不超过1MB</div>
          </template>
        </UploadImageById>
      </ElFormItem>
    </div>
    <ElFormItem label="预览方式" prop="preview">
      <ElSelect v-model="formData.preview" placeholder="请选择预览方式" class="!w-300px">
        <ElOption label="介绍图" :value="false" />
        <ElOption label="应用" :value="true" />
      </ElSelect>
    </ElFormItem>
    <ElFormItem label="应用详情" prop="detail">
      <Editor v-model="formData.detail" :upload-path="ExtraPathEnum.TENANT_PACKAGE_APP"  height="350px" />
    </ElFormItem>
  </ElForm>
</template>
<script setup lang="ts">
import { ExtraPathEnum, UploadImageItem } from '@/components/UploadFile/src/extraPathEnum'
import { TenantPackageAppVO, TenantPackageVO } from '@/api/system/tenantPackage'
import { DictDataType, getDictOptions } from '@/utils/dict'

defineComponent({
  name: 'MultipleTenantPackageAppForm'
})

// 自定义应用logo图
const validateLogo = (_rule: any, _value: any, callback: any) => {
  if (!logo.value?.url) {
    callback(new Error('应用logo不能为空'))
  } else {
    callback()
  }
}

// 自定义应用简介图校验
const validateEffectImage = (_rule: any, _value: any, callback: any) => {
  if (!effectImage.value?.url) {
    callback(new Error('应用简介图不能为空'))
  } else {
    callback()
  }
}

// 自定义标签校验
const validateTags = (_rule: any, _value: any, callback: any) => {
  if (packageTags.value.length <= 0) {
    callback(new Error('应用标签不能为空'))
  } else {
    callback()
  }
}

// 自定义行业校验
const validateIndustry = (_rule: any, _value: any, callback: any) => {
  if (packageIndustries.value.length <= 0) {
    callback(new Error('应用行业不能为空'))
  } else {
    callback()
  }
}
const formRules = reactive({
  price: [{ required: true, message: '应用价格不能为空', trigger: 'change' }],
  originalPrice: [{ required: true, message: '应用原价不能为空', trigger: 'change' }],
  industry: [{ required: true, validator: validateIndustry, trigger: 'change' }],
  tags: [{ required: true, validator: validateTags, trigger: 'change' }],
  logo: [{ required: true, validator: validateLogo, trigger: 'change' }],
  effectImage: [{ required: true, validator: validateEffectImage, trigger: 'change' }],
  summary: [{ required: true, message: '应用简介不能为空', trigger: 'change' }],
  detail: [{ required: true, message: '应用详情不能为空', trigger: 'change' }],
  preview: [{ required: true, message: '请选择图片预览方式', trigger: 'change' }]
})

const formRef = ref() // 表单 Ref

const defineFormData: TenantPackageAppVO = {
  price: undefined,
  originalPrice: undefined,
  tags: '',
  industry: '',
  logo: '',
  effectImage: '',
  summary: '',
  detail: '',
  preview: false
}

const formData = ref<TenantPackageAppVO>({ ...defineFormData })

const packageIndustries = ref<string[]>([])

// 套餐行业字典
const packageIndustriesOptions = ref<DictDataType[]>([])
/**
 * 获取行业字典
 */
function getPackageIndustriesOptions() {
  packageIndustriesOptions.value = getDictOptions(DICT_TYPE.TEAMIFY_COMPANY_INDUSTRY)
}

const packageTags = ref<string[]>([])

// 套餐类标
const packageTagOptions = ref<DictDataType[]>([])

/**
 * 获取套餐类标签字典
 */
function getPackageTagOptions() {
  packageTagOptions.value = getDictOptions(DICT_TYPE.SYSTEM_TENANT_PACKAGE_TAGS)
}

const logo = ref<UploadImageItem>({
  id: '',
  url: '',
  name: ''
})

const effectImage = ref<UploadImageItem>({
  id: '',
  url: '',
  name: ''
})

onMounted(() => {
  getPackageTagOptions()
  getPackageIndustriesOptions()
})

/**
 * 赋值表单
 * @param data
 */
function setFormData(data: TenantPackageVO) {
  // PS: 金额不在这里解析，再父类解析
  for (const key in formData.value) {
    if (data.hasOwnProperty(key)) {
      // 确保是对象自身的属性，而不是继承的属性
      formData.value[key] = data[key]
    }
  }
  // 解析logo
  if (formData.value.logo) {
    logo.value = { id: '', url: formData.value.logo, name: '' }
  }

  // 解析效果展示图
  if (formData.value.effectImage) {
    effectImage.value = { id: '', url: formData.value.effectImage, name: '' }
  }

  //  解析标签
  if (formData.value.tags) {
    packageTags.value = formData.value.tags.split(',')
  }

  // 解析行业
  if (formData.value.industry) {
    packageIndustries.value = formData.value.industry.split(',')
  }
}

/**
 * 获取表单数据
 * @returns
 */
function getFormData(): TenantPackageAppVO {
  // PS: 金额不在这里解析，再父类解析
  // 解析logo
  if (logo.value.id) {
    formData.value.logo = logo.value.url
  }
  // 解析效果展示图
  if (effectImage.value.id) {
    formData.value.effectImage = effectImage.value.url
  }
  // 解析行业
  formData.value.industry = packageIndustries.value.join(',')
  // 解析标签
  const tags: string[] = []
  packageTags.value.forEach((tag) => {
    tags.push(tag)
  })
  formData.value.tags = tags.join(',')
  return formData.value
}

/**
 * 校验表单
 */
async function validate(): Promise<boolean> {
  if (!formRef) return false
  const valid = await formRef.value.validate()
  if (!valid) return false

  return true
}

/**
 * 重置表单
 */
function resetForm() {
  formData.value = { ...defineFormData }
  packageIndustries.value = []
  packageTags.value = []
  logo.value = { id: '', url: '', name: '' }
  effectImage.value = { id: '', url: '', name: '' }
  formRef.value?.resetFields()
}

defineExpose({
  setFormData,
  getFormData,
  validate,
  resetForm
})
</script>
<style scoped lang="scss"></style>
