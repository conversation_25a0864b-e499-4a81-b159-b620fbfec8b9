<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    size="large"
    @closed="menuDialogVisible = false"
  >
    <ElScrollbar :height="formData.type ? '560px' : '200px'">
      <ElForm
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        :rules="formRules"
        label-width="110px"
      >
        <FormTitle title="基础信息" />
        <div class="px-30px">
          <div grid="~ cols-2 gap-x-80px">
            <ElFormItem label="套餐名称" prop="name">
              <ElInput v-model="formData.name" placeholder="请输入套餐名" class="!w-300px" />
            </ElFormItem>
            <ElFormItem label="套餐类型" prop="type">
              <ElSelect v-model="formData.type" :disabled="formType !== 'create'" placeholder="请选择套餐类型" class="!w-300px">
                <ElOption
                  v-for="(dict, index) in packageTypeOptions"
                  :key="index"
                  :label="dict.label"
                  :value="dict.value"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="套餐状态" prop="status">
              <ElRadioGroup v-model="formData.status" class="!w-300px">
                <ElRadio
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :value="dict.value"
                >
                  {{ dict.label }}
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="备注" prop="remark">
              <ElInput v-model="formData.remark" placeholder="请输入备注" class="!w-300px" />
            </ElFormItem>
          </div>

          <ElFormItem label="套餐权限" prop="menuIds">
            <div
              class="w-full min-h-32px rounded-4px py-5px px-12px box-border cursor-pointer"
              style="border: 1px solid #00000026"
              flex="! row items-center justify-between"
              @click="handleOpenMenuDialog"
            >
              <div
                v-if="menuNames.length === 0"
                class="text-[--el-text-color-placeholder]"
                style="line-height: 20px"
                >请选择套餐权限</div
              >
              <div v-else flex="~ wrap gap-8px">
                <ElTag v-for="(item, index) in menuNames" :key="index" type="info">{{
                  item
                }}</ElTag>
              </div>
              <div class="text-14px text-[--el-text-color-placeholder]" flex="~ row items-center">
                <ElIcon><ArrowDown /></ElIcon
              ></div>
            </div>
          </ElFormItem>
        </div>
      </ElForm>
      <ElDivider v-if="formData.type" />
      <FormTitle v-if="formData.type" :title="currentPackageName" />
      <div v-if="formData.type" class="px-30px">
        <AppForm v-if="isAppPackage" ref="baseAppFormRef" />
        <BaseForm v-else ref="baseAppFormRef" />
        <div class="w-full h-100px"></div>
      </div>
    </ElScrollbar>
    <template #footer>
      <ElButton :disabled="formLoading" type="primary" @click="submitForm">确 定</ElButton>
      <ElButton @click="dialogVisible = false">取 消</ElButton>
    </template>

    <Dialog v-model="menuDialogVisible" title="套餐权限" append-to-body @opened="initMenuDialog">
      <ElCard class="cardHeight">
        <template #header>
          全选/全不选:
          <ElSwitch
            class="mr-16px"
            v-model="treeNodeAll"
            active-text="是"
            inactive-text="否"
            inline-prompt
            @change="handleCheckedTreeNodeAll"
          />
          全部展开/折叠:
          <ElSwitch
            v-model="menuExpand"
            active-text="展开"
            inactive-text="折叠"
            inline-prompt
            @change="handleCheckedTreeExpand"
          />
        </template>
        <ElTree
          ref="treeRef"
          :data="menuOptions"
          :props="defaultProps"
          empty-text="加载中，请稍候"
          node-key="id"
          show-checkbox
        />
      </ElCard>
      <template #footer>
        <ElButton :disabled="formLoading" type="primary" @click="handleSelectMenu()"
          >确 定</ElButton
        >
        <ElButton @click="menuDialogVisible = false">取 消</ElButton>
      </template>
    </Dialog>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, DictDataType, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import * as TenantPackageApi from '@/api/system/tenantPackage'
import * as MenuApi from '@/api/system/menu'
import { ElTree } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { fenToYuan, yuanToFen } from '@/utils'
import AppForm from './components/AppForm.vue'
import BaseForm from './components/BaseForm.vue'
defineOptions({ name: 'SystemTenantPackageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
interface FormData {
  id: number | null
  name: string | null
  remark: string | null
  menuIds: number[]
  status: number
  type: number | undefined
}
const formData = ref<FormData>({
  id: null,
  name: null,
  remark: null,
  menuIds: [],
  status: CommonStatusEnum.ENABLE,
  type: undefined
})
// 自定义菜单权限校验
const validateMenu = (_rule: any, _value: any, callback: any) => {
  if (formData.value.menuIds.length <= 0) {
    callback(new Error('关联的菜单权限不能为空'))
  } else {
    callback()
  }
}
const formRules = reactive({
  name: [{ required: true, message: '套餐名不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  menuIds: [{ required: true, validator: validateMenu, trigger: 'change' }],
  type: [{ required: true, message: '套餐类型不能为空', trigger: 'change' }],
  price: [{ required: true, message: '套餐价格不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const baseAppFormRef = ref() // 基础应用表单 Ref

const menuDialogVisible = ref(false)
const menuOptions = ref<any[]>([]) // 树形结构数据
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref<InstanceType<typeof ElTree>>() // 树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const menuNames = ref<string[]>([])

// 套餐类型字典
const packageTypeOptions = ref<DictDataType[]>([])
/**
 * 获取套餐类型字典
 */
function getPackageTypeOptions() {
  packageTypeOptions.value = getIntDictOptions(DICT_TYPE.SYSTEM_TENANT_PACKAGE_TYPE)
}

// 当前选择的套餐类型是不是应用套餐
const isAppPackage = computed(() => {
  if (formData.value.type === 10) {
    return true
  } else {
    return false
  }
})

//  当前选择的套餐名称
const currentPackageName = computed(() => {
  const result = packageTypeOptions.value.find((item) => item.value === formData.value.type)
  return result?.label || ''
})

onMounted(() => {})
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  formLoading.value = true
  resetForm()
  getPackageTypeOptions()
  // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
  menuOptions.value = handleTree(await MenuApi.getSimpleMenusList())
  // 修改时，设置数据
  if (id) {
    try {
      const res = await TenantPackageApi.getTenantPackage(id)
      res.price = Number(fenToYuan(res.price))
      res.originalPrice = Number(fenToYuan(res.originalPrice))
      // 设置选中
      formData.value = res
      setTimeout(() => {
        if (baseAppFormRef.value) {
          baseAppFormRef.value.setFormData(res)
        }
      }, 300)
      // 设置选中de菜单
      handleSelectMenu(res.menuIds)
    } finally {
      formLoading.value = false
    }
  }
  formLoading.value = false
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return

  const baseAppFormValid = await baseAppFormRef.value?.validate()
  if (!baseAppFormValid) return
  // 提交请求
  formLoading.value = true
  try {
    let data = formData.value as unknown as TenantPackageApi.TenantPackageVO
    let data2 = {}
    if (baseAppFormRef.value) {
      data2 = baseAppFormRef.value.getFormData()
      data = { ...data, ...data2 }
    }
    data.price = yuanToFen(Number(data.price))
    data.originalPrice = yuanToFen(Number(data.originalPrice))

    if (formType.value === 'create') {
      await TenantPackageApi.createTenantPackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await TenantPackageApi.updateTenantPackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false
  // 重置表单
  formData.value = {
    id: null,
    name: null,
    remark: null,
    menuIds: [],
    status: CommonStatusEnum.ENABLE,
    type: undefined
  }
  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
  menuNames.value = []
  if (baseAppFormRef.value) {
    baseAppFormRef.value.resetForm()
  }
}

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  treeRef.value!.setCheckedNodes(treeNodeAll.value ? menuOptions.value : [])
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  const nodes = treeRef.value?.store.nodesMap
  for (let node in nodes) {
    if (nodes[node].expanded === menuExpand.value) {
      continue
    }
    nodes[node].expanded = menuExpand.value
  }
}

/** 卡开选择菜单权限弹框 */
function handleOpenMenuDialog() {
  menuDialogVisible.value = true
}

function initMenuDialog() {
  // 加载已经设置好的菜单，怕弹框加载完对象还没初始化好，延时100ms执行
  if (formData.value.menuIds.length > 0) {
    setTimeout(() => {
      formData.value.menuIds.forEach((menuId: number) => {
        treeRef.value!.setChecked(menuId, true, false)
      })
    }, 100)
  }
}
/**
 * 选择菜单权限，按确定
 */
function handleSelectMenu(_menuIds: number[] = []) {
  menuDialogVisible.value = false
  let menuIds = _menuIds
  if (!menuIds || menuIds.length === 0) {
    menuIds = [
      ...(treeRef.value!.getCheckedKeys(false) as unknown as Array<number>), // 获得当前选中节点
      ...(treeRef.value!.getHalfCheckedKeys() as unknown as Array<number>) // 获得半选中的父节点
    ]
  }
  const lMenuNames: string[] = []
  menuOptions.value.forEach((element) => {
    if (menuIds.includes(element.id)) {
      const name = element.name as string
      lMenuNames.push(name)
    }
  })
  formData.value.menuIds = menuIds
  menuNames.value = lMenuNames
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
