<template>
  <div class="operation" v-loading="loading">
    <template v-if="total > 0">
      <div class="operation-record">
        <div class="operation-record-li" v-for="item in list" :key="item.id">
          <div class="head">
            <div class="head-user">{{ item.createName }}</div>
            <div class="head-title">{{ item.title }}</div>
          </div>
          <div class="time">
            <div class="time-label">{{ item.createTime }}</div>
          </div>
          <div class="content" v-html="item.content"></div>
        </div>
      </div>
      <div>
        <Pagination v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNo"
                    :total="total"
                    @pagination="getList"/>
      </div>
    </template>
    <el-empty v-else/>
  </div>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {propTypes} from '@/utils/propTypes'
import * as MainApi from '@/api/oa/system/operation/operation'
import {CrmModelTypeEnum} from "@/api/crm/common";

const total = ref(0) // 列表的总页数
const list = ref<any[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中

const props = defineProps({
  targetId: propTypes.number.def(),
  targetType: propTypes.string.def()
})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  targetId: props.targetId,
  targetType: props.targetType
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MainApi.getPage(Object.assign({}, queryParams, {
      belongId: props.targetType === CrmModelTypeEnum.CUSTOMER ? props.targetId : null,
      targetId: props.targetType === CrmModelTypeEnum.CUSTOMER ? null : props.targetId,
      targetType: props.targetType === CrmModelTypeEnum.CUSTOMER ? null : props.targetType
    }))
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  getList()
})
</script>

<style scoped lang="scss">
.operation-record {
  .operation-record-li {
    position: relative;
    padding-left: 40px;
    padding-bottom: 20px;

    &:last-child {
      &:after {
        display: none;
      }
    }

    &:before {
      content: "";
      width: 12px;
      height: 12px;
      background: var(--el-border-color);
      position: absolute;
      border-radius: 100px;
      top: 4px;
      left: 0;
    }

    &:after {
      content: "";
      width: 1px;
      height: 100%;
      background: var(--el-border-color);
      position: absolute;
      border-radius: 100px;
      top: 5px;
      left: 6px;
    }

    .head {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      display: flex;
      align-items: center;

      .head-user {
        color: var(--el-color-primary);
        margin-right: 10px;
      }
    }

    .time {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      display: flex;
      align-items: center;
    }

    .content {
      font-size: 14px;
      margin-top: 10px;
    }
  }
}
</style>
