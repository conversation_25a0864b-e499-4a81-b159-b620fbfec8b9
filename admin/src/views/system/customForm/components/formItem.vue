<template>
  <component v-model="formData[column.prop]" v-bind="column.props" :is="column.component">
    <template v-if="column.type === ColumnTypeVo.select">
      <el-option
          :label="dict.label"
          :value="dict.value"
          v-for="dict in column.options"
          :key="dict.value"
      />
    </template>
    <template v-if="column.type === ColumnTypeVo.radio">
      <el-radio
          :label="dict.label"
          :value="dict.value"
          v-for="dict in column.options"
          :key="dict.value"
      />
    </template>
    <template v-if="column.type === ColumnTypeVo.checkbox">
      <el-checkbox
          :label="dict.label"
          :value="dict.value"
          v-for="dict in column.options"
          :key="dict.value"
      />
    </template>
  </component>
</template>
<script setup lang="ts">
import {ColumnTypeVo} from "@/components/Zeadoor/interface";

defineProps(["column"])
const formData = ref({})
</script>
<style lang="scss">
.select-image {
  width: 120px;
  height: 120px;
  border: 1px solid #eee;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: #cbcbcb;
}

.edit-panel {
  width: 100%;
  height: 200px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50px;
  color: #c4c4c4;
}
</style>
