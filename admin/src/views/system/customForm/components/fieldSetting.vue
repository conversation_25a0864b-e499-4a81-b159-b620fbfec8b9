<template>
  <el-form v-if="field" label-width="90px" label-position="top" size="default">
    <el-form-item label="字段名称">
      <el-input type="text" v-model="field.label" @blur="handleChangeName" @input="handleChangeName"/>
    </el-form-item>
    <el-form-item label="字段ID">
      <div class="flex" style="align-items: center;">
        <div class="flex-1">{{ field.prop || "-" }}</div>
        <el-icon @click="handleChangeProp" v-if="field.isCustom">
          <EditPen/>
        </el-icon>
      </div>
    </el-form-item>
    <el-form-item label="提示文字">
      <el-input type="text" v-model="field.props.placeholder"/>
    </el-form-item>
    <el-form-item label="栅格数量">
      <el-input-number v-model="field.span" controls-position="right" :max="24" :min="1"/>
    </el-form-item>
    <el-form-item label="最大长度">
      <el-input-number v-model="field.props.maxlength" :disabled="!field.isCustom" controls-position="right" :max="9999" :min="1"/>
    </el-form-item>
    <el-form-item label="列表宽度">
      <el-input-number v-model="field.width" controls-position="right" :max="9999" :min="1"/>
    </el-form-item>
    <el-form-item label="隐藏搜索">
      <el-switch v-model="field.hiddenSearch" />
    </el-form-item>
    <el-form-item label="">
      <el-checkbox v-model="field.required" :disabled="!field.isCustom" >设为必填</el-checkbox>
    </el-form-item>
    <el-form-item label="校验规则">
      <el-select v-model="field.ruleType" placeholder="请选择内容校验规则" clearable :disabled="!field.isCustom" >
        <el-option label="手机号" value="mobile"/>
        <el-option label="邮箱" value="email"/>
        <el-option label="身份证号" value="idCard"/>
        <el-option label="网址" value="www"/>
      </el-select>
    </el-form-item>
    <el-form-item label="数据类型">
      <el-radio-group v-model="field.dataType" :disabled="!field.isCustom" >
        <el-radio label="字典" :value="DataTypeEnum.dict"/>
        <el-radio label="自定义" :value="DataTypeEnum.custom"/>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="自定义数据" label-position="top" v-if="field.dataType === DataTypeEnum.custom">
      <div class="resource-li" v-for="(item, index) in field.options" :key="index">
        <el-input v-model="item.value" placeholder="请输入内容" style="flex: 1;margin-right: 10px" :disabled="!field.isCustom" />
        <el-button link type="danger" @click="field.options.splice(index, 1)" v-if="field.isCustom">移除</el-button>
      </div>
      <el-button style="width: 100%;" @click="field.options.push({label: null, value: null})" :disabled="!field.isCustom" >添加数据
      </el-button>
    </el-form-item>
    <el-form-item label="字典" label-position="top" v-else-if="field.dataType === DataTypeEnum.dict">
      <el-select v-model="field.dictKey" filterable remote show-arrow clearable :disabled="!field.isCustom"
                 reserve-keyword placeholder="请选择字典" :remote-method="getDictList" :loading="dictLoading">
        <el-option v-for="item in dictList" :key="item.type" :label="item.name" :value="item.type"/>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {DataTypeEnum, getPlaceholderByColumn} from "@/components/Zeadoor/CustomForm/config";
import {getDictTypePage} from "@/api/system/dict/dict.type";
import {EditPen} from "@element-plus/icons-vue";
import {ElMessageBox} from "element-plus";

const props = defineProps(["modelValue"])
const emit = defineEmits(['update:modelValue'])

const field = ref<any>(Object.assign({}, props.modelValue || {}));
watch(() => field.value, function () {
  emit('update:modelValue', field.value)
}, {
  deep: true
})
watch(() => props.modelValue, function () {
  field.value = props.modelValue

  if (field.value?.dictKey) {
    getDictList(null, field.value.dictKey)
  }

}, {
  deep: true
})

/**
 * 标题修改时，自动更新占位文字
 */
const handleChangeName = () => {
  field.value.props.placeholder = getPlaceholderByColumn(field.value)
}

/**
 * 修改字段ID
 */
const handleChangeProp = () => {
  ElMessageBox.prompt('请输入字段ID', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /\S/,
    inputErrorMessage: '字段ID不能为空',
    inputValue: field.value.prop
  }).then(({value}) => {
    field.value.prop = value
  })
}

/** 查询字典列表 */
const dictList = ref([])
const dictLoading = ref(false)
const getDictList = async (name: string, type: string) => {
  dictLoading.value = true
  try {
    const data = await getDictTypePage({
      name: name,
      type: type
    })
    dictList.value = data.list
  } finally {
    dictLoading.value = false
  }
}
</script>

<style scoped lang="scss">
</style>
