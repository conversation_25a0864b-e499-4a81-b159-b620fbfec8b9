<template>
  <el-form v-if="formData" label-width="80px" label-position="top" size="default">
    <el-form-item label="表单尺寸">
      <el-radio-group v-model="formData.size" clearable>
        <el-radio-button value="large">大</el-radio-button>
        <el-radio-button value="default">中</el-radio-button>
        <el-radio-button value="small">小</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="标签长度">
      <el-input-number v-model="formData.labelWidth" clearable controls-position="right"/>
    </el-form-item>
    <el-form-item label="标签位置">
      <el-radio-group v-model="formData.labelPosition" clearable>
        <el-radio value="left">居左</el-radio>
        <el-radio value="right">居右</el-radio>
        <el-radio value="top">居上</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="栅格间距">
      <el-input-number v-model="formData.gutter" clearable controls-position="right"/>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
const props = defineProps(["modelValue"])
const formData = ref(props.modelValue)
const emit = defineEmits(['update:modelValue'])
watch(() => formData.value, () => {
  emit("update:modelValue", formData.value)
}, {
  deep: true
})
watch(() => props.modelValue, function () {
  formData.value = props.modelValue
}, {
  deep: true
})
</script>

<style scoped lang="scss">
</style>
