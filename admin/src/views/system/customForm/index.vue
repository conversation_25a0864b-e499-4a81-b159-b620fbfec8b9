<template>
  <ZCustomTable page-key="CustomFormList" :config="config" @get-list="getList" :is-custom="false" >
    <template #column-prop-version="scope">
      <font style="color: #2462ff;" v-if="scope.row.version">V{{scope.row.version}}.0</font>
      <font style="font-size: 12px;" v-else>未发布版本</font>
    </template>

  </ZCustomTable>
</template>

<script setup lang="ts">
import * as MainApi from '@/api/system/customForm/index'
import { ConfigBO } from '@/components/Zeadoor/CustomTable/config'

const { push } = useRouter() // 路由跳转
const config = ref<ConfigBO>({
  table: {
    showSelection: false,
    columns: [
      {
        label: '名称',
        prop: 'name'
      },
      {
        label: '描述',
        prop: 'description'
      },
      {
        label: '当前版本号',
        prop: 'version',
        align: "center"
      }
    ],
    toolbar: {
      width: "80px",
      buttons: [
        {
          key: 'show',
          label: '详情',
          type: 'primary',
          link: true,
          click: (scope: any) => {
            push({ name: 'CustomFormEdit', params: { id: scope.row.id } })
          }
        }
      ]
    }
  }
})

/** 查询列表 */
const getList = async (queryParams: any, done: Function, error: Function) => {
  try {
    const data = await MainApi.getPage(queryParams.value)
    done(data.list, data.total)
  } catch {
    error()
  }
}
</script>
