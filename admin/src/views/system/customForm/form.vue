<template>
  <div class="detail-page !w-full">
    <div class="detail-page-head">
      <el-button @click="$router.back()">返回</el-button>
      <div class="name">{{ detailInfo?.name }}</div>
      <div class="btn-tool">
        <el-button :loading="saveLoading" @click="onSave">保存</el-button>
        <el-button type="primary" :loading="submitLoading" @click="onSubmit">发布</el-button>
      </div>
    </div>
    <div class="frame" v-loading="loading">
      <div class="left">
        <el-tabs stretch v-model="columnTab">
          <el-tab-pane label="组件" name="column">
            <div class="form-title">基础组件</div>
            <div class="form-column">
              <div class="form-column-li" v-for="item in Fields"
                   :key="item"
                   :draggable="true"
                   @dragstart="addItemByStartDrag($event, item)"
                   :droppable="true" @dragover.prevent
                   @click="addColumn(item)">
                <div class="form-column-li-label">{{ item.label }}</div>
              </div>
            </div>
            <div class="form-title">高级组件</div>
            <div class="form-column">
              <div class="form-column-li" v-for="item in HighFields"
                   :key="item"
                   :draggable="true"
                   @dragstart="addItemByStartDrag($event, item)"
                   :droppable="true" @dragover.prevent
                   @click="addColumn(item)">
                <div class="form-column-li-label">{{ item.label }}</div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="大纲" name="outline">
            <template v-if="_config.form?.columns.length">
              <div class="outline-li" :class="activeField?.prop===item.prop?'is-active':''"
                   @click="activeField=item;settingTab='field'"
                   :key="item"
                   v-for="item in _config.columns">
                <div class="outline-li-name">{{ item.label }}</div>
                <div class="outline-li-tool">
                  <el-icon @click="onRemoveItem(item, $event)" v-if="!item.isFix && !item.isSubmit">
                    <Close/>
                  </el-icon>
                </div>
              </div>
            </template>
            <el-empty v-else description="暂无字段" style="height: 100%;" :image-size="50"/>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="center">
        <div class="form-panel" :droppable="true" @drop="onDropFormPanel" @dragover.prevent>
          <el-form :size="_config.form?.size"
                   ref="formRef"
                   :validate-on-rule-change="false"
                   :label-width="_config.form.labelWidth + 'px'"
                   :label-position="_config.form.labelPosition">
            <el-row :gutter="_config.form?.gutter">
              <template v-for="item in _config.form?.columns || []" :key="item">
                <el-col :span="item.span || 12">
                  <div class="form-panel-item"
                       :draggable="true"
                       :droppable="true"
                       @dragover.prevent
                       @drop="onDropFieldItem($event, item)"
                       @dragstart="moveItemByStartDrag($event, item)"
                       @dragover="onDragEnterFieldItem($event, item)"
                       @dragenter="onDragEnterFieldItem($event, item)"
                       @dragleave="onDragLeaveFieldItem($event, item)"
                       :class="[dropFieldKey===item.prop?'isDrop':'', activeField?.prop===item.prop?'is-active':'']"
                       @click="activeField=item;settingTab='field'">
                    <div class="form-panel-item-split" :class="item.class" v-if="item.type === ColumnTypeVo.divider">
                      <div class="column-split-font" v-if="item.label">{{ item.label }}</div>
                    </div>
                    <el-form-item v-else :label="item.label" :prop="item.prop"
                                  :class="[item.hideLabel?'hidden-label':'', item.hidden?'is-hidden':'']">
                      <template #label><font v-if="item.required"
                                             style="color: red;font-weight: bold;margin-right: 5px">*</font>{{
                          item.label
                        }}
                      </template>
                      <div class="form-panel-item-input">
                        <formItem :column="item"/>
                      </div>
                    </el-form-item>
                    <div class="form-panel-item-tool" v-if="item.isCustom">
                      <div class="form-panel-item-tool-bar" v-if="!item.isSystem"
                           @click="onRemoveItem(item, $event)">
                        <el-icon>
                          <Close/>
                        </el-icon>
                      </div>
                      <div class="form-panel-item-tool-bar is-hidden" v-if="item.hidden">已隐藏</div>
                    </div>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="right">
        <el-tabs stretch v-model="settingTab">
          <el-tab-pane label="表单" name="form">
            <formSetting v-model="_config.form"/>
          </el-tab-pane>
          <el-tab-pane label="组件" name="field">
            <fieldSetting v-model="_config.form.columns[getActiveIndex]" v-if="getActiveIndex > -1"/>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as MainApi from '@/api/system/customForm/index'
import {ref} from 'vue'
import {ConfigBO, setComposeColumn} from "@/components/Zeadoor/CustomForm/config";
import {Fields, HighFields} from "@/components/Zeadoor/CustomForm/field";
import {Close} from "@element-plus/icons-vue";
import {ColumnTypeVo} from "@/components/Zeadoor/interface";
import {generateUUID} from "@/utils";
import {merge} from "lodash-es";
import formItem from "./components/formItem.vue"
import fieldSetting from "./components/fieldSetting.vue"
import formSetting from "./components/formSetting.vue"

const {params} = useRoute() // 查询参数
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

/** 查询列表 */
const loading = ref(false)
const detailInfo = ref<MainApi.CustomFormVO>({})
const getDetail = async () => {
  loading.value = true
  try {
    detailInfo.value = await MainApi.get(params.id)
    if(detailInfo.value.config) {
      _config.value = merge({}, _config.value, JSON.parse(detailInfo.value.config))
    }
  } finally {
    loading.value = false
  }
}

/** 保存 */
const saveLoading = ref(false)
const onSave = async () => {
  saveLoading.value = true
  try {
    await MainApi.update(Object.assign({}, detailInfo.value, {
      config: JSON.stringify(_config.value)
    }))
    message.success("保存成功")
  } finally {
    saveLoading.value = false
  }
}

/** 保存 */
const submitLoading = ref(false)
const onSubmit = async () => {
  try {
    await message.confirm("确定发布吗？")
    submitLoading.value = true
    await MainApi.submit(Object.assign({}, detailInfo.value, {
      config: JSON.stringify(_config.value)
    }))
    message.success("发布成功")
  } finally {
    submitLoading.value = false
  }
}

const settingTab = ref<string>("form")
const columnTab = ref<string>('column')
const _config = ref<ConfigBO>({
  form: {
    size: "default",
    labelWidth: 100,
    labelPosition: "left",
    gutter: 20,
    columns: []
  }
});
const activeField = ref<any>()
const dropFieldKey = ref<string>()

/**
 * 初始化新字段
 * @param item
 */
const createField = (item: any): any => {
  let filed = JSON.parse(JSON.stringify(item))
  filed.prop = generateUUID()
  // 是否必填
  filed.required = false
  // 栅格数量
  filed.span = 12
  // 列表宽度
  filed.width = 200
  // 是否用户自定义字段
  filed.isCustom = true
  setComposeColumn(filed)
  return filed
}

/**
 * 新增表单字段
 * @param item
 */
const addColumn = (item: any) => {
  let newField = createField(item)
  _config.value.form.columns.push(newField)
  activeField.value = newField
  settingTab.value = "field"
}

const addItemByStartDrag = (event: any, item: any) => {
  event.dataTransfer.setData('method', "add")
  event.dataTransfer.setData('field', JSON.stringify(item))
}

const moveItemByStartDrag = (e: any, item: any) => {
  e.dataTransfer.setData('method', "move")
  e.dataTransfer.setData('prop', item.prop)
}

const onDragEnterFieldItem = (e: any, item: any) => {
  dropFieldKey.value = item.prop
}
const onDragLeaveFieldItem = (e: any) => {
  dropFieldKey.value = ""
}
const onDropFormPanel = (e: any) => {
  let prop = e.dataTransfer.getData('prop')
  let method = e.dataTransfer.getData('method')
  let field = e.dataTransfer.getData('field')

  let newField = createField(field ? JSON.parse(field) : null)
  if (method === "add") {
    _config.value.form.columns.push(newField)
  } else if (method === "move") {
    let index = _config.value.form.columns.findIndex(field => {
      return field.prop.toString() === prop.toString()
    })
    newField = _config.value.form.columns.find(field => {
      return field.prop.toString() === prop.toString()
    })
    _config.value.form.columns.splice(index, 1)
    _config.value.form.columns.push(newField)
  }
  dropFieldKey.value = ""
  activeField.value = newField
  e.stopPropagation()
}
const onDropFieldItem = (e: any, item: any) => {
  let prop = e.dataTransfer.getData('prop')
  let method = e.dataTransfer.getData('method')
  let field = e.dataTransfer.getData('field')

  let newIndex = _config.value.form.columns.findIndex(field => {
    return item.prop.toString() === field.prop.toString()
  })
  let curItem: any = null
  if (method === "add") {
    curItem = createField(field ? JSON.parse(field) : null)
  } else if (method === "move") {
    let index = _config.value.form.columns.findIndex(field => {
      return field.prop.toString() === prop.toString()
    })
    curItem = _config.value.form.columns.find(field => {
      return field.prop.toString() === prop.toString()
    })
    _config.value.form.columns.splice(index, 1)
    if (index < newIndex) {
      newIndex = newIndex - 1
    }
  }
  _config.value.form.columns.splice(newIndex, 0, curItem)
  dropFieldKey.value = ""
  activeField.value = curItem
  e.stopPropagation()
}

const onRemoveItem = function (item: Field, e: any) {
  if (activeField.value && activeField.value.prop === item.prop) {
    activeField.value = null
  }
  _config.value.form.columns.splice(_config.value.form.columns.findIndex(t => t.prop === item.prop), 1)
  e.stopPropagation()
}

const getActiveIndex = computed(() => {
  if (activeField.value) {
    return _config.value.form?.columns.findIndex((t: any) => t.prop === activeField.value.prop)
  }
  return -1
})

onMounted(() => {
  getDetail()
})
</script>
<style lang="scss" scoped>
.frame {
  width: 100%;
  display: flex;
  flex: 1;

  .left,
  .right {
    width: 250px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    background: #f4f8fe;
    border-radius: 10px;
    border: 1px solid var(--el-border-color);

    :deep(.el-tabs) {
      height: 100%;

      .el-tabs__content {
        padding: 0 10px;
        overflow: auto;
      }

      .el-tab-pane {
        height: 100%;
      }
    }

  }

  .left {
    .form-title {
      font-size: 14px;
    }

    .form-column {
      display: flex;
      flex-flow: wrap row;
      padding: 10px 0;
      max-height: calc(100% - 20px);
      overflow: auto;
      margin-bottom: 10px;
      justify-content: space-between;

      .form-column-li {
        border-radius: 5px;
        cursor: pointer;
        color: var(--font-hb);
        width: calc(50% - 7px);
        text-align: center;
        background: #fff;
        margin: 0 0px 10px 0px;
        border: 1px dashed #eee;
        position: relative;

        &.is-add {
          border-style: solid;

          &:hover {
            .form-column-li-remove {
              display: flex;
              justify-content: center;
              align-items: center;
              color: #fff;
            }
          }
        }

        &.is-add,
        &:hover {
          color: #1561e8;
          border-color: #1561e8;
        }

        .form-column-li-label {
          font-size: 12px;
          height: 30px;
          line-height: 30px;
        }

        .form-column-li-remove {
          display: none;
          position: absolute;
          background: rgba(0, 0, 0, .5);
          width: 100%;
          top: 0;
          left: 0;
          height: 100%;
        }
      }
    }

    .outline-li {
      background: #fff;
      padding: 8px 10px;
      border-radius: 5px;
      cursor: pointer;
      border: 1px dashed #eee;
      margin-bottom: 5px;
      display: flex;
      align-items: center;

      &.is-active,
      &:hover {
        color: #1561e8;
        border-color: #1561e8;
      }

      .outline-li-name {
        font-size: 12px;
        flex: 1;
        color: #5b5b5b;
      }

      .outline-li-tool {
        color: #9f9f9f;
      }
    }
  }

  .right {
    width: 300px;
  }

  .center {
    flex: 1;
    width: 0;

    .form-panel {
      height: 100%;
      background: #fff;
      margin: 0 10px;
      overflow: auto;
      border: 1px solid rgb(221, 223, 230);
      border-radius: 10px;
      padding: 10px;

      .el-form {
        width: 100%;

        .el-form-item {
          margin-bottom: 0;
        }
      }

      .form-panel-item {
        padding: 10px 0;
        margin-bottom: 0px;
        position: relative;
        cursor: pointer;
        height: fit-content;

        &:hover {
          .form-panel-item-tool {
            display: block !important;
          }
        }

        .form-panel-item-split {
          border-bottom: 1px dashed #eee;
          padding: 10px 0 10px 0;
          font-size: 15px;
        }

        .form-panel-item-tool {
          height: 100%;
          position: absolute;
          top: 0px;
          right: 0px;
          display: none;
          z-index: 1;

          .form-panel-item-tool-bar {
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            background: red;
            margin: 0 0 5px 5px;
            color: #fff;
            cursor: pointer;
            border-radius: 3px;

            &.is-hidden {
              width: 80px;
              color: #000;
              background: #eee;
            }
          }
        }

        .form-panel-item-input {
          width: 100%;
        }

        &.isDrop {
          &:before {
            content: "";
            width: 100%;
            height: 2px;
            background: #1e80ff;
            position: absolute;
            top: 0;
            left: 0;
          }
        }

        &:hover:after,
        &.is-active:after {
          content: "";
          border: 1px dashed #1e80ff;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          pointer-events: none;
        }

        &.is-active:after {
          border-style: solid;
        }

        :deep(.form-panel-item-input) {
          display: flex;
          align-items: center;
          width: 100%;

          .el-input,
          .el-input-number,
          .el-select,
          .el-cascader,
          .tox-tinymce {
            width: 100%;
          }
        }

        :deep(.el-form-item) {
          .el-form-item__content {
          }

          &.hidden-label {
            > .el-form-item__label {
              display: none;
            }
          }

          &.is-hidden {
            opacity: 0.3;
          }
        }
      }
    }
  }
}
</style>
