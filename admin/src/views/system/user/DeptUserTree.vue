<template>
  <div class="head-container" v-loading="loading">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门/人员名称">
      <template #prefix>
        <Icon icon="ep:search"/>
      </template>
    </el-input>
    <el-tree
        ref="treeRef"
        :data="deptList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="defaultProps"
        default-expand-all
        highlight-current
        node-key="id"
        @node-click="handleNodeClick">
      <template #default="{ data }">
        <div class="flex" style="align-items: center">
          <div class="" style="color: var(--el-color-warning)" v-if="data.type === 'dept'"><el-icon><Briefcase /></el-icon></div>
          <el-avatar v-else :src="data.avatar || defaultAvatar" :size="15"/>
          <div class="m-l-2">{{data.name}}</div>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import {ElTree} from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import {defaultProps, handleTree} from '@/utils/tree'
import * as UserApi from "@/api/oa/personnel/user/user";
import {Briefcase} from "@element-plus/icons-vue";
import defaultAvatar from "@/assets/imgs/avatar.jpg";

defineOptions({name: 'SystemUserDeptTree'})

const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()
const userList = ref<any[]>([])
const loading = ref(true)

/** 获得部门树 */
const getTree = async () => {
  loading.value = true
  const res = await DeptApi.getSimpleDeptList()
  userList.value = await UserApi.getBookList()
  deptList.value = []
  deptList.value.push(...handleTree(res))
  handleAppendUserTree(deptList.value)
  loading.value = false
}

const handleAppendUserTree = (dataList) => {
  dataList.forEach((t: any) => {
    t.type = "dept"
    if (!t.children) t.children = []
    if (t.children && t.children) {
      handleAppendUserTree(t.children)
    }
    t.children = t.children.concat(userList.value.filter((s: any) => s.deptId === t.id).map((s: any) => {
      return {
        id: s.id,
        name: s.nickname,
        avatar: s.avatar,
        type: 'user'
      }
    }))
  })
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
