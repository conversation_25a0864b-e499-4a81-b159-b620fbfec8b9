<template>
  <ZListContainer>
    <template #search>
      <ZListContainerSearch v-model="queryParams" @search="handleQuery">
        <template #search-fast>
          <el-form-item label="类型" prop="specificExecuteType">
            <el-select v-model="queryParams.specificExecuteType" placeholder="请选择类型" clearable
                       class="!w-60 l-10" style="margin-left: 10px">
              <el-option
                :label="dict.label"
                :value="dict.value"
                v-for="dict in getIntDictOptions(DICT_TYPE.IMPORT_EXPORT_TYPE)"
                :key="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="dateRange" class="m-l-5">
            <el-date-picker
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              end-placeholder="结束日期"
              style="margin-left: 10px"
              v-model="queryParams.dateRange"/>
          </el-form-item>
        </template>
      </ZListContainerSearch>
    </template>
    <template #table>
      <ZTable v-loading="loading" :data="list" row-key="id">
        <el-table-column label="类型" prop="specificExecuteType" width="150px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.IMPORT_EXPORT_TYPE" :value="scope.row.specificExecuteType"/>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.IMPORT_STATUS" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="导出数量" prop="allNum" width="120px" align="center">
          <template #default="scope">
            {{ scope.row.allNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="结果文件" prop="resultFile" min-width="200px">
          <template #default="scope">
            <div class="is-link" @click="openWindow(scope.row.resultFile.url)">{{ scope.row.resultFile?.name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" prop="createName" width="100px"/>
        <el-table-column label="开始时间" prop="startTime" width="200px"/>
        <el-table-column label="完成时间" prop="endTime" width="200px"/>
        <el-table-column label="创建时间" prop="createTime" min-width="200px"/>
      </ZTable>
    </template>
    <template #page>
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </template>
  </ZListContainer>s
</template>

<script lang="ts" setup>
import * as ExportVOApi from '@/api/system/export/index'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";
import {ref} from 'vue';
import dayjs from "dayjs";
import {openWindow} from "@/utils/filt";

defineOptions({name: 'SystemExport'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = ref({
  specificExecuteType: "",
  dateRange: [],
  startTime: "",
  endTime: ""
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    if (queryParams.value.dateRange && queryParams.value.dateRange.length) {
      queryParams.value.startTime = dayjs(queryParams.value.dateRange[0]).format("YYYY-MM-DD HH:mm:ss")
      queryParams.value.endTime = dayjs(queryParams.value.dateRange[1]).format("YYYY-MM-DD HH:mm:ss")
    }
    let res = await ExportVOApi.getExportPage(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
