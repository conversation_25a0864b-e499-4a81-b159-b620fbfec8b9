<template>
  <ZListContainer>
    <template #header>
      <el-tabs v-model="type">
        <el-tab-pane label="导入列表" name="import"/>
        <el-tab-pane label="导出列表" name="export"/>
      </el-tabs>
    </template>
    <template #main>
      <ImportList v-if="type === 'import'" />
      <ExportList v-if="type === 'export'" />
    </template>
  </ZListContainer>
</template>

<script lang="ts" setup>
import ExportList from "./export/index.vue"
import ImportList from "./import/index.vue"

const type = ref("import")
</script>

<style lang="scss" scoped>
.zeadoor-list-container{
}
</style>
