<template>
  <div class="detail-page">
    <div class="top">
      <div class="info" v-if="companyInfo">
        <div class="info-avatar">
          <CropperAvatar
            ref="cropperRef"
            :btnProps="{ preIcon: 'ant-design:cloud-upload-outlined' }"
            :showBtn="false"
            :value="companyInfo.logo"
            width="120px"
            @change="handelUpload"
            :path="ExtraPathEnum.TEAMIFY"
          />
        </div>
        <div class="info-name">{{ companyInfo?.companyName }}</div>
        <div class="info-tag">
          <div class="info-tag-span" v-if="companyInfo.companySize">
            <dict-tag
              cssClass="text"
              :type="DICT_TYPE.TEAMIFY_COMPANY_SIZE"
              :value="companyInfo.companySize"
            />
          </div>
          <div class="info-tag-span" v-if="companyInfo.areaName">
            {{ companyInfo.areaName }}
          </div>
          <div class="info-tag-span" v-if="companyInfo.industryId">
            <dict-tag
              cssClass="text"
              :type="DICT_TYPE.TEAMIFY_COMPANY_INDUSTRY"
              :value="companyInfo.industryId"
            />
          </div>
        </div>
      </div>
      <div class="menu">
        <el-tabs v-model="tabKey">
          <el-tab-pane label="基本信息" :name="1" />
          <el-tab-pane label="订单与支付(待开发)" :name="2" />
          <el-tab-pane label="企业权益(待开发)" :name="3" />
        </el-tabs>
      </div>
    </div>
    <div class="main">
      <BasePanel v-if="tabKey === 1" @success="getDetail" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import * as TenantApi from '@/api/tenant/index'
import { DICT_TYPE } from '@/utils/dict'
import { CropperAvatar } from '@/components/Cropper'
import { ExtraPathEnum } from '@/components/UploadFile/src/extraPathEnum'
import BasePanel from './detail/base.vue'

const tabKey = ref(1)
const loading = ref(false)
const companyInfo = ref<any>({})

/** 打开弹窗 */
const getDetail = async () => {
  loading.value = true
  try {
    companyInfo.value = await TenantApi.getCompanyDetail()
  } finally {
    loading.value = false
  }
}

const cropperRef = ref()
const handelUpload = async (data) => {
  await TenantApi.updateCompanyLogo({ logo: data, id: companyInfo.value.id })
  cropperRef.value.close()
  getDetail()
}

onMounted(() => {
  getDetail()
})
</script>
<style lang="scss" scoped></style>
