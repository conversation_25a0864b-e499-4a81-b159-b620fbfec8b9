<template>
  <div class="form-panel" :class="isOpen ? 'is-open' : ''">
    <div class="form-panel-title">
      <div class="form-panel-title-label">
        <el-icon>
          <CaretRight />
        </el-icon>
        基本信息
      </div>
      <div class="form-panel-title-btn">
        <el-button type="primary" link @click="readonly = false">编辑</el-button>
      </div>
    </div>
    <div class="form-panel-center" v-if="isOpen">
      <div class="form-panel-center-form no-border" :class="readonly ? '' : 'is-edit'">
        <el-form
          ref="formRef"
          v-loading="formLoading"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="right"
        >
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="企业名称" prop="companyName">
                <el-input
                    v-model="formData.companyName"
                    placeholder="请输入企业名称"
                    maxlength="50"
                    v-if="!readonly"
                />
                <span v-else>{{ formData.companyName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业全称" prop="companyFullName">
                <el-input
                    v-model="formData.companyFullName"
                    placeholder="请输入企业全称"
                    maxlength="50"
                    v-if="!readonly"
                />
                <span v-else>{{ formData.companyFullName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业规模" prop="companySize">
                <el-select
                  v-model="formData.companySize"
                  placeholder="请选择企业规模"
                  default-first-option
                  v-if="!readonly"
                >
                  <el-option
                    :label="dict.label"
                    :value="dict.value"
                    v-for="dict in getIntDictOptions(DICT_TYPE.TEAMIFY_COMPANY_SIZE)"
                    :key="dict.value"
                  />
                </el-select>
                <dict-tag
                  v-else
                  cssClass="text"
                  :type="DICT_TYPE.TEAMIFY_COMPANY_SIZE"
                  :value="formData.companySize"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业行业" prop="industryId">
                <el-select
                  v-model="formData.industryId"
                  placeholder="请选择企业行业"
                  default-first-option
                  v-if="!readonly"
                >
                  <el-option
                    :label="dict.label"
                    :value="dict.value"
                    v-for="dict in getIntDictOptions(DICT_TYPE.TEAMIFY_COMPANY_INDUSTRY)"
                    :key="dict.value"
                  />
                </el-select>
                <dict-tag
                  v-else
                  cssClass="text"
                  :type="DICT_TYPE.TEAMIFY_COMPANY_INDUSTRY"
                  :value="formData.industryId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所在城市" prop="areaId">
                <el-cascader
                  v-model="formData.areaId"
                  :options="areaList"
                  :props="defaultProps"
                  class="w-1/1"
                  clearable
                  filterable
                  placeholder="请选择所在城市"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.areaName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="信用代码" prop="creditCode">
                <el-input
                  v-model="formData.creditCode"
                  placeholder="请输入企业社会统一信用代码"
                  maxlength="50"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.creditCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业电话" prop="creditMobile">
                <el-input
                  v-model="formData.creditMobile"
                  placeholder="请输入企业电话"
                  maxlength="50"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.creditMobile }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人姓名" prop="legalPersonName">
                <el-input
                  v-model="formData.legalPersonName"
                  placeholder="请输入法人姓名"
                  maxlength="50"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.legalPersonName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人身份证号" prop="legalIdNo">
                <el-input
                  v-model="formData.legalIdNo"
                  placeholder="请输入法人身份证号"
                  maxlength="50"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.legalIdNo }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人手机号" prop="legalMobile">
                <el-input
                  v-model="formData.legalMobile"
                  placeholder="请输入法人手机号"
                  maxlength="50"
                  v-if="!readonly"
                />
                <span v-else>{{ formData.legalMobile }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业logo" prop="logo">
                <UploadImg
                  :disabled="readonly"
                  :is-show-tip="false"
                  v-model="formData.logo"
                  :path="ExtraPathEnum.TEAMIFY"
                  :limit="1"
                  class="upload-image"
                  v-if="!readonly || formData.logo"
                />
                <el-image class="empty-photo" v-else :src="defaultPhoto" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="营业执照" prop="businessLicense">
                <UploadImg
                  :disabled="readonly"
                  :is-show-tip="false"
                  v-model="formData.businessLicense"
                  :path="ExtraPathEnum.TEAMIFY"
                  :limit="1"
                  class="upload-image"
                  v-if="!readonly || formData.businessLicense"
                />
                <el-image class="empty-photo" v-else :src="defaultPhoto" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="form-panel-center-btn" v-if="!readonly">
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="updateLoading"
          :loading="updateLoading"
          >保存
        </el-button>
        <el-button @click="getDetail">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CaretRight } from '@element-plus/icons-vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as UserApi from '@/api/oa/personnel/user/user'
import { defaultProps } from '@/utils/tree'
import * as TenantApi from '@/api/tenant/index'
import { ExtraPathEnum } from '@/components/UploadFile/src/extraPathEnum'
import defaultPhoto from '@/assets/imgs/empty-image.png'
import * as AreaApi from '@/api/system/area'

const isOpen = ref(true)
const readonly = ref(true)
const message = useMessage() // 消息弹窗
const emits = defineEmits(['success'])

const { t } = useI18n() // 国际化
const formData = ref<UserApi.UserVO>({})
const areaList = ref([]) // 地区列表
const formRules = reactive({
  companyName: [{ required: true, message: '企业名称不能为空', trigger: ['blur', 'change'] }],
  companySize: [{ required: true, message: '企业规模不能为空', trigger: ['blur', 'change'] }],
  industryId: [{ required: true, message: '企业行业不能为空', trigger: ['blur', 'change'] }],
  areaId: [{ required: true, message: '所在城市不能为空', trigger: ['blur', 'change'] }],
  creditCode: [{ required: true, message: '信用代码不能为空', trigger: ['blur', 'change'] }],
  legalMobile: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: t('profile.rules.truephone'),
      trigger: ['blur', 'change']
    }
  ]
})
const formRef = ref() // 表单 Ref
const formLoading = ref(false)
const updateLoading = ref(false)

const getDetail = async () => {
  readonly.value = true
  formLoading.value = true
  try {
    formData.value = await TenantApi.getCompanyDetail()
  } finally {
    formLoading.value = false
  }
}

const submitForm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  updateLoading.value = true
  try {
    await TenantApi.updateCompany(formData.value)
    message.success(t('common.updateSuccess'))
    await getDetail()
    emits("success")
  } finally {
    updateLoading.value = false
  }
}

onMounted(async () => {
  getDetail()
  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
})
</script>

<style scoped lang="scss"></style>
