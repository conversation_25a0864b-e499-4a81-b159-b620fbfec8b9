.z-detail-drawer {
  .el-drawer__header {
  }
  .z-detail-drawer-header {
    flex-flow: wrap row;
    .tool{
      display: flex;
      align-items: center;
    }
  }
  .el-drawer__body{
    padding-top: 10px;
  }
  .detail-main{
    .detail-main-create{
      display: flex;
      align-items: center;
      padding:0 0 10px 0;
      border-radius: 8px;
      .el-button{
        margin-right: 5px;
        .el-icon{
          margin-right: 3px;
        }
      }
    }
  }
}
.follow-section{
  margin-top: 20px;
  .zeadoor-list-container{
    .zeadoor-list-container-main{
      .el-card__body{
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
      .el-tabs__item{
        &.is-top{
          &.is-active{
            background: var(--el-color-primary) !important;
            color: #fff;
            border-radius: 5px !important;
          }
        }
      }
    }
  }
}

.custom-page {
  display: flex;
  flex-direction: column;
  .page-header {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
  .page-center{
    height: 0;
  }
}