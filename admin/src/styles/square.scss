.square-list-main{
  width: 1200px;
  margin: 0 auto;
  .menu{
    width: 100%;
    display: flex;
    align-items: center;
    .menu-list{
      flex: 1;
      display: flex;
      align-items: center;
      flex-flow: wrap row;
      margin: 7px 0;
      overflow: hidden;
      height: 42px;
      .menu-list-item{
        height: 32px;
        margin: 5px 12px 5px 0;
        padding: 0 10px;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #595959;
        cursor: pointer;
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        &.is-active{
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          font-weight: bold;
          border-color: transparent;
        }
      }
    }
    .menu-more{
      font-size: 12px;
      display: flex;
      align-items: center;
    }
  }
  .package{
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    .package-li{
      background: #FFFFFF;
      box-shadow: 0 2px 4px 0 #0000001a;
      border-radius: 8px;
      overflow: hidden;
      .package-li-img{
        width: 100%;
        height: 150px;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .package-li-name{
        font-size: 16px;
        margin: 10px 0 10px 0;
        padding: 0 20px;
      }
      .package-li-desc{
        color: var(--el-text-color-secondary);
        font-size: 12px;
        padding: 0 20px;
        height: 35px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5; /* 可选，设置行高 */
      }
      .package-li-footer{
        display: flex;
        align-items: center;
        padding: 10px 20px 20px 20px;
        .package-li-footer-tag{
          flex: 1;
          white-space: nowrap;      /* 禁止换行 */
          overflow: hidden;         /* 隐藏溢出内容 */
          text-overflow: ellipsis;  /* 显示省略号 */
          display: block;           /* 块级元素或inline-block */
          width: 100%;              /* 需要指定宽度 */
          .el-tag{
            margin-right: 5px;
          }
        }
        .package-li-footer-count{
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}