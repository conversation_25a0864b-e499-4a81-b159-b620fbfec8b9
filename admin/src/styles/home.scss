.is-dept {
  flex: 1;

  .model-center {
    height: 300px;
  }
}

.is-notice {
  width: 380px;
  margin-left: 20px;
  flex-shrink: 0;
}

.dept-container {
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  font-weight: bold;
  padding: 8px 15px;
  border-radius: 5px;
  font-size: 14px;
  word-break: keep-all;

  &.is-first {
    background-image: linear-gradient(180deg, #629EFF 0%, #0148E6 100%);
    color: #fff;
  }
}

.is-calendar {
  flex: 1;

  .model-center {
    padding-bottom: 0;

    .is-calendar-plug {
      :deep(.el-calendar) {
        .el-calendar__header {
          font-size: 12px;
          padding: 0 0 10px 0;
        }

        .el-calendar__body {
          padding: 10px 0;

          thead th {
            color: var(--el-color-info);
            font-size: 12px;
          }
        }

        .el-calendar-table__row {
          td {
            border: 0;
          }

          .el-calendar-day {
            height: 40px;
            font-size: 12px;
            text-align: center;

            .calendar-is-out-time {
              width: 4px;
              height: 4px;
              background-color: var(--el-color-danger);
              border-radius: 100px;
              margin: 4px auto 0 auto;
            }
          }
        }
      }
    }

    .is-calendar-list {
      border-top: 1px solid var(--el-border-color-extra-light);
      padding-top: 10px;

      .todo-item {
        display: flex;
        font-size: 12px;
        padding: 5px 0;
        align-items: center;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }

        .todo-item-level {
          width: 14px;
          height: 14px;
          border-radius: 3px;

          &.a {
            background-color: var(--el-color-success);
          }

          &.b {
            background-color: var(--el-color-warning);
          }

          &.c {
            background-color: var(--el-color-danger);
          }
        }

        .todo-item-title {
          flex: 1;
          margin-left: 10px;
        }

        .todo-item-time {
          width: 6px;
          height: 6px;
          background-color: var(--el-color-danger);
          border-radius: 100px;
        }
      }
    }
  }
}

:deep(.tree-org-node__inner) {
  box-shadow: 0 0 0 !important;
}

:deep(.zm-tree-org) {
  padding: 0 !important;
}

.notice-li {
  font-size: 12px;
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
  cursor: pointer;

  &:last-child:after {
    display: none;
  }

  &:after {
    content: "";
    width: 2px;
    border: 1px dashed #d4eaff;
    height: 100%;
    position: absolute;
    top: 3px;
    left: 5px;
  }

  &:before {
    content: "";
    width: 12px;
    height: 12px;
    background: #1880FF;
    position: absolute;
    top: 3px;
    left: 0;
    border-radius: 100px;
    border: 3px solid #dbe7ff;
    z-index: 1;
  }

  .notice-li-time {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .notice-li-name {
    background: var(--el-color-primary-light-9);
    padding: 10px 15px;
    border-radius: 5px;
    margin-top: 10px;
  }
}

.banner-count {
  background-image: url("@/assets/imgs/admin-home-bg1.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom;
}