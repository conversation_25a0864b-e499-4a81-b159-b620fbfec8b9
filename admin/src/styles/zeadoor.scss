.v-body-scrollbar {
  .el-scrollbar__view {
    .v-body-breadcrumb {
      margin: 15px 0 0 10px;
    }

    .v-body-app-view {
    }
  }
}

.form-split {
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  padding: 10px 20px 30px 40px;
  position: relative;
  color: #000;

  &.line {
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px dashed var(--el-border-color);
    padding-left: 0;

    &:before {
      display: none;
    }
  }

  &:before {
    content: "";
    width: 4px;
    height: 20px;
    border-radius: 100px;
    background: var(--el-color-primary);
    left: 20px;
    top: 12px;
    position: absolute;
  }

  .label {
  }

  .tip {
    font-size: 12px;
    font-weight: 500;
    margin-top: 10px;
    color: var(--el-text-color-regular);
  }
}

.table-custom {
  height: 100%;
  overflow: auto;
}

.table-column-tool {
  display: flex;
  align-items: center;

  .table-column-tool-more {
    padding: 0 10px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

.table-custom-column {
  display: flex;
  padding: 20px 20px;
  border-bottom: 1px dashed var(--el-border-color-lighter);
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }

  &:last-child {
    border: 0;
  }

  .table-custom-column-checkbox {
    padding-right: 20px;
  }

  .table-custom-column-center {
    flex: 1;

    .avatar {
      margin-right: 20px;

      img {
        width: 35px;
        height: 35px;
        border-radius: 5px;
      }
    }

    .name {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      align-items: center;

      .el-tag {
        margin-left: 10px;
      }
    }

    .label {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-top: 10px;
      display: flex;
      align-items: center;
    }

    .title {
      font-size: 16px;
    }
  }

  .table-custom-column-tool {
    display: flex;
  }
}

.dict-tag[cssClass='text'] span {
  border: 0 !important;
  color: var(--el-text-color);
  padding: 0;
  background: transparent !important;
  font-size: inherit !important
}

.table-instructions {
  margin-bottom: 10px;
  background: #fdf6ec;
  color: #e6a23c;
  padding: 10px 20px;
  border: 1px solid #e6a23c;
  border-radius: 5px;

  .table-instructions-title {
    font-size: 16px;
    padding: 0 0 10px;
    font-weight: 700;
  }

  .table-instructions-content {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.bg-table {
  background: var(--el-bg-color-page) !important;
}

.asset-count-footer {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 20px 0;

  .asset-count-footer-span {
    margin-right: 50px;

    font {
      color: var(--el-color-warning);
      font-weight: bold;
    }
  }
}

.cell {
  .el-form-item {
    margin-bottom: 0;
  }
}

.column-money {
  font-weight: bold;
}

.el-dialog {
  background-image: url("@/assets/imgs/dialog-bg.png");
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;

  .el-dialog__header {
    border-bottom: 0 !important;
    height: 40px !important;
  }
}

.form-rule-panel {
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
  margin-bottom: 10px;

  .form-rule-panel-title {
    background-color: #f8f8f9;
    padding: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;

    .name {
      flex: 1;
    }

    .delete {
      cursor: pointer;

      &:hover {
        color: var(--el-color-danger);
      }
    }
  }

  .form-rule-panel-center {
    padding: 20px;
  }
}

.el-table__header {
  .el-table__cell {
    color: #000;
  }
}

.is-full-dialog {
  margin: 20px auto;
  height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

/*表单中常用详细设置面板样式*/
.-common-form-card {
  width: 100%;
  background: var(--el-border-color-extra-light);
  border-radius: 10px;
  padding: 10px 20px 20px 20px;
}

.el-form {
  .el-radio {
    margin-right: 15px;
  }
}

.form-column-radio {
  .el-radio {
    width: 100%;
  }
}

.z-detail-drawer {

  .z-detail-drawer-header{
    display: flex;
    align-items: center;
    .title{
      flex: 1;
      display: flex;
      align-items: center;
      font-weight: bold;
      color: var(--el-text-color-primary);
      .title-icon{
        margin-right: 8px;
      }
    }
    .tool{
      margin-right: 30px;
    }
  }

  .el-drawer__header {
    margin-bottom: 0px;

    .z-detail-drawer-head {
      display: flex;
      align-items: center;

      .tag {
        margin-right: 10px;
      }

      .name {
      }

      .btn {
        margin-right: 10px;
        display: flex;
        align-items: center;
        .btn-li{
          padding: 5px 6px;
          cursor: pointer;
          &:hover{
            color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .el-drawer__close-btn {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .tabs-panel {
    .zeadoor-list-container {
      height: auto !important;
      .zeadoor-list-container-search {
        .el-card {
          margin-bottom: 0;
        }
      }
    }

    .el-card {
      &:hover {
        box-shadow: 0 0 0 !important;
      }

      .el-card__body {
        padding: 10px 0 !important;
      }
    }
  }
}

.z-form-detail {
  .el-form-item {
    margin-bottom: 10px;

    .el-form-item__label {
      color: #9aa6b2 !important;
    }
    .edit-icon{
      display: none;
      cursor: pointer;
      color: var(--el-text-color-secondary);
      margin: 0 0 0 10px;
      &:hover{
        color: var(--el-color-primary);
      }
    }
    &:hover{
      .edit-icon{
        display: block;
      }
    }
  }
  .top-remark{
    background: var(--el-bg-color-page);
    font-size: 12px;
    padding: 10px 15px;
    border-radius: 10px;
    color: var(--el-text-color-secondary);
    margin-bottom: 10px;
  }
}

.body-center {
  width: 1200px;
  margin: 0 auto;
  position: relative;
}

.is-link {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.is-hover-link {
  cursor: pointer;

  &:hover {
    color: var(--el-color-primary) !important;
  }
}

/*选择工资项*/
.salary-field {
  &.center {
    width: 100%;

    .center-alter {
      display: flex;
      align-items: center;
      color: var(--el-text-color-placeholder);

      .center-alter-label {
        flex: 1;
      }
    }

    .center-table {
      width: 100%;
      margin-top: 20px;
      border: 1px solid var(--el-border-color);
      padding: 10px 20px;
      border-radius: 10px;
      height: 60vh;
      overflow: auto;

      .field-li {
        display: flex;
        padding: 5px 0;
        align-items: baseline;

        .field-li-label {
          width: 120px;
          flex-shrink: 0;
        }

        .field-li-list {
          display: flex;
          align-items: center;
          flex-flow: wrap row;

          .field-li-list-item {
            margin: 0 10px 10px 0;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;

            .check-mark {
              position: absolute;
              right: 0;
              bottom: 0;
              width: 0;
              height: 0;
              border-bottom: 16px solid var(--el-color-primary);
              border-left: 16px solid transparent;
              display: flex;
              align-items: flex-end;
              justify-content: flex-start;
              color: white;
              font-size: 12px;

              .check-mark-text {
                position: absolute;
                right: 0;
                bottom: -18px;
                color: #fff;
              }
            }

            &:hover {
              border-color: var(--el-color-primary);
            }

            &.is-edit {
              border: 0 !important;

              .el-input {
                width: 150px;
              }
            }

            &.is-select {
              border: 1px solid var(--el-border-color);
              padding: 5px 15px;
              border-radius: 5px;
              width: 120px;
            }

            &.is-active {
              border: 1px solid var(--el-color-primary);
              color: var(--el-color-primary);
            }

            &.is-add {
              border-style: dashed;
              width: 140px;
            }

            .el-icon {
              margin-left: 5px;
            }

            .field-li-list-item-remove {
              position: absolute;
              top: -4px;
              right: -4px;
              cursor: pointer;

              &:hover {
                color: var(--el-color-danger);
              }
            }
          }
        }
      }
    }
  }
}

.z-field-group {
  margin-bottom: 30px;

  .group-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--el-text-color-primary);
  }

  .field-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .field-item {
    position: relative;
    padding: 8px 16px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    color: var(--el-text-color-primary);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);
    }

    &.is-selected {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }

    &.is-add {
      border: 1px dashed var(--el-color-primary);
      color: var(--el-color-primary);
    }

    .check-mark {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 0;
      height: 0;
      border-bottom: 16px solid var(--el-color-primary);
      border-left: 16px solid transparent;
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;
      color: white;
      font-size: 12px;

      .check-mark-text {
        position: absolute;
        right: 0;
        bottom: -18px;
        color: #fff;
      }
    }
  }
}

.el-table__cell,
.relative-form-error {
  .el-form-item__error {
    position: relative !important;
    top: 0;
  }
  .el-form-item__content{
    display: block;
  }
}

.table-count-body {
  display: flex;
  align-items: center;
  margin: 0 0 15px 0;

  .table-count-panel {
    flex: 1;
  }

  .el-card {
    margin: 0;
  }
}

.table-count-panel {
  border-radius: 5px;
  display: flex;
  text-align: center;
  padding: 10px 0;

  .count-name {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
  }

  .count-li {
    flex: 1;
    cursor: pointer;

    &.is-active {
      font-weight: bold;

      .count-li-label {
        color: var(--el-color-primary);
      }

      color: var(--el-color-primary);
    }

    .count-li-label {
      color: var(--el-text-color-secondary);
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .count-li-value {
      margin-top: 10px;
      font-size: 14px;

      font {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-left: 5px;
      }
    }
  }
}

.detail-page {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - var(--top-tool-height) - var(--breadcrumb-height) - 20px) !important;

  .top {
    background: #fff;
    background-image: url("@/assets/imgs/detail-top-bg.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    margin-bottom: 20px;
    box-shadow: 0 8px 16px rgb(161 173 196 / 15%);
    padding: 20px;
    border-radius: 10px;
    position: relative;

    .btn {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    .info {
      text-align: center;
      padding: 30px 0 30px 0;

      .info-avatar {
        width: 90px;
        height: 90px;
        border-radius: 100px;
        margin: 0 auto;
        position: relative;
        overflow: hidden;

        .img-lg {
          width: 100%;
          height: 100%;
        }

        img {
          width: 100%;
          height: 100%;
        }

        &:hover {
          .info-avatar-text2 {
            display: block;
          }
        }

        .info-avatar-text2 {
          position: absolute;
          left: 0;
          bottom: 0;
          background: rgba(0, 0, 0, .5);
          width: 100%;
          color: #fff;
          font-size: 12px;
          padding: 5px 0 8px 0;
          display: none;
          cursor: pointer;
        }
      }

      .info-name {
        font-size: 18px;
        font-weight: bold;
        padding: 10px 0;

        span {
          font-size: 12px;
          font-weight: 500;
          margin-left: 5px;
        }
      }

      .info-tag {
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--el-text-color-regular);
        padding: 5px 0;

        .info-tag-span {
          padding: 0 10px;
          position: relative;

          :deep(.-fac-readonly) {
            font-weight: 500 !important;
          }

          &:last-child:after {
            display: none;
          }

          &:after {
            content: "";
            width: 1px;
            height: 10px;
            position: absolute;
            top: 50%;
            transform: translate(0, -50%);
            right: 0;
            background-color: var(--el-border-color);
          }
        }
      }

      .info-status {
        padding-top: 10px;
      }
    }

    .menu {
      display: flex;
      align-items: center;
      justify-content: center;

      .el-tabs__header {
        margin-bottom: 0;
      }

      .el-tabs__nav-wrap:after {
        display: none;
      }
    }
  }

  .main {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;

    .form-panel {
      background: #fff;
      border-radius: 10px;
      padding: 0 20px;
      margin-bottom: 20px;

      &.is-open {
        .form-panel-title {
          .form-panel-title-label {
            .el-icon {
              transform: rotate(90deg);
            }
          }
        }
      }

      .form-panel-title {
        display: flex;
        align-items: center;
        cursor: pointer;

        .form-panel-title-label {
          flex: 1;
          font-size: 14px;
          display: flex;
          align-items: center;
          color: var(--el-text-color-primary);
          font-weight: bold;
          padding: 15px 0;

          .el-icon {
            margin-right: 5px;
          }
        }

        .form-panel-title-btn {
        }
      }

      .form-panel-center {
        border-top: 1px solid var(--el-border-color-extra-light);
        padding-top: 10px;

        .form-panel-center-form {
          margin-bottom: 10px;
          padding: 20px;
          border: 1px solid var(--el-border-color-extra-light);
          border-radius: 10px;
          position: relative;

          &.no-border {
            border: 0;
          }

          &.is-edit {
            border: 0;

            .el-col {
              .el-form-item {
                margin-bottom: 15px;
              }
            }
          }

          &:hover {
            .form-panel-center-form-btn {
              display: inline-block;
            }
          }

          .form-panel-center-form-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            display: none;
            z-index: 1;
          }

          .el-col {
            .el-form-item {
              margin-bottom: 0;
            }
          }
        }

        .form-panel-center-btn {
          text-align: center;
          padding-bottom: 20px;
        }
      }
    }
  }
}

.detail-page-head {
  width: 100%;
  display: flex;
  background: #fff;
  margin-bottom: 20px;
  padding: 10px 20px;
  box-shadow: 0 8px 16px rgb(161 173 196 / 15%);
  border-radius: 10px;
  align-items: center;
  font-size: 14px;

  .name {
    flex: 1;
    margin-left: 20px;
    font-weight: bold;
  }

  .center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .el-tabs {
      .el-tabs__header {
        margin-bottom: 0;
      }

      .el-tabs__nav-wrap {
        &:after {
          height: 0 !important;
        }
      }
    }
  }

  .right {
    flex: 1;
  }
}

/**OKR-KR*/
.kr-panel,
.execute-panel {
  padding: 10px 0;

  .kr-li,
  .execute-li {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 10px 0;
    color: var(--el-text-color-regular);
    cursor: pointer;

    &:hover {
      .kr-li-name,
      .execute-li-name {
        color: var(--el-color-primary);
      }
    }

    .kr-li-name,
    .execute-li-name {
      flex: 1;
    }

    .kr-li-user,
    .execute-li-user {
      color: var(--el-text-color-secondary);
      display: flex;
      align-items: center;
      margin-right: 50px;
    }

    .kr-li-progress,
    .execute-li-progress {
      margin-left: 20px;
    }

    .kr-li-score{
      margin-left: 20px;
    }

    .kr-li-btn,
    .execute-li-btn {
      display: flex;
      align-items: center;
      margin-left: 50px;
    }
  }
}

.kr-panel {
  .kr-li {
    .kr-li-index {
      color: var(--el-color-primary);
      margin-right: 10px;
    }

    .kr-li-rate {
      margin-right: 20px;
      display: flex;
      align-items: center;
    }
  }
}

.execute-panel {
  padding-left: 50px;

  .execute-li {
    .execute-li-logo {
      color: #000;
      margin-right: 5px;
    }

    .execute-li-name {
      color: #000;
    }

    .execute-li-user {
    }

    .execute-li-time {
      margin-left: 50px;
    }

    .execute-li-progress {
    }

    .execute-li-btn {
    }
  }

  .execute-panel-empty {
    color: #fdb600;
    font-size: 12px;
  }
}

.progress-panel {
  &.is-kr {
    cursor: pointer;

    &:hover {
      .progress-li .progress-li-title .name {
        color: var(--el-color-primary);
      }
    }

    .progress-li:after {
      display: none;
    }
  }

  .progress-li {
    position: relative;
    font-size: 14px;
    padding-left: 40px;
    padding-bottom: 20px;

    &.status_1:before {
      background: green;
    }

    &.status_3:before {
      background: red;
    }

    &.status_2:before {
      background: orange;
    }

    &:last-child {
      &:after {
        display: none;
      }
    }

    &:before {
      content: "";
      width: 12px;
      height: 12px;
      background: var(--el-border-color);
      position: absolute;
      border-radius: 100px;
      top: 4px;
      left: 0;
      z-index: 10;
    }

    &:after {
      content: "";
      width: 1px;
      height: 100%;
      background: var(--el-border-color);
      position: absolute;
      border-radius: 100px;
      top: 5px;
      left: 6px;
    }

    .progress-li-title {
      display: flex;
      align-items: center;

      .index {
        color: var(--el-color-primary);
        margin-right: 10px;
      }

      .name {
        margin-right: 10px;
        color: var(--el-text-color-secondary);
      }

      .count {
        font-size: 16px;
        font-weight: bold;
      }

      .rate {
        margin-left: 5px;
        font-weight: bold;
        font-size: 12px;

        &.up {
          color: var(--el-color-success);
        }

        &.down {
          color: var(--el-color-danger);
        }
      }

      .delete{
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }

    .progress-li-content {
      color: var(--el-text-color-secondary);
      margin-top: 5px;
    }

    .progress-li-time {
      font-size: 12px;
      color: var(--el-text-color-disabled);
      margin-top: 5px;
    }
  }
}

/**看板*/
.bulletin-board {
  display: flex;
  width: 100%;
  padding: 0 0 50px 0;
  overflow: auto;

  .bulletin-board-panel {
    background: var(--el-bg-color-page);
    height: 600px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;

    .drop-panel {
      flex: 1;
      height: 0;
      margin-top: 10px;

      .task {
        height: 100%;

        .task-li {
          background: #fff;
          padding: 10px;
          border-radius: 5px;
          margin: 5px 10px;
          font-size: 12px;

          .task-li-t {
            display: flex;
            align-items: center;

            .status {
            }

            .name {
              flex: 1;
              font-size: 16px;
            }

            .avatar {
              width: 24px;
              height: 24px;
              border-radius: 100px;
              background: #bd672e;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 10px;
            }
            .leader{
              font-size: 12px;
              color: var(--el-color-primary);
            }
          }

          .task-li-f {
            display: flex;
            align-items: center;
            border-top: 1px solid var(--el-border-color);
            padding-top: 10px;
            margin-top: 10px;

            .level {
              margin-right: 10px;
            }

            .task {
              flex: 1;
            }

            .time {
              flex: 1;
            }

            .progress {
            }
          }
        }
      }
    }
  }
}

/*头像*/
.el-avatar {
  background: transparent !important;

  img {
    background: transparent !important;
  }
}

/*办公广场*/
.square-pay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 10;

  .square-pay-center {
    border-radius: 20px;
    width: 1000px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #d7edff;
    overflow: hidden;
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    .top {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      background-image: url("@/assets/imgs/pay.png");
      background-size: cover;
      background-position: top;

      .user {
        display: flex;
        align-items: center;
        flex: 1;

        .el-avatar {
          width: 24px;
          height: 24px;
        }
      }

      .close {
        font-size: 20px;
        color: var(--el-text-color-secondary);
        cursor: pointer;

        &:hover {
          color: #000;
        }
      }
    }

    .center {
      background: #fff;
      display: flex;
      border-radius: 20px 20px 0 0;
      overflow: auto;
      flex: 1;

      .form {
        padding: 20px 30px;
        height: 100%;
        width: 100%;

        .form-token {
          font-size: 14px;
          margin-bottom: 20px;

          font {
            color: var(--el-color-primary);
          }
        }

        .form-top {
          display: flex;
          width: 100%;
          align-items: center;
          border: 1px solid var(--el-border-color);
          padding: 20px;
          border-radius: 10px;

          .logo {
            margin-right: 20px;

            img {
              width: 70px;
              height: 70px;
              border-radius: 10px;
            }
          }

          .detail {
            flex: 1;

            .detail-name {
              font-size: 26px;
              font-weight: bold;
              margin-bottom: 5px;
            }

            .detail-ur {
              .detail-ur-tag {
                .el-tag {
                  margin-right: 5px;
                }
              }
            }
          }

          .price {
            font-size: 12px;

            font {
              font-size: 30px;
              font-weight: bold;
            }
          }
        }

        .form-package {
          display: flex;

          .form-package-item {
            background: #FFFFFF;
            border: 1px solid #D8D8D8;
            height: 160px;
            width: 220px;
            margin-right: 20px;
            text-align: center;
            border-radius: 5px;
            position: relative;
            cursor: pointer;

            .badge {
              position: absolute;
              top: -8px;
              left: 0px;
              background: #FFE1DF;
              border: 1px solid #E04239;
              border-radius: 7px 0 7px 0;
              font-size: 12px;
              color: #E04239;
              padding: 2px 5px;
            }

            &.is-active {
              background-image: linear-gradient(180deg, #FFE0E0 0%, #FFFFFF 39%);
              border-color: #E04239;
              box-shadow: 0 0 5px 0px #E04239;

              .name {
                color: #000;
              }

              .badge {
                background-image: linear-gradient(257deg, #FF6E40 0%, #E04239 100%);
                color: #fff;
              }
            }

            .name {
              font-size: 12px;
              padding-top: 20px;
              color: var(--el-text-color-regular);
            }

            .price {
              margin: 15px 0;

              font {
                font-weight: 600;
                font-size: 36px;
              }
            }

            .tip {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }

        .form-count {
          font-size: 14px;
          margin: 20px 0;
          display: flex;
          align-items: center;

          .el-input-number {
            margin-left: 10px;
          }

          .form-count-label {
            width: 100px;
          }

          .form-count-value {
            flex: 1;

            .el-input-number {
              margin: 0;
            }

            &.is-price {
              color: var(--el-color-danger-dark-2);
              font-weight: bold;
              font-size: 12px;

              font {
                font-size: 20px;
              }
            }

            &.is-channel-code {
              display: flex;
              align-items: center;

              .is-channel-code-li {
                display: flex;
                align-items: center;
                padding: 10px 20px;
                border: 1px solid var(--el-border-color);
                border-radius: 5px;
                margin-right: 20px;
                cursor: pointer;

                &.is-active,
                &:hover {
                  border-color: var(--el-color-primary);
                  color: var(--el-color-primary);
                }

                img {
                  height: 20px;
                  margin-right: 10px;
                }
              }
            }
          }
        }

        .form-power {
          margin-top: 20px;
          margin-bottom: 20px;
          background: #F9F9F9;
          border-radius: 8px;
          padding: 20px;

          .form-power-title {
            font-size: 16px;
            margin-bottom: 10px;
          }

          .form-power-detail {
            font-size: 14px;
            display: flex;
            flex-flow: wrap row;

            .detail-li{
              display: flex;
              align-items: center;
              margin-right: 30px;
              .el-icon {
                color: var(--el-color-primary);
                margin-right: 10px;
              }
            }

            ul {
              width: 100%;
              display: flex;
              flex-flow: wrap row;

              li {
                display: flex;
                align-items: center;

                .el-icon {
                  color: var(--el-color-primary);
                  margin-right: 5px;
                }

                margin: 0 30px 10px 0;
                color: var(--el-text-color-regular);
              }
            }

            .form-power-detail-li {
            }
          }
        }
      }

      .pay {
        text-align: center;
        margin: 0 auto;
        padding: 30px 0;

        .pay-price {
          font-size: 12px;
          color: var(--el-color-danger);

          font {
            font-size: 30px;
            font-weight: bold;
          }
        }

        .pay-qr {
          canvas {
            width: 300px !important;
            height: 300px !important;
          }
        }

        .pay-info {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }

      .result {
        text-align: center;
        margin: 0 auto;
        padding: 50px 0;

        .result-icon {
          font-size: 80px;

          .success {
            color: var(--el-color-success);
          }

          .cancel {
            color: var(--el-color-danger);
          }
        }

        .result-label {
          font-size: 16px;
          font-weight: bold;
        }

        .result-order {
          background: var(--el-bg-color-page);
          padding: 10px 30px;
          margin-top: 50px;
          width: 500px;

          .result-order-li {
            display: flex;
            padding: 10px 0;
            justify-content: center;
            align-items: center;
            text-align: left;

            .label {
              margin-right: 50px;
              color: var(--el-text-color-secondary);
            }

            .value {
              width: 100px;
              color: var(--el-text-color);
            }
          }
        }
      }
    }
  }
}

.amap-sug-result {
  z-index: 1111111;
}

/*选择数据面板-已选择*/
.selected-data-panel {
  width: 350px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 20px;

  .selected-data-panel_title {
    font-size: 16px;
    display: flex;
    align-items: center;

    .count {
      flex: 1;
    }

    .clear {
      font-size: 12px;
      color: var(--el-color-danger);
      cursor: pointer;
      padding-right: 10px;
      display: flex;
      align-items: center;

      &:hover {
      }
    }
  }

  .selected-data-panel-body {
    flex: 1;
    padding-top: 10px;
    height: 0;
    position: relative;

    :deep(.el-scrollbar) {
      height: 100%;
    }

    .data-item {
      display: flex;
      align-items: center;
      padding: 8px 15px 5px 0;
      cursor: pointer;

      &:hover {
        background: var(--el-bg-color-page);
      }

      .data-item_label {
        flex: 1;
        font-size: 14px;
        width: 0;
      }

      .data-item_icon {
        cursor: pointer;
        padding-left: 5px;
        color: var(--el-text-color-regular);

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }
  }

  .selected-data-panel_btn {
    display: flex;
    justify-content: flex-end;
  }
}

/**卡片样式**/
.el-card {
  border: 1px solid #fff !important;
  border-radius: 8px !important;

  &:hover {
    box-shadow: 0 6px 24px rgba(31, 35, 41, 0.08);
  }
}

/**全局按钮文本框圆角样式**/
.el-button {
  border-radius: 6px !important;
}

.el-input .el-input__wrapper {
  border-radius: 6px !important;
}

// 使用el-input类型type=“number” 时，取消右边的上下箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/**表格中操作按钮下拉样式**/
.table-tool-btn-dropdown {
  .el-dropdown-menu__item {
    padding: 0 !important;

    .el-button {
      padding: 5px 10px;
      width: 100%;
      display: flex;
      justify-content: flex-start;
    }
  }
}

.remaining-days{
  color: green;
}
.time-out-days{
  color: red;
}
.hover-update-select{
  &:hover{
    .el-select__wrapper{
      background: #fff;
      box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
    }
  }
  .el-select__wrapper{
    box-shadow: 0 0 0 !important;
    background: transparent;
  }
}

.holiday-detail-list{
  width: 100%;
  .holiday-detail-list-btn{
    display: flex;
    align-items: center;
    .el-button{
      margin-right: 20px;
    }
    .count{
      display: flex;
      align-items: center;
      .count-item{
        margin-right: 20px;
      }
    }
  }
}
.must{
  color: red;
}
.form-item-tip{
  margin-left: 10px;
  font-size: 18px;
}
.icon-expand:before {
  content: "\e602" !important;
}
.el-message{
  z-index: 5000 !important;
}
.user-info-dropdown-menu{
  .el-dropdown-menu__item{
    padding: 10px 25px !important;
  }
}
.zeadoor-list-container {
  display: flex;
  height: calc(100vh - var(--top-tool-height) - var(--breadcrumb-height) - 40px) !important;

  .zeadoor-list-container-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    width: 100%;

    .header-panel {
      .el-tabs {
        .el-tabs__header {
          margin-bottom: 0;

          .el-tabs__nav-wrap {
            &:after {
              display: none;
            }

            .el-tabs__item {
              padding: 10px 30px;

              &.is-active {
                background: #fff;
                //border-top: 1px solid rgb(221, 223, 230);
                //border-left: 1px solid rgb(221, 223, 230);
                //border-right: 1px solid rgb(221, 223, 230);
                box-shadow: 0 -10px 20px #cad8fa42;
                border-radius: 10px 10px 0 0;
              }
            }
          }
        }
      }
    }

    .main-panel {
      flex: 1;
      width: 100%;
      height: 0;
      display: flex;
      flex-direction: column;

      .zeadoor-list-container {
        height: 100% !important;
      }
    }
  }

  .zeadoor-list-container-table {
    flex: 1;
    height: 0;
    display: flex;

    & > .el-card {
      width: 100%;
      height: 100%;
      margin-bottom: 0;

      .el-card__body {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }

    .el-table__inner-wrapper:before {
      display: none;
    }

    .table-center {
      flex: 1;
      height: 0;

      .el-table,
      .el-scrollbar__view {
        height: 100%;
      }
    }

    .table-page {
      padding: 0 10px;

      .el-pagination {
        margin: 0 !important;
      }

      .is-multiple {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 60px;

        .btn {
          flex: 1;
          display: flex;
          align-items: center;

          .btn-all {
            margin-right: 20px;
          }

          .btn-list {
          }
        }

        .page {
        }
      }
    }
  }
}