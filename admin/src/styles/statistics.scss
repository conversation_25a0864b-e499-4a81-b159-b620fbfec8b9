.statistics-span {
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  height: 100%;

  .statistics-span-title {
    .label {
      font-size: 16px;
      font-weight: bold;
    }
    .description{
      font-size: 12px;
      color: var(--el-text-color-regular);
      font-weight: 400 !important;
    }
  }

  .statistics-span-main {
    height: 300px;
  }
}

.statistics-count{
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  .statistics-count-l{
    flex: 1;
    color: var(--el-text-color-secondary);
    .l{
      font-size: 12px;
    }
    .r{
      font-size: 12px;
      padding-top: 10px;
      font{
        font-size: 26px;
        font-weight: bold;
        color:#000;
        margin-right: 5px;
      }
    }
  }
  .statistics-count-r{
    width: 40px;
    height: 40px;
    border-radius: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    &.a{
      background: #e2e4fd;
      color: #6c76f4;
    }
    &.b{
      background: #d4f6ea;
      color: #28d094;
    }
    &.c{
      background: #d3dffc;
      color: #2561ef;
    }
    &.d{
      background: #fff8e4;
      color: #fddb78;
    }
    &.e{
      background: #fee3e1;
      color: #fa746b;
    }
    &.f{
      background: #d3dffc;
      color: #2561ef;
    }
  }
}
:deep(.el-col-5){
  width: 20% !important;
  flex: initial !important;
}