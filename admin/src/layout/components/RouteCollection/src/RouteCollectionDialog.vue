<template>
  <el-dialog v-model="dialogVisible" title="快捷跳转" width="400px">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px"
             v-loading="formLoading">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入名称"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="formLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as RouteApi from '@/api/crm/route'

defineOptions({ name: 'RouteCollectionDialog' })

const message = useMessage()

const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()

const formData = ref({
  systemKey: '',
  key: '',
  name: '',
  fullPath: '',
  type: 1
})

const formRules = reactive({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
})

const emit = defineEmits(['success'])

/** 打开弹窗 */
const open = (systemKey: string, key: string, routePath: string) => {
  dialogVisible.value = true
  resetForm()
  formData.value.systemKey = systemKey  // crm
  formData.value.key = key              // customer
  formData.value.fullPath = routePath   // crm/customer
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    systemKey: '',
    key: '',
    name: '',
    fullPath: '',
    type: 1
  }
  formRef.value?.resetFields()
}

/** 提交表单 */
const submitForm = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  formLoading.value = true
  try {
    const data = {
      systemKey: formData.value.systemKey,  // crm
      key: formData.value.key,              // customer
      name: formData.value.name,            // 用户输入的名称
      fullPath: formData.value.fullPath,    // crm/customer
      type: formData.value.type,            // 1
    }
    await RouteApi.createQuickJump(data)
    message.success('保存成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    formLoading.value = false
  }
}

defineExpose({ open })
</script>
