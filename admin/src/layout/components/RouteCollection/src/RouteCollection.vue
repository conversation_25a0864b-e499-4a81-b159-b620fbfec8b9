<script lang="ts" setup>
import { useDesign } from '@/hooks/web/useDesign'
import { Star, StarFilled } from "@element-plus/icons-vue"
import { useRouter } from 'vue-router'
import * as RouteApi from '@/api/crm/route'
import RouteCollectionDialog from './RouteCollectionDialog.vue'

defineOptions({ name: 'RouteCollection' })

const { getPrefixCls } = useDesign()
const router = useRouter()
const message = useMessage()

const prefixCls : any = getPrefixCls('routeCollection')

const isCollection = ref(false)
const collectionDialogRef = ref()

/** 从URL中提取路由信息 */
const extractRouteInfo = () => {
  const currentPath = router.currentRoute.value.path

  // 从路径中提取 systemKey 和 key
  // 例如: /crm/customer -> systemKey: crm, key: customer
  // 例如: /crm/customer/seas -> systemKey: crm, key: customer/seas
  // 路径格式: crm/customer  (去掉开头的/)
  const pathParts = currentPath.split('/').filter(part => part)

  if (pathParts.length >= 2) {
    const routePath = pathParts.join('/') // crm/customer 或 crm/customer/seas
    const key = pathParts.slice(1).join('/') // customer 或 customer/seas
    return {
      systemKey: pathParts[0], // crm
      key: key,                // customer 或 customer/seas
      fullPath: routePath,    // crm/customer 或 crm/customer/seas
    }
  }

  return null
}

/** 检查当前路由是否已快捷跳转 */
const checkCollection = async () => {
  const routeInfo = extractRouteInfo()
  if (!routeInfo) return

  try {
    const data = {
      systemKey: routeInfo.systemKey,
      key: routeInfo.key
    }
    const result = await RouteApi.getQuickJump(data)
    isCollection.value = result.id ? true : false
  } catch (error) {
    console.error('检查快捷跳转状态失败:', error)
    isCollection.value = false
  }
}

/** 处理快捷跳转点击 */
const handleCollectionClick = async () => {
  const routeInfo = extractRouteInfo()
  if (!routeInfo) {
    message.warning('无法获取当前路由信息')
    return
  }

  if (isCollection.value) {
    // 取消快捷跳转
    try {
      const data = {
        systemKey: routeInfo.systemKey,
        key: routeInfo.key
      }
      await RouteApi.removeQuickJump(data)
      message.success('取消快捷跳转成功')
      isCollection.value = false
    } catch (error) {
      console.error('取消快捷跳转失败:', error)
    }
  } else {
    // 添加快捷跳转
    collectionDialogRef.value?.open(routeInfo.systemKey, routeInfo.key, routeInfo.fullPath)
  }
}

/** 快捷跳转成功回调 */
const onCollectionSuccess = () => {
  isCollection.value = true
}

// 组件挂载时检查快捷跳转状态
onMounted(async () => {
  await checkCollection()
})

// 监听路由变化，重新检查快捷跳转状态
watch(() => router.currentRoute.value.path, () => {
  checkCollection()
})
</script>

<template>
  <el-tooltip :content="isCollection ? '取消快捷跳转' : '添加快捷跳转'" placement="bottom">
    <div class="custom-hover" @click="handleCollectionClick">
      <el-icon size="23" v-if="isCollection" style="color: var(--el-color-warning)">
        <StarFilled/>
      </el-icon>
      <el-icon style="color: #9a9a9a" v-else size="20">
        <Star/>
      </el-icon>
    </div>
  </el-tooltip>

  <!-- 快捷跳转对话框 -->
  <RouteCollectionDialog ref="collectionDialogRef" @success="onCollectionSuccess" />
</template>
