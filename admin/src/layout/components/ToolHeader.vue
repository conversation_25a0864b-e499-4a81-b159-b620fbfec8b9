<script lang="tsx">
import {computed, defineComponent} from 'vue'
import {Message} from '@/layout/components//Message'
import {Collapse} from '@/layout/components/Collapse'
import {UserInfo} from '@/layout/components/UserInfo'
import {Screenfull} from '@/layout/components/Screenfull'
import {SizeDropdown} from '@/layout/components/SizeDropdown'
import {LocaleDropdown} from '@/layout/components/LocaleDropdown'
import {LockScreen} from '@/layout/components/LockScreen'
import RouterSearch from '@/components/RouterSearch/index.vue'
import {useAppStore} from '@/store/modules/app'
import {useDesign} from '@/hooks/web/useDesign'
import {Logo} from "@/layout/components/Logo";
import {HeadMenu} from "@/layout/components/HeadMenu";
import LockDialog from "@/layout/components/UserInfo/src/components/LockDialog.vue";

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('tool-header')

const appStore = useAppStore()

// 面包屑
const breadcrumb = computed(() => appStore.getBreadcrumb)

// 折叠图标
const hamburger = computed(() => appStore.getHamburger)

// 全屏图标
const screenfull = computed(() => appStore.getScreenfull)

// 搜索图片
const search = computed(() => appStore.search)

// 尺寸图标
const size = computed(() => appStore.getSize)

// 布局
const layout = computed(() => appStore.getLayout)

// 多语言图标
const locale = computed(() => appStore.getLocale)

// 消息图标
const message = computed(() => appStore.getMessage)

export default defineComponent({
  name: 'ToolHeader',
    components: {LockDialog},
  setup() {
    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          'h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between',
          'dark:bg-[var(--el-bg-color)]'
        ]}
        style="background: #fff"
      >
        {layout.value !== 'top' ? (

          <div class="h-full flex items-center">

            <Logo
                class={[
                  'relative',
                  {
                    'w-[var(--left-menu-min-width)]': appStore.getCollapse,
                    'w-[var(--left-menu-max-width)]': !appStore.getCollapse
                  }
                ]}
                style="transition: all var(--transition-time-02);"
            ></Logo>

            <HeadMenu/>

            {hamburger.value && layout.value !== 'cutMenu' ? (
              <Collapse class="custom-hover" color="var(--top-header-text-color)"></Collapse>
            ) : undefined}
          </div>
        ) : undefined}
        <div class="h-full flex items-center head-right">
          {screenfull.value ? (
            <Screenfull class="custom-hover" color="var(--top-header-text-color)"></Screenfull>
          ) : undefined}
          {search.value ? <RouterSearch isModal={false} /> : undefined}
            <LockScreen
                class="custom-hover"
            ></LockScreen>
          {size.value ? (
            <SizeDropdown class="custom-hover" color="var(--top-header-text-color)"></SizeDropdown>
          ) : undefined}
          {locale.value ? (
            <LocaleDropdown
              class="custom-hover"
              color="var(--top-header-text-color)"
            ></LocaleDropdown>
          ) : undefined}
          {message.value ? (
            <Message class="custom-hover" color="var(--top-header-text-color)"></Message>
          ) : undefined}
          <UserInfo></UserInfo>
        </div>
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-tool-header;

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
}
</style>
