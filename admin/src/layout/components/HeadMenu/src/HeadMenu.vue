<script lang="ts" setup>
import {onMounted} from 'vue'
import {CaretBottom, HomeFilled, Menu} from '@element-plus/icons-vue'
import {usePermissionStore} from '@/store/modules/permission'
import {useAppStore} from '@/store/modules/app'
import {Promotion} from '@element-plus/icons-vue'
import CreateApplication from '@/views/square/components/create-square.vue'
import {getAccessToken} from "@/utils/auth";

defineOptions({name: 'HeadMenu'})

const {push} = useRouter()
const appStore = useAppStore()

const permissionStore = usePermissionStore()

const layout = computed(() => appStore.getLayout)

const createApplicationRef = ref()
const currentApp = computed(() => appStore.getApp)

const routers = computed(() => {
  let menuList =
      unref(layout) === 'cutMenu' ? permissionStore.getMenuTabRouters : permissionStore.getRouters
  menuList = menuList.filter((t: any) => !t?.meta?.hidden)
  return menuList
})

const handleApp = (app: any) => {
  const token = getAccessToken()

  let httpUrl = app.url || app.path
  if (httpUrl && httpUrl.toLowerCase().startsWith("http")) {
    httpUrl = httpUrl.replace(/\${token}/g, token);
    httpUrl = httpUrl.replace(/%3F/g, "?");
    window.location.href = httpUrl
    return
  }
  appStore.setAppItem(app)
  let item = getPushItem(app)
  if (item && item.name) {
    push({name: item.name})
  }
}

const getPushItem = (item: any) => {
  if (item.children && item.children.length) {
    let a = null
    item.children.forEach((t: any) => {
      if (!a) {
        a = getPushItem(t)
      }
    })
    return a
  } else {
    return item
  }
}

const showAppMenu = ref(false)
onMounted(() => {
  document.addEventListener('click', function (event) {
    showAppMenu.value = false
  })
  if (currentApp.value) {
    if (!routers.value || !routers.value.find((t: any) => t.name === currentApp.value.name)) {
      appStore.setAppItem(null)
    }
  }
  if (!currentApp.value && routers.value && routers.value.length) {
    let find = routers.value.find((t: any) => t.path === "/oa")
    handleApp(find || routers.value[0])
  }
})
</script>

<template>
  <div class="zeadoor-tool-head-menu">
    <div class="menu" @click="push('/admin/index')">
      <el-icon>
        <HomeFilled/>
      </el-icon>
      首页
    </div>
    <div class="menu is-active is-app" @click.stop="showAppMenu = !showAppMenu">
      <template v-if="currentApp">
        <Icon :icon="currentApp?.meta?.icon" :size="14"/>
        {{ currentApp?.meta.title }}
      </template>
      <template v-else>
        <el-icon>
          <Menu/>
        </el-icon>
        应用中心
      </template>
      <el-icon class="m-l-3" style="color: var(--el-text-color-disabled)">
        <CaretBottom/>
      </el-icon>
    </div>
    <div class="app-list" v-if="showAppMenu">
      <template v-for="item in routers" :key="item">
        <div class="app-list-item" v-if="!item?.meta?.hidden" @click="handleApp(item)">
          <div class="icon el-icon" :class="item?.meta?.icon">
            <Icon :icon="item?.meta?.icon" :size="22"/>
          </div>
          <div class="label">{{ item?.meta?.title }}</div>
        </div>
      </template>
      <div class="app-list-item" @click="createApplicationRef.open()">
        <div class="icon el-icon">
          <el-icon size="22">
            <Promotion/>
          </el-icon>
        </div>
        <div class="label">新建应用</div>
      </div>
    </div>
  </div>
  <CreateApplication ref="createApplicationRef"/>
</template>

<style lang="scss">
.zeadoor-tool-head-menu {
  display: flex;
  align-items: center;
  height: 100%;

  .menu {
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    height: 100%;
    color: var(--el-text-color-regular);
    cursor: pointer;
    position: relative;

    //&.is-app:after{
    //  content: "";
    //  width: 0px;
    //  height: 0px;
    //  position: absolute;
    //  bottom: 0;
    //  right: 0;
    //  border-top: 8px solid transparent;
    //  border-left: 8px solid transparent;
    //  border-right: 8px solid var(--el-color-primary);
    //}

    .el-icon {
      margin-right: 5px;
    }

    &:hover {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    &.is-active {
      color: var(--el-color-primary);
    }
  }

  .app-list {
    position: fixed;
    background: #fff;
    padding: 20px 20px 0 20px;
    z-index: 100000;
    top: 70px;
    left: 200px;
    width: 540px;
    border-radius: 10px;
    box-shadow: 0 2px 7px 1px #ececec;
    display: flex;
    flex-flow: wrap row;

    .app-list-item {
      width: 100px;
      text-align: center;
      margin-bottom: 30px;
      font-size: 14px;
      cursor: pointer;

      &:hover {
      }

      .icon {
        width: 50px;
        height: 50px;
        background: var(--el-bg-color-page);
        border-radius: 100px;
        color: var(--el-color-primary);
      }

      .label {
        margin-top: 5px;
      }
    }
  }
}
</style>
