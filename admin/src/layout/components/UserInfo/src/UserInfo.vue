<script lang="ts" setup>
import {ElMessageBox} from 'element-plus'

import avatarImg from '@/assets/imgs/avatar.jpg'
import {useDesign} from '@/hooks/web/useDesign'
import {useTagsViewStore} from '@/store/modules/tagsView'
import {useUserStore} from '@/store/modules/user'
import LockDialog from './components/LockDialog.vue'
import LockPage from './components/LockPage.vue'
import {useLockStore} from '@/store/modules/lock'

defineOptions({name: 'UserInfo'})

const {t} = useI18n()

const {push, replace} = useRouter()

const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()

const {getPrefixCls} = useDesign()

const prefixCls = getPrefixCls('user-info')

const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')

// 锁定屏幕
const lockStore = useLockStore()
const getIsLock = computed(() => lockStore.getLockInfo?.isLock ?? false)

const loginOut = async () => {
  try {
    await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    })
    await userStore.loginOut()
    tagsViewStore.delAllViews()
    replace('/square/list?redirect=/index')
  } catch {
  }
}
const toProfile = async () => {
  push('/user/profile')
}
const toTask = async () => {
  push({name: "systemTask"})
}
const toDocument = () => {
  window.open('https://doc.iocoder.cn/')
}
</script>

<template>
  <ElDropdown class="custom-hover" :class="prefixCls" trigger="click">
    <div class="flex items-center">
      <ElAvatar :src="avatar" alt="" class="w-[calc(var(--logo-height)-25px)] rounded-[50%]"/>
      <span class="pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden">
        {{ userName }}
      </span>
    </div>
    <template #dropdown>
        <ElDropdownMenu class="user-info-dropdown-menu">
        <ElDropdownItem @click="push({name: 'CompanyDetail'})">
          <Icon icon="ep:menu"/>
          <div>企业信息</div>
        </ElDropdownItem>
        <ElDropdownItem @click="toProfile">
          <Icon icon="ep:tools"/>
          <div>{{ t('common.profile') }}</div>
        </ElDropdownItem>
        <ElDropdownItem @click="toTask">
          <Icon icon="ep:tools"/>
          <div>任务中心</div>
        </ElDropdownItem>
        <ElDropdownItem @click="loginOut">
          <Icon icon="ep:switch-button"/>
          <div>{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>

  <teleport to="body">
    <transition name="fade-bottom" mode="out-in">
      <LockPage v-if="getIsLock"/>
    </transition>
  </teleport>
</template>

<style scoped lang="scss">
.fade-bottom-enter-active,
.fade-bottom-leave-active {
  transition: opacity 0.25s,
  transform 0.3s;
}

.fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(-10%);
}

.fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(10%);
}
</style>
