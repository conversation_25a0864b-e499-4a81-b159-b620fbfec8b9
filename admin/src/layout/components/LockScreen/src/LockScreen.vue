<script lang="ts" setup>
import {Icon} from '@/components/Icon'
import {useFullscreen} from '@vueuse/core'
import {propTypes} from '@/utils/propTypes'
import {useDesign} from '@/hooks/web/useDesign'
import LockDialog from "@/layout/components/UserInfo/src/components/LockDialog.vue";

defineOptions({ name: 'LockScreen' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('lockScreen')

defineProps({
  color: propTypes.string.def('')
})

const { toggle, isFullscreen } = useFullscreen()

const toggleFullscreen = () => {
  toggle()
}

const dialogVisible = ref<boolean>(false)
const lockScreen = () => {
  dialogVisible.value = true
}

</script>

<template>
  <div class="custom-hover" :class="prefixCls" @click="lockScreen">
    <Icon icon="ep:lock" size="18px"/>
  </div>

  <LockDialog v-if="dialogVisible" v-model="dialogVisible" />
</template>
