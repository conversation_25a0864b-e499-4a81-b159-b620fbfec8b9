import { FormColumnBO } from '@/components/Zeadoor/CustomForm/config'
import { SearchToolBO, TableToolButtonBO } from '@/components/Zeadoor/CustomTable/config'

export interface SearchBO {
  show?: Boolean
  showMore?: Boolean
  hiddenSearch?: Boolean
  hiddenReset?: Boolean
  toolbar?: SearchToolBO // 操作栏目
  columns?: FormColumnBO[],
  defaultQuery?: any,  // 默认检索条件
  disabledProps?: string[], // 禁用属性
  fastPlaceholder: string,  // 快速搜索站位文字
}

export interface SearchToolBO {
  show?: Boolean //是否显示
  buttons?: Array<TableToolButtonBO | string> // 操作按钮
}

export const Config: SearchBO = {
  show: true,
  showMore: true,
  hiddenSearch: false,
  hiddenReset: false,
  toolbar: {
    show: true,
    columns: []
  },
  columns: []
}
