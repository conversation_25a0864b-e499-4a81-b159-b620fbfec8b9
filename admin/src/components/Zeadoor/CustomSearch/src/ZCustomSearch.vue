<script lang="ts" setup>
import {Config} from '@/components/Zeadoor/CustomSearch/config'
import {ColumnTypeVo} from '@/components/Zeadoor/interface'
import {propTypes} from '@/utils/propTypes'
import {merge} from 'lodash'
import * as CustomFormApi from '@/api/system/customForm/index'
import * as AreaApi from '@/api/system/area'
import {defaultProps} from '@/utils/tree'
import {setComposeColumn} from '@/components/Zeadoor/CustomForm/config'

const props = defineProps({
  config: propTypes.object.def(),
  pageKey: propTypes.string.def(),
  isCustom: propTypes.bool.def(false),
  modelValue: propTypes.object.def({}),
  columns: propTypes.object.def([])
})
const emits = defineEmits(['search', 'update:modelValue', 'update:columns'])

const _config = ref<ConfigBO>(merge({}, Config, props.config))
const formRef = ref()
const formMoreRef = ref()
const areaList = ref([]) // 地区列表

/**************************
 *  表单属性项目处理逻辑
 * ******************************/
const getColumns = async () => {
  let detailInfo = await CustomFormApi.getByKey(props.pageKey)
  if (detailInfo?.config) {
    _config.value = merge({}, _config.value, JSON.parse(detailInfo.config)?.form)
    emits('before-set-columns', _config.value?.columns)
  }
  await setComposeColumns()
}

/**
 * 完善填充字段属性
 */
const setComposeColumns = async () => {
  // 查询地区列表
  if (_config.value.columns.find((t: any) => t.type === ColumnTypeVo.city)) {
    areaList.value = await AreaApi.getAreaTree()
  }
  _config.value.columns?.forEach((t: any) => {
    setComposeColumn(t)
  })
}

const formRules = ref<any>(props.rules || {})

const columnLoading = ref(true)
onMounted(async () => {
  columnLoading.value = true
  if (props.isCustom) {
    await getColumns()
  } else {
    await setComposeColumns()
  }
  emits("update:columns", _config.value?.columns)

  columnLoading.value = false
  nextTick(() => {
    formRef.value?.clearValidate()
    formMoreRef.value?.clearValidate()
  })
})

defineExpose({})

const formData = ref<any>(props.modelValue)
const showFilter = ref(false)
const onConfirm = function () {
  showFilter.value = false
  emits('update:modelValue', formData.value)
  emits('search')
}

const onOpenFilter = function () {
  formData.value = Object.assign({}, props.modelValue)
}

const resetQuery = () => {
  formRef.value?.resetFields()
  formMoreRef.value?.resetFields()
  emits('update:modelValue', formData.value)
  emits('search')
  showFilter.value = false
}
/**
 * 判断是否禁用事件
 * @param item
 * @param scope
 */
const isDisabledEvent = (item: any) => {
  if (_config.value.disabledProps?.indexOf(item.prop) > -1) return true
  if (item.hasOwnProperty('vDisabled')) {
    if (typeof item.vDisabled === 'function') {
      return item.vDisabled(formData.value)
    }
    return !!item.vDisabled
  }
  return false
}

const getSearchCount = computed(() => {
  let count = 0
  for (let key in props.modelValue) {
    let find = _config.value.columns?.find((t: any) => t.prop === key)
    if (find) {
      if (Array.isArray(props.modelValue[key])) {
        if (props.modelValue[key].length) {
          count++
        }
      } else if (props.modelValue[key]) {
        count++
      }
    }
  }
  return count
})

const getShowColumns = computed(() => {
  return _config.value?.columns?.filter((t:any) => !t.hiddenSearch)
})

</script>

<template>
  <div class="zeadoor-list-container-search">
    <ContentWrap>
      <slot name="search">
        <el-form ref="formRef" :model="formData" label-width="68px" @submit.prevent>
          <div class="zeadoor-list-container-search-top">
            <div class="fast-search-form">
              <div class="fast-search-form-item">
                <slot name="search-fast">
                  <el-form-item label-width="0" prop="searchKey">
                    <el-input
                        v-model="formData.searchKey"
                        class="!w-240px"
                        clearable
                        :placeholder="_config.fastPlaceholder || '请输入快速搜索关键词'"
                        @keyup.enter="emits('search')"
                    />
                  </el-form-item>

                  <template v-for="item in getShowColumns" :key="item">
                    <el-form-item :prop="item.prop" :label="item.label" v-if="item.isFast" style="margin-left: 10px">
                      <slot :name="`search-prop-${item.prop}`" :column="item" :form="formData" :disabled="isDisabledEvent(item)">
                        <el-cascader
                            v-if="item.type === ColumnTypeVo.city"
                            v-model="formData[item.prop]"
                            :style="{'width': item.width}"
                            :options="areaList"
                            :props="defaultProps"
                            clearable
                            :disabled="isDisabledEvent(item)"
                            filterable
                            v-bind="item.props"
                        />
                        <component
                            v-else
                            :is="item.component"
                            v-bind="item.props"
                            :style="{'width': item.width}"
                            :disabled="isDisabledEvent(item)"
                            v-model="formData[item.prop]"
                        >
                          <template v-if="item.type === ColumnTypeVo.select">
                            <el-option
                                :label="dict.label"
                                :value="dict.value"
                                v-for="dict in item.options"
                                :key="dict.value"
                            />
                          </template>
                          <template v-if="item.type === ColumnTypeVo.radio">
                            <el-radio
                                :label="dict.label"
                                :value="dict.value"
                                v-for="dict in item.options"
                                :key="dict.value"
                            />
                          </template>
                          <template v-if="item.type === ColumnTypeVo.checkbox">
                            <el-checkbox
                                :label="dict.label"
                                :value="dict.value"
                                v-for="dict in item.options"
                                :key="dict.value"
                            />
                          </template>
                        </component>
                      </slot>
                    </el-form-item>
                  </template>

                </slot>
              </div>
              <div class="fast-search-form-search">
                <el-button @click="emits('search')" v-if="!_config.hiddenSearch">
                  <Icon class="mr-5px" icon="ep:search"/>
                  搜索
                </el-button>
                <el-button @click="resetQuery" v-if="!_config.hiddenReset">
                  <Icon class="mr-5px" icon="ep:refresh"/>
                  重置
                </el-button>
                <el-button
                    v-if="_config.showMore"
                    @click="showFilter = true"
                    :type="getSearchCount > 0 ? 'primary' : ''"
                    :plain="getSearchCount > 0"
                >
                  <Icon icon="ep:filter"/>
                  {{ getSearchCount || '' }}
                </el-button>
              </div>
            </div>
            <div class="fast-search-tool">
              <slot name="search-tool"></slot>
            </div>
          </div>
          <div class="zeadoor-list-container-search-more">
            <slot name="search-more"></slot>
          </div>
        </el-form>
      </slot>
    </ContentWrap>

    <el-drawer
        class="z-detail-drawer"
        size="500px"
        v-model="showFilter"
        @open="onOpenFilter"
        title="筛选"
    >
      <div class="search-form">
        <div class="search-form-center">
          <el-form ref="formMoreRef" :model="formData" label-position="top">
            <slot :name="`search-filter`" :form="formData">
              <template v-for="item in getShowColumns" :key="item">
                <el-form-item :prop="item.prop" :label="item.label"  v-if="!item.isFast">
                  <slot :name="`search-prop-${item.prop}`" :column="item" :form="formData" :disabled="isDisabledEvent(item)">
                    <el-cascader
                        v-if="item.type === ColumnTypeVo.city"
                        v-model="formData[item.prop]"
                        class="!w-full"
                        :options="areaList"
                        :props="defaultProps"
                        clearable
                        :disabled="isDisabledEvent(item)"
                        filterable
                        v-bind="item.props"
                    />
                    <component
                        v-else
                        :is="item.component"
                        v-bind="item.props"
                        class="!w-full"
                        :disabled="isDisabledEvent(item)"
                        v-model="formData[item.prop]"
                    >
                      <template v-if="item.type === ColumnTypeVo.select">
                        <el-option
                            :label="dict.label"
                            :value="dict.value"
                            v-for="dict in item.options"
                            :key="dict.value"
                        />
                      </template>
                      <template v-if="item.type === ColumnTypeVo.radio">
                        <el-radio
                            :label="dict.label"
                            :value="dict.value"
                            v-for="dict in item.options"
                            :key="dict.value"
                        />
                      </template>
                      <template v-if="item.type === ColumnTypeVo.checkbox">
                        <el-checkbox
                            :label="dict.label"
                            :value="dict.value"
                            v-for="dict in item.options"
                            :key="dict.value"
                        />
                      </template>
                    </component>
                  </slot>
                </el-form-item>
              </template>
            </slot>
          </el-form>
        </div>
        <div class="search-form-footer">
          <el-button type="primary" @click="onConfirm">确定</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss">
.zeadoor-list-container-search {
  .el-card {
    .el-card__body {
      padding: 15px !important;
    }
  }

  .zeadoor-list-container-search-top {
    display: flex;
    align-items: center;

    .fast-search-form {
      flex: 1;
      display: flex;
      align-items: center;

      .fast-search-form-item {
        display: flex;
        align-items: center;
      }

      .fast-search-form-search {
        margin-left: 10px;
      }

      .el-form-item {
        margin-bottom: 0 !important;
      }
    }

    .fast-search-tool {
      display: flex;
      align-items: center;

      .el-button {
        margin: 0 0 0 10px;
      }
    }
  }

  .zeadoor-list-container-search-more {
  }
}

.search-form {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-form-center {
    flex: 1;
    overflow: auto;
    height: 0;
  }

  .search-form-footer {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
