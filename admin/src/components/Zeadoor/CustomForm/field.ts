import {ColumnTypeVo} from "@/components/Zeadoor/interface";
import {DataTypeEnum} from "@/components/Zeadoor/CustomForm/config";

export const Fields = [
  {
    label: "文本框",
    type: ColumnTypeVo.text
  },
  {
    label: "多行文本框",
    type: ColumnTypeVo.textarea
  },
  {
    label: "数字输入框",
    type: ColumnTypeVo.number
  },
  {
    label: "单选框",
    type: ColumnTypeVo.radio,
    dataType: DataTypeEnum.custom,
    options:[
      {
        value: "radio-01",
        label: "单选1"
      },
      {
        value: "radio-02",
        label: "单选2"
      }
    ]
  },
  {
    label: "多选框",
    type: ColumnTypeVo.checkbox,
    dataType: DataTypeEnum.custom,
    options: [
      {
        value: "check-01",
        label: "多选1"
      },
      {
        value: "check-02",
        label: "多选2"
      }
    ]
  },
  {
    label: "下拉框",
    type: ColumnTypeVo.select,
    dataType: DataTypeEnum.custom,
    options: [
      {
        value: "check-01",
        label: "下拉选项1"
      },
      {
        value: "check-02",
        label: "下拉选项2"
      }
    ]
  },
  {
    label: "日期",
    type: ColumnTypeVo.date
  },
  {
    label: "日期时间",
    type: ColumnTypeVo.datetime
  },
  {
    label: "富文本",
    type: ColumnTypeVo.edit,
    span: 24
  },
]
export const HighFields = [
  {
    label: "分割标题",
    type: ColumnTypeVo.divider,
    span: 24
  },
  {
    label: "图片",
    type: ColumnTypeVo.image,
    span: 24
  },
  {
    label:  "文件",
    type: ColumnTypeVo.file,
    span: 24
  },
  {
    label:  "城市",
    type: ColumnTypeVo.city,
    span: 12
  }
]
