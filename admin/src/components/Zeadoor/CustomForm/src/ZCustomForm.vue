<script lang="ts" setup>
import {
  Config,
  ConfigBO,
  setComposeColumn,
  setRuleColumn
} from '@/components/Zeadoor/CustomForm/config'
import {ColumnTypeVo} from '@/components/Zeadoor/interface'
import {propTypes} from '@/utils/propTypes'
import {merge} from 'lodash'
import * as CustomFormApi from '@/api/system/customForm/index'
import * as AreaApi from '@/api/system/area'
import {defaultProps} from '@/utils/tree'

const props = defineProps({
  pageKey: propTypes.string.def(),
  config: propTypes.object.def(),
  isCustom: propTypes.bool.def(false),
  rules: propTypes.object.def(),
  dataId: propTypes.object.def(),
})
const emits = defineEmits(['getDetail', 'init', 'update:rule', 'before-set-columns'])

const _config = ref<ConfigBO>(merge({}, Config, props.config || {}))
const formRef = ref()
const loading = ref(false) // 列表的加载中
const areaList = ref([]) // 地区列表

/**************************
 *  表单属性项目处理逻辑
 * ******************************/
const getColumns = async () => {
  let detailInfo = await CustomFormApi.getByKey(props.pageKey)
  if (detailInfo?.config) {
    _config.value = merge({}, _config.value, JSON.parse(detailInfo.config))
    emits('before-set-columns', _config.value?.form?.columns)
  }
  await setComposeColumns()
}

/**
 * 完善填充字段属性
 */
const setComposeColumns = async () => {
  // 查询地区列表
  if (_config.value.form.columns.find((t: any) => t.type === ColumnTypeVo.city)) {
    areaList.value = await AreaApi.getAreaTree()
  }
  _config.value.form.columns?.forEach((t: any) => {
    setComposeColumn(t)
    setRuleColumn(t, formRules.value)
  })
  emits('update:rule', formRules.value)
}

const formData = ref<any>(_config.value.form?.defaultVal || {})
const formRules = ref<any>(props.rules || {})

/**
 * 查询详情
 */
const getDetail = async () => {
  loading.value = true
  return new Promise((resolve, reject) => {
    emits(
        'getDetail',
        props.dataId,
        (detail: any) => {
          formData.value = detail
          loading.value = false
          resolve()
        },
        () => {
          loading.value = false
          reject()
        }
    )
  })
}

const columnLoading = ref(true)
onMounted(async () => {
  columnLoading.value = true
  if (props.isCustom) {
    await getColumns()
  } else {
    await setComposeColumns()
  }
  columnLoading.value = false
  if (props.dataId) {
    await getDetail()
  }
  emits('init', formData.value, _config.value)
  nextTick(() => {
    formRef.value.clearValidate()
  })
})

/**
 * 判断是否禁用事件
 * @param item
 * @param scope
 */
const isDisabledEvent = (item: any, formData: any) => {
  if (_config.value.form?.disabledProps?.indexOf(item.prop) > -1) return true
  if (item.hasOwnProperty('vDisabled')) {
    if (typeof item.vDisabled === 'function') {
      return item.vDisabled(formData)
    }
    return !!item.vDisabled
  }
  return false
}

/**
 * 判断是否渲染事件
 * @param item
 * @param formData
 */
const isIfEvent = (item: any, formData: any) => {
  if (item.hasOwnProperty('vIf')) {
    if (typeof item.vIf === 'function') {
      return item.vIf(formData)
    }
    return !!item.vIf
  }
  return true
}

const handleClear = (prop: string) => {
  formData.value[prop] = ""
}

defineExpose({
  validate: async () => {
    if (!formRef) return false
    const valid = await formRef.value.validate()
    if (!valid) return false
    return true
  },
  formData: formData.value
})
</script>

<template>
  <el-skeleton v-if="columnLoading" :rows="8" animated/>
  <el-form
      v-else
      ref="formRef"
      v-loading="loading"
      :model="formData"
      :rules="formRules"
      :label-width="_config.form?.labelWidth"
      :label-position="_config.form.labelPosition"
  >
    <el-row :gutter="_config.form?.gutter">
      <template v-for="(item, index) in _config.form?.columns" :key="index">
        <el-col :span="item.span || 24" v-if="item.type === ColumnTypeVo.divider">
          <div class="form-split line">{{ item.label }}</div>
        </el-col>
        <el-col :span="item.span || 12" v-else-if="isIfEvent(item, formData)">
          <el-form-item :label="item.label" :prop="item.prop">
            <slot :name="`form-prop-${item.prop}`" :form="formData" :disabled="isDisabledEvent(item, formData)">
              <el-cascader
                  v-if="item.type === ColumnTypeVo.city"
                  v-model="formData[item.prop]"
                  class="!w-full"
                  :options="areaList"
                  :props="defaultProps"
                  clearable
                  filterable
                  @clear="handleClear(item.prop)"
                  v-bind="item.props"
                  :disabled="isDisabledEvent(item, formData)"
              />
              <component
                  v-else
                  :is="item.component"
                  v-bind="item.props"
                  clearable
                  class="!w-full"
                  @clear="handleClear(item.prop)"
                  :disabled="isDisabledEvent(item, formData)"
                  v-model="formData[item.prop]"
              >
                <template v-if="item.type === ColumnTypeVo.select">
                  <el-option
                      :label="dict.label"
                      :value="dict.value"
                      v-for="dict in item.options"
                      :key="dict.value"
                  />
                </template>
                <template v-if="item.type === ColumnTypeVo.radio">
                  <el-radio
                      :label="dict.label"
                      :value="dict.value"
                      v-for="dict in item.options"
                      :key="dict.value"
                  />
                </template>
                <template v-if="item.type === ColumnTypeVo.checkbox">
                  <el-checkbox
                      :label="dict.label"
                      :value="dict.value"
                      v-for="dict in item.options"
                      :key="dict.value"
                  />
                </template>
              </component>
            </slot>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>

<style lang="scss"></style>
