<template>
  <Dialog v-model="dialogVisible" :title="dataId?'编辑':'新增'" :width="config?.width || 1000">
    <ZCustomForm ref="formRef" v-bind="props" @init="onInit" v-model:rules="formRules" :data-id="dataId"
                 @get-detail="getDetail"
                 @before-set-columns="onBeforeSetColumns">
      <template #[`form-prop-${column.prop}`]="{form, disabled}" v-for="column in columns">
        <slot :name="`form-prop-${column.prop}`" :form="form" :disabled="disabled">
        </slot>
      </template>
    </ZCustomForm>
    <template #footer>
      <el-button :disabled="formLoading" :loading="formLoading" type="primary" @click="onSubmit()">保 存</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>

import {propTypes} from "@/utils/propTypes";

const props = defineProps({
  pageKey: propTypes.string.def(),
  config: propTypes.object.def(),
  isCustom: propTypes.bool.def(false),
  rules: propTypes.object.def(),
})

const dialogVisible = ref(false) // 弹窗的是否展示
const dataId = ref()
const formLoading = ref(false)
const formRef = ref()
const formRules = ref(props.rules)

const emits = defineEmits(['getDetail', 'init', "update:rule", 'before-set-columns'])

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  dataId.value = id
}

const columns = ref([])
const onInit = (formData: any, config: any) => {
  columns.value = config.form?.columns || []
  emits("update:rule", formRules.value)
  emits("init", formData, config)
}
const getDetail = (id: number, done: Function, error: Function) => {
  emits("getDetail", id, done, error)
}
const onBeforeSetColumns = (params: any) => {
  emits("before-set-columns", params)
}
const onSubmit = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return

  formLoading.value = true
  emits("onSave", formRef.value.formData, () => {
    dialogVisible.value = false
    formLoading.value = false
  }, () => {
    formLoading.value = false
  })
}

defineExpose({open, formData: computed(() => {
    return formRef.value?.formData
  })}) // 提供 open 方法，用于打开弹窗
</script>
