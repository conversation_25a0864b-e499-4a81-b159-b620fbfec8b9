<script lang="ts" setup>

import {<PERSON>tBottom, CaretLeft, CaretRight} from "@element-plus/icons-vue";
import dayjs from "dayjs";

const props = defineProps({
  modelValue: {
    type: Array[Date],
    default: []
  }
})
const defaultProps = ref({
  showOverflowTooltip: true,
  stripe: true,
})
const emits = defineEmits(['update:modelValue', "change"]) // 定义 emits

const year = ref(dayjs().format("YYYY"))
const startDate = ref(props.modelValue && props.modelValue[0] ? props.modelValue[0] : null)
const endDate = ref(props.modelValue && props.modelValue[1] ? props.modelValue[1] : null)
const type = ref("quarter")
const value = ref(Math.ceil((dayjs().month() + 1) / 3) + 1)
const showMenu = ref(false)

const preYear = () => {
  year.value = dayjs(year.value).add(-1, "years").format("YYYY")
  if(startDate.value) startDate.value = dayjs(startDate.value).add(-1, "years").format("YYYY-MM-DD")
  if(endDate.value) endDate.value = dayjs(endDate.value).add(-1, "years").format("YYYY-MM-DD")
  emits("update:modelValue", [startDate.value, endDate.value])
  emits("change", [startDate.value, endDate.value])
}
const nextYear = () => {
  year.value = dayjs(year.value).add(1, "years").format("YYYY")
  if(startDate.value) startDate.value = dayjs(startDate.value).add(1, "years").format("YYYY-MM-DD")
  if(endDate.value) endDate.value = dayjs(endDate.value).add(1, "years").format("YYYY-MM-DD")
  emits("update:modelValue", [startDate.value, endDate.value])
  emits("change", [startDate.value, endDate.value])
}

const handleModel = (model: number) => {
  switch (model){
    case 1:
      type.value = "year"
      startDate.value = dayjs(year.value).startOf("year").format("YYYY-MM-DD")
      endDate.value = dayjs(year.value).startOf("year").add(1, "year").format("YYYY-MM-DD")
      break;
    case 2:
    case 3:
    case 4:
    case 5:
      type.value = "quarter"
      startDate.value = dayjs(year.value).startOf("year").add((model-2) * 3, "month").format("YYYY-MM-DD")
      endDate.value = dayjs(year.value).startOf("year").add((model-2) * 3 + 3, "month").format("YYYY-MM-DD")
      break;
  }
  value.value = model
  emits("update:modelValue", [startDate.value, endDate.value])
  emits("change", [startDate.value, endDate.value])
}

const handleMonth = (month: number) => {
  startDate.value = dayjs(year.value).format("YYYY") + "-" + month.toString().padStart(2, '0') + "-01"
  endDate.value = dayjs(dayjs(year.value).format("YYYY") + month.toString().padStart(2, '0') + "-01").add(1, "months").format("YYYY-MM-DD")
  type.value = "month"
  value.value = month
  emits("update:modelValue", [startDate.value, endDate.value])
  emits("change", [startDate.value, endDate.value])
}

const getShowLabel = computed(() => {
  switch (type.value){
    case "year":
      return year.value + "年 年度"
    case "quarter":
      switch (value.value){
        case 2:
          return year.value + "年 第一季度"
        case 3:
          return year.value + "年 第二季度"
        case 4:
          return year.value + "年 第三季度"
        case 5:
          return year.value + "年 第四季度"
      }
      break
    case "month":
      return year.value + "年 " + value.value +"月"
  }
})

onMounted(() => {
  document.addEventListener("click", () => {
    showMenu.value = false
  })
})

</script>

<template>
  <div class="zeadoor-full-month">
    <div class="input" @click.stop="showMenu=!showMenu">{{ getShowLabel }}<el-icon><CaretBottom /></el-icon></div>
    <div class="menu" v-if="showMenu">
      <div class="year">
        <el-icon @click.stop="preYear"><CaretLeft /></el-icon>
        <font>{{year}}</font>
        <el-icon @click.stop="nextYear"><CaretRight /></el-icon>
      </div>

      <div class="model">
        <div class="model-li !w-full" @click="handleModel(1)">年度</div>
        <div class="model-li" @click="handleModel(2)">第一季度</div>
        <div class="model-li" @click="handleModel(3)">第二季度</div>
        <div class="model-li" @click="handleModel(4)">第三季度</div>
        <div class="model-li" @click="handleModel(5)">第四季度</div>
      </div>
      <div class="month">
        <div class="month-li" :key="item" v-for="item in 12" @click="handleMonth(item)">{{item}}月</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.zeadoor-full-month{
  position: relative;
  .input{
    color: var(--el-color-primary);
    padding: 0 20px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    word-break: normal;
    &:hover{
      background: var(--el-color-primary-light-9);
    }
    .el-icon{
      margin-left: 10px;
    }
  }
  .menu{
    position: fixed;
    background: #fff;
    z-index: 11111;
    border: 1px solid var(--el-border-color-light);
    border-radius: 5px;
    box-shadow: var(--el-box-shadow-light);
    margin-top: 10px;
    width: 250px;
    .year{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;
      font{
        margin: 0 10px;
      }
      .el-icon{
        cursor: pointer;
        &:hover{
          color: var(--el-color-primary);
        }
      }
    }
    .model{
      width: 100%;
      display: flex;
      flex-flow: wrap row;
      border-bottom: 1px dashed var(--el-border-color);
      .model-li{
        width: 50%;
        text-align: center;
        cursor: pointer;
        padding: 5px 0;
        &:hover{
          background: #eee;
        }
      }
    }
    .month{
      width: 100%;
      display: flex;
      flex-flow: wrap row;
      padding: 10px 0;
      .month-li{
        width: 33.33%;
        text-align: center;
        cursor: pointer;
        padding: 5px 0;
        &:hover{
          background: #eee;
        }
      }
    }
  }
}
</style>
