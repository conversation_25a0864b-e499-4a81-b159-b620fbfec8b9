<template>
  <div class="z-user-input" v-loading="loading">
    <div class="z-user-input-btn" @click="userSelectRef.open({isMultiple: isMultiple}, userList)" v-if="!readonly">
      <el-icon>
        <Plus/>
      </el-icon>
    </div>
    <div class="z-user-input-list" v-if="userList.length">
      <template v-if="isMultiple && !readonly">已选择</template><font><img :src="userList[0].avatar" v-if="userList[0].avatar"/>{{ userList[0].nickname }}</font>
      <div class="flex" v-if="userList.length>1">等<font>{{ userList.length }}</font>个人员</div>
      <div class="close" @click="userList = []" v-if="!readonly && !hiddenDelete">
        <el-icon size="18">
          <CircleCloseFilled/>
        </el-icon>
      </div>

      <!--      <template v-for="(item, index) in userList" :key="item.id">-->
      <!--        <div class="z-user-input-list-li" v-if="index<2">-->
      <!--          <div class="avatar">-->
      <!--            <img :src="item.avatar" v-if="item.avatar"/>-->
      <!--            <img v-else src="@/assets/imgs/avatar.jpg"/>-->
      <!--          </div>-->
      <!--          <div class="name">{{ item.nickname }}</div>-->
      <!--          <div class="close" @click="userList.splice(index, 1)" v-if="!readonly">-->
      <!--            <el-icon>-->
      <!--              <Close/>-->
      <!--            </el-icon></div>-->
      <!--        </div>-->
      <!--      </template>-->
    </div>
  </div>
  <ZUserSelect ref="userSelectRef" @confirm="onSelectUser" :can-empty="true" :search-condition="searchCondition"/>
</template>

<script setup lang="ts">
import {ref, watch, onMounted} from 'vue'
import {CircleCloseFilled, Close, Plus} from "@element-plus/icons-vue";
import {propTypes} from "@/utils/propTypes";
import * as UserApi from "@/api/oa/personnel/user/user";

// 定义属性
const props = defineProps({
  isMultiple: propTypes.bool.def(false),
  modelValue: propTypes.object.def(),
  isInitSearch: propTypes.bool.def(false),
  readonly: propTypes.bool.def(false),
  isForceMultiple: propTypes.bool.def(false),
  searchCondition: propTypes.object.def(),
  hiddenDelete: propTypes.bool.def(false),
})
const userList = ref<any[]>([]);
const userIdList = ref<any[]>([])
const userSelectRef = ref()
const loading = ref(false)
const inMounted = ref(false)

const getUserList = async () => {
  loading.value = true
  try {
    userList.value = await UserApi.getBookList({
      idList: userIdList.value
    })
    inMounted.value = true
  } finally {
    loading.value = false
  }
}

const emit = defineEmits(['update:modelValue', 'delete', "change"])
const onSelectUser = function (users: any) {
  if (props.isMultiple) {
    if (users && users.length) {
      userList.value = users || []
    } else {
      userList.value = []
    }
  } else {
    userList.value = [users];
  }
  emit("change", userList.value)
}

watch(() => userList.value, function () {
  let modelValue = userList.value.map((t: any) => t.id).toString()
  if (props.isForceMultiple || (props.modelValue && typeof props.modelValue !== "string" && typeof props.modelValue !== "number")) {
    modelValue = userList.value.map((t: any) => t.id)
  }
  emit("update:modelValue", modelValue)
}, {
  deep: true
})

watch(() => props.modelValue, function () {
  onInit()
}, {
  deep: true
})

const onInit = function () {
  if (props.modelValue) {
    if (props.isMultiple) {
      if (typeof props.modelValue === 'string') {
        userIdList.value = props.modelValue.split(",")
      } else {
        userIdList.value = props.modelValue
      }
    } else {
      userIdList.value = [props.modelValue]
    }
    if (props.isInitSearch && !inMounted.value && userIdList.value.length) {
      getUserList()
    }
  } else {
    userList.value = []
    userIdList.value = null
  }
}

onMounted(() => {
  onInit()
})

defineExpose({
  getUserList,
  clear: () => {
    userList.value = []
  }
})

</script>

<style scoped lang="scss">
.z-user-input {
  display: flex;
  align-items: center;
  width: 100%;

  .z-user-input-list {
    display: flex;
    align-items: center;
    flex: 1;

    font {
      color: var(--el-color-primary);
      margin:0 5px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      img{
        width: 15px;
        height: 15px;
        margin-right: 2px;
      }
    }
    .close{
      margin-left: 5px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: var(--el-text-color-disabled);

      &:hover {
        color: var(--el-color-danger);
      }
    }

    .z-user-input-list-li {
      display: flex;
      background: var(--el-bg-color-page);
      border-radius: 100px;
      padding: 0px 10px;
      align-items: center;
      margin-right: 10px;

      .avatar {
        width: 20px;
        height: 20px;
        margin-right: 3px;
        line-height: 0;

        img {
          width: 20px;
          height: 20px;
          border-radius: 100px;
        }
      }

      .name {
        font-size: 12px;
      }

      .close {
        cursor: pointer;
        margin-left: 5px;
        display: flex;
        align-items: center;

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }
  }

  .z-user-input-btn {
    width: 40px;
    height: 30px;
    border-radius: 100px;
    background: var(--el-bg-color-page);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;

    &:hover {
      background: var(--el-color-primary);
      color: #fff;
    }
  }
}
</style>
