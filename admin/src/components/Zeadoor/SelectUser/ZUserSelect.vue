<template>
  <el-dialog
    v-model="showDialog"
    title="选择员工"
    width="700px"
    append-to-body
    class="is-full-dialog"
  >
    <div class="customer-panel" v-loading="loading">
      <div class="customer">
        <div class="customer-search">
          <ZListContainerSearch
              ref="searchRef"
            v-model="searchFrom"
            :hidden-reset="true"
            :hidden-search="true"
            @search="getUserList"
            :columns="searchColumns"
            :show-more="true">
            <template #search-fast>
              <el-form-item label-width="0" prop="searchKey" class="!flex-1">
                <el-input
                  v-model="searchFrom.searchKey"
                  @change="getUserList"
                  class="!flex-1"
                  clearable
                  placeholder="请输入人员名称/手机号"
                />
              </el-form-item>
            </template>
            <!-- 检索条件 -->
            <template #search-prop-deptId="{ column, form }">
              <el-form-item prop="deptId" :label="column.label">
                <el-tree-select
                  v-model="form.deptId"
                  :data="deptList"
                  :props="defaultProps"
                  check-strictly
                  node-key="id"
                  placeholder="请选择所属部门"
                />
              </el-form-item>
            </template>
          </ZListContainerSearch>
        </div>
        <div class="customer-check" v-if="isMultiple">
          <el-checkbox @click="onCheckAll" v-model="getCheckAll">全选</el-checkbox>
        </div>
        <div class="customer-list">
          <template v-if="userList.length">
            <template v-for="item in userList" :key="item.id">
              <div
                class="customer-list-li"
                :class="isSelect(item) ? 'is-checked' : ''"
                @click="handleSelect(item)">
                <div class="customer-list-li-selected">
                  <template v-if="!isMultiple">
                    <i class="oa-iconfont icon-yuanxingxuanzhongfill" style="color: var(--el-color-primary);font-size: 18px;" v-if="isSelect(item)" ></i>
                    <i class="oa-iconfont icon-yuanxingweixuanzhong" style="color: #dadada;font-size: 18px;" v-else ></i>
                  </template>
                  <template v-else>
                    <i class="oa-iconfont icon-fangxingxuanzhongfill" style="color: var(--el-color-primary);font-size: 18px;" v-if="isSelect(item)" ></i>
                    <i class="oa-iconfont icon-fangxingweixuanzhong" style="color: #dadada;font-size: 18px;" v-else ></i>
                  </template>
                </div>
                <div class="customer-list-li-avatar">
                  <img :src="item.avatar" v-if="item.avatar" />
                  <img v-else src="@/assets/imgs/avatar.jpg" />
                </div>
                <div class="customer-list-li-info">
                  <div class="customer-list-li-info-name" >{{ item.nickname}}<font class="work-no" v-if="item.workNo">({{ item.workNo }})</font>
                  </div>
                  <div class="customer-list-li-info-organ">
                    <div class="" v-if="item.deptName">{{item.deptName}}</div>
                    <div class="" v-if="item.mobile">{{item.mobile}}</div>
                    <div class="" v-if="item.positionName">{{item.positionName}}</div>
                  </div>
                </div>
              </div>
            </template>
          </template>
          <el-empty v-else class="!h-full" content="暂无人员数据" />
        </div>
      </div>
      <div class="selected" v-if="isMultiple">
        <div class="selected-title">
          <div class="selected-title-label">已选:{{ selectUserList.length }}个员工</div>
                      <el-button type="primary" link @click="clearSelect">清空</el-button>
        </div>
        <div class="selected-center">
          <div class="item" v-for="(item, index) in selectUserList" :key="item.id">
            <span class="name">{{ item.nickname }}</span>
            <el-icon v-on:click="selectUserList.splice(index, 1)">
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Check, CircleCheck, Close, Search, SuccessFilled } from '@element-plus/icons-vue'
import * as UserApi from '@/api/oa/personnel/user/user'
import { propTypes } from '@/utils/propTypes'
import { SearchColumnTypeVo, SearchColumnVo } from '@/components/Zeadoor/interface'
import { DICT_TYPE } from '@/utils/dict'
import { getEffectPositionPage } from '@/api/oa/personnel/organ/position'
import { getEffectJobPage } from '@/api/oa/personnel/organ/job'
import { getEffectRankPage } from '@/api/oa/personnel/organ/rank'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

const emit = defineEmits(['confirm'])
const loading = ref(false)
const showDialog = ref(false)
const userList = ref<any[]>([])
const selectUserList = ref<any[]>([])
const search = ref()
const isMultiple = ref(false)
const deptList = ref<any[]>([])
const searchRef = ref()

// 定义属性
const props = defineProps({
  searchCondition: propTypes.object.def(),
  canEmpty: propTypes.object.def(false)
})

const searchFrom = ref({
  searchKey: null,
  workType: null,
  workStatus: null
})
const searchColumns = ref<Array<SearchColumnVo>>([
  {
    label: '部门',
    prop: 'deptId'
  },
  {
    label: '职位',
    prop: 'positionId',
    type: SearchColumnTypeVo.select,
    placeholder: "请选择职位",
    props: {
      remote: true,
      remoteMethod: async (searchKey: string, done: any) => {
        const data = await getEffectPositionPage({
          name: searchKey
        })
        done(
          data.list.map((t: any) => {
            return {
              label: t.name,
              value: t.id
            }
          })
        )
      }
    }
  },
  {
    label: '职务',
    prop: 'jobId',
    type: SearchColumnTypeVo.select,
    placeholder: "请选择职务",
    props: {
      remote: true,
      remoteMethod: async (searchKey: string, done: any) => {
        const data = await getEffectJobPage({
          name: searchKey
        })
        done(
          data.list.map((t: any) => {
            return {
              label: t.name,
              value: t.id
            }
          })
        )
      }
    }
  },
  {
    label: '职级',
    prop: 'rankId',
    type: SearchColumnTypeVo.select,
    placeholder: "请选择职级",
    props: {
      remote: true,
      remoteMethod: async (searchKey: string, done: any) => {
        const data = await getEffectRankPage({
          name: searchKey
        })
        done(
          data.list.map((t: any) => {
            return {
              label: t.name,
              value: t.id
            }
          })
        )
      }
    }
  },
  {
    label: '入职日期',
    prop: 'joinDateList',
    type: SearchColumnTypeVo.dateBetween
  },
  {
    label: '员工类型',
    prop: 'workType',
    placeholder: "请选择员工类型",
    type: SearchColumnTypeVo.dictNumber,
    dictKey: DICT_TYPE.TEAMIFY_WORK_TYPE
  },
  {
    label: '工作状态',
    prop: 'workStatus',
    placeholder: "请选择工作状态",
    type: SearchColumnTypeVo.dictNumber,
    dictKey: DICT_TYPE.TEAMIFY_WORK_STATUS
  }
])

const getUserList = async () => {
  loading.value = true
  try {
    userList.value = await UserApi.getBookList(searchFrom.value)
  } finally {
    loading.value = false
  }
}

const handleSelect = function (item: any) {
  if (isMultiple.value) {
    if (!selectUserList.value.find((t: any) => t.id === item.id)) {
      selectUserList.value.push(item)
    } else {
      selectUserList.value.splice(
        selectUserList.value.findIndex((t: any) => t.id === item.id),
        1
      )
    }
  } else {
    selectUserList.value = [item]
  }
}

const clearSelect = function(){
  selectUserList.value = []
}

const isSelect = computed(() => {
  return function (item: any) {
    return !!selectUserList.value.find((t: any) => t.id === item.id)
  }
})

const open = async function (config?: any, uses?: any[]) {
  showDialog.value = true
  isMultiple.value = !!config?.isMultiple
  selectUserList.value = JSON.parse(JSON.stringify(uses || []))
  search.value = null
  userList.value = []
  searchFrom.value = {}
  searchFrom.value = Object.assign({}, searchFrom.value, props.searchCondition)
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  await getUserList()
}

const handleConfirm = function () {
  let val = isMultiple.value ? selectUserList.value : selectUserList.value[0]
  if(!props.canEmpty && ((isMultiple.value && (val == null || !val.length)) || (!isMultiple.value && val == null))){
    ElMessage.error("请选择人员")
    return
  }
  emit('confirm', val)
  showDialog.value = false
}

const getCheckAll = computed(() => {
  let checkAll = true
  userList.value.forEach((t: any) => {
    if(!isSelect.value(t)) checkAll = false
  })
  return checkAll
})
const onCheckAll = function () {
  if(getCheckAll.value){
    userList.value.forEach((t: any) => {
      handleSelect(t)
    })
  } else{
    userList.value.forEach((t: any) => {
      if(!isSelect.value(t)) {
        handleSelect(t)
      }
    })
  }
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.show-customer-list {
  display: flex;
  align-items: center;
  flex-flow: wrap row;
  gap: 10px 10px;

  .show-customer-list-item {
    border: 1px solid var(--el-border-color);
    padding: 0 5px 0 10px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    box-sizing: inherit;
    height: 30px;
    color: var(--el-text-color-regular);

    img {
      width: 18px;
      height: 18px;
      margin-right: 5px;
      border-radius: 100px;
    }

    &:hover {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }

    .el-icon {
      margin-left: 10px;
      cursor: pointer;

      &:hover {
        background: #eee;
        border-radius: 100px;
      }
    }
  }

  button {
    height: 32px;
    width: 100px;
  }
}

.customer-panel {
  display: flex;
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
  height: 100%;

  .customer {
    flex: 1;
    padding: 13px 10px;
    border-right: 1px solid var(--el-border-color);
    display: flex;
    flex-direction: column;

    .customer-search {
      :deep(.zeadoor-list-container-search) {
        .el-card {
          border: 0 !important;

          .el-card__body {
            padding: 0 !important;

            .fast-search-form {
              display: flex;

              .fast-search-form-item {
                flex: 1;
              }
            }
          }
        }
      }
    }

    .customer-check{
      padding: 0 10px 0 12px;
    }

    .customer-list {
      width: 100%;
      flex: 1;
      overflow: auto;
      height: 100%;

      .customer-list-li {
        display: flex;
        padding: 10px 0;
        align-items: center;
        border-bottom: 1px solid var(--el-border-color-extra-light);
        cursor: pointer;

        &.is-checked {
        }

        &:hover {
          background: #fafafa;
        }

        .customer-list-li-avatar {
          width: 40px;
          height: 40px;
          border-radius: 100px;

          img {
            width: 40px;
            height: 40px;
            border-radius: 100px;
          }
        }

        .customer-list-li-info {
          flex: 1;
          margin-left: 15px;

          .customer-list-li-info-name {
            font-size: 15px;
            color: var(--el-text-color-primary);

            .work-no {
              font-size: 12px;
              color: var(--el-text-color-secondary);
              margin-left: 10px;
            }
          }

          .customer-list-li-info-organ {
            color: var(--el-text-color-secondary);
            font-size: 12px;
            margin-top: 5px;
            display: flex;
            align-items: center;
            div{
              margin-right: 5px;
            }
          }
        }

        .customer-list-li-selected {
          padding: 0 10px;

          :deep(.el-icon) {
            font-size: 18px !important;
          }
        }
      }
    }
  }

  .selected {
    width: 300px;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .selected-title {
      display: flex;
      align-items: center;

      .selected-title-label {
        flex: 1;
      }
    }

    .selected-center {
      display: flex;
      flex-flow: wrap row;
      gap: 10px;
      overflow:auto;
      margin-top: 20px;

      .item {
        display: flex;
        align-items: center;
        padding: 5px 10px;
        background: #2561ef;
        color: #fff;
        border-radius: 4px;
        font-size: 12px;
        height: fit-content;

        .name::after {
          content: '|';
          margin: 0 5px;
          opacity: 0.5;
        }

        .el-icon {
          cursor: pointer;
        }
      }
    }
  }
}
</style>
