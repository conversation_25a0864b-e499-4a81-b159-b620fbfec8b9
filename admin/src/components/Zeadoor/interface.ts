export interface ColumnVo {
  label: string // "标题"
  prop: string // "字段名"
  width?: number | string // "宽度"
  minWidth?: number | string // "最小宽度"
  type?: ColumnTypeVo // "类型"
  dictKey?: string // 字典key
  hidden?: boolean // 是否隐藏
  vIf?: boolean | Function // 判断条件
  align?: string // 位置
  span?: string // span
  click?: Function // 点击事件
}

export enum ColumnTypeVo {
  text = 'text',
  textarea = 'textarea',
  dict = 'dict',
  dictText = 'dictText',
  money = 'money',
  number = 'number',
  date = 'date',
  datetime = 'datetime',
  select = 'select',
  radio = 'radio',
  checkbox = 'checkbox',
  edit = 'edit',
  file = 'file',
  image = 'image',
  divider = 'divider',
  city = 'city',
  user = 'user',
}

export interface SearchColumnVo {
  label: string // "标题"
  prop: string // "字段名"
  type?: SearchColumnTypeVo // "类型"
  dictKey?: string // 字典key
  hidden?: boolean // 是否隐藏
  props?: any // 配置
  options?: any[] // 下拉选项
  placeholder?: string // 提示
}

export enum SearchColumnTypeVo {
  text = 'text',
  dict = 'dict',
  dictNumber = 'dictNumber',
  date = 'date',
  dateBetween = 'dateBetween',
  datetime = 'datetime',
  select = 'select',
  tree = 'tree'
}
