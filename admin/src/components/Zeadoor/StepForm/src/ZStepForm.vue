<script lang="ts" setup>
import {useDesign} from '@/hooks/web/useDesign'
import {ColumnVo} from "@/components/Zeadoor/interface";

defineOptions({name: 'ZStepForm'})

const {getPrefixCls} = useDesign()

const prefixCls = getPrefixCls('step-form')

const props = defineProps({
  showIndex: {
    type: Boolean,
    default: true
  },
  columns: {
    type: Array<ColumnVo>,
    default: []
  },
  name: {
    type: String,
    default: null
  }
})
const defaultProps = ref({
  showOverflowTooltip: true,
  stripe: true,
})
</script>

<template>
  <div class="zeadoor-step-form">
    <div class="zeadoor-step-form-head">
      <div class="zeadoor-step-form-head-back">
        <el-button @click="$router.back()">返回</el-button>
      </div>
      <div class="zeadoor-step-form-head-name">
        <slot name="name">
          {{ name }}
        </slot>
      </div>
      <div class="zeadoor-step-form-head-tab">
        <slot name="tab">
        </slot>
      </div>
    </div>
    <div class="zeadoor-step-form-body">
      <slot name="form"></slot>
    </div>
    <div class="zeadoor-step-footer">
      <div class="zeadoor-step-footer-left"></div>
      <div class="zeadoor-step-footer-center">
        <slot name="tool-btn">
        </slot>
      </div>
      <div class="zeadoor-step-footer-right"></div>
    </div>
  </div>
</template>

<style lang="scss">
.zeadoor-step-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--top-tool-height) - var(--breadcrumb-height) - 25px) !important;

  .zeadoor-step-form-head {
    background: #fff;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 10px;
    border-radius: 5px;

    .zeadoor-step-form-head-back {
      z-index: 1;
      display: flex;
      align-items: center;
      height: 40px;
    }

    .zeadoor-step-form-head-name {
      z-index: 1;
      margin-left: 10px;
    }

    .zeadoor-step-form-head-tab {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .tab-item{
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 20px;
        cursor: pointer;
        position: relative;
        color: var(--el-text-color-secondary);
        &.is-active{
          color: var(--el-color-primary);
        }
        &:last-child:after{
          display: none;
        }
        &:after{
          content: "▶︎";
          position: absolute;
          right: -5px;
          font-size: 12px;
          transform: scale(0.8);
        }
      }
    }
  }

  .zeadoor-step-form-body {
    padding: 20px 0;
    flex: 1;
    height: 0;
    overflow: auto;
  }

  .zeadoor-step-footer {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 10px 0;
    border-radius: 5px;

    .zeadoor-step-footer-left {
    }

    .zeadoor-step-footer-center {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .zeadoor-step-footer-right {
    }
  }
}
</style>
