<script lang="ts" setup>
import { ColumnTypeVo, ColumnVo } from '@/components/Zeadoor/interface'
import { erpNumberFormatter, formatMoney } from '@/utils'
import dayjs from 'dayjs'

defineOptions({ name: 'ZTable' })
const tableRef = ref()

const props = defineProps({
  showIndex: {
    type: Boolean,
    default: true
  },
  showSelection: {
    type: Boolean,
    default: false
  },
  columns: {
    type: Array<ColumnVo>,
    default: []
  },
  spanMethod: {
    type: Function,
    default: null
  },
  tableRowClassName: {
    type: Function,
    default: null
  },
  selectable: {
    type: Function,
    default: null
  }
})
const defaultProps = ref({
  showOverflowTooltip: true,
  stripe: true
})

const getColumnShowCondition = function (column: ColumnVo) {
  let show = true
  if (typeof column.vIf === 'function') {
    show = column.vIf(column)
  }
  return !column.hidden && show
}

const emit = defineEmits(['cellClick', 'selectionChange', 'select', 'selectAll'])

const onClickRow = function (column: ColumnVo, row: any) {
  if (column.hasOwnProperty('click')) {
    column.click(row)
  }
}

// 添加行合并方法
const objectSpanMethod = ({ row, rowIndex, columnIndex }: SpanMethodProps) => {
  return props.spanMethod
    ? props.spanMethod({ row, rowIndex, columnIndex })
    : {
        rowspan: 1,
        colspan: 1
      }
}

const tableRowClassName = (row, data) => {
  return props.tableRowClassName ? props.tableRowClassName(row, data) : ''
}

const selectable = (row: any) => {
  return props.selectable ? props.selectable(row) : true
}

const getShowColumns = computed(() => {
  return props.columns.filter((t:any) => t.type !== ColumnTypeVo.divider)
})

defineExpose({ ref: tableRef })
</script>

<template>
  <el-table
    v-bind="Object.assign({}, defaultProps, props)"
    ref="tableRef"
    row-key="id"
    :span-method="objectSpanMethod"
    :row-class-name="tableRowClassName"
    @cell-click="emit('cellClick', $event)"
    @select="(selection: any[], row: any) => emit('select', selection, row)"
    @select-all="(selectionList: any[]) => emit('selectAll', selectionList)"
    @selection-change="emit('selectionChange', $event)"
  >
    <el-table-column label="#" width="50" type="index" v-if="showIndex" />
    <el-table-column
      type="selection"
      width="55"
      :reserve-selection="true"
      v-if="showSelection"
      :selectable="selectable"
    />
    <template v-for="column in getShowColumns">
      <el-table-column :key="column.prop" v-bind="column" v-if="getColumnShowCondition(column)">
        <template #default="scope">
          <slot :name="`column-prop-${column.prop}`" :row="scope.row">
            <div
              @click="onClickRow(column, scope.row)"
              :class="column.hasOwnProperty('click') ? 'is-link' : ''"
            >
              <dict-tag
                v-if="column.type === ColumnTypeVo.dict"
                :type="column.dictKey"
                :value="scope.row[column.prop]"
              />
              <dict-tag
                v-else-if="column.type === ColumnTypeVo.dictText"
                :type="column.dictKey"
                cssClass="text"
                :value="scope.row[column.prop]"
              />
              <span class="column-money" v-else-if="column.type === ColumnTypeVo.money">{{
                formatMoney(scope.row[column.prop])
              }}</span>
              <span class="column-number" v-else-if="column.type === ColumnTypeVo.number">{{
                scope.row[column.prop] ? erpNumberFormatter(scope.row[column.prop], 2) : 0
              }}</span>
              <span class="column-date" v-else-if="column.type === ColumnTypeVo.date">{{
                  scope.row[column.prop] ? dayjs(scope.row[column.prop]).format('YYYY-MM-DD') : ""
              }}</span>
              <span class="column-date" v-else-if="column.type === ColumnTypeVo.datetime">{{
                  scope.row[column.prop] ? dayjs(scope.row[column.prop]).format('YYYY-MM-DD HH:mm:ss') : ""
                }}</span>
              <span v-else>{{ scope.row[column.prop] }}</span>
            </div>
          </slot>
        </template>
      </el-table-column>
    </template>
    <slot></slot>
    <slot name="column-tool"></slot>
  </el-table>
</template>

<style lang="scss" scoped>
.is-link {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>
