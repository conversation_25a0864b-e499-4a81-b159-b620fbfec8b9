import {ColumnVo} from '@/components/Zeadoor/interface'
import { SearchBO } from '@/components/Zeadoor/CustomSearch/config'

export interface PageBO {
    show?: Boolean
}

export interface TableBO {
    showSelection?: Boolean // 是否显示多选框
    toolbar?: TableToolBO // 操作栏目
    multipleToolBar?: TableMultipleToolBO //批量操作栏
    columns?: Array<ColumnVo>  // 表格列
}

export interface TableToolBO {
    show?: Boolean //是否显示
    label?: String //操作栏标题
    width?: Number //宽度
    fixed?: Boolean | String // 位置
    buttons?: Array<TableToolButtonBO | string> // 操作按钮
}

export interface TableMultipleToolBO {
    show?: Boolean //是否显示
    buttons?: Array<TableToolButtonBO | string> // 操作按钮
}

export interface TableToolButtonBO {
    key?: String // key
    label?: String // 按钮名称
    type?: String // 类型
    link?: Boolean // 链接类型
    click?: Function // 点击事件
    plain?: Boolean // 朴素按钮
    hasPermission?: any[] //权限
    vDisabled?: Function | Boolean// 是否禁用
    vIf?: Function | Boolean // 是否显示

}

export interface ConfigBO {
    page?: PageBO
    table?: TableBO,
    search?: SearchBO
}

export const Config: ConfigBO = {
    page: {
        show: true
    },
    search: {
        show: true
    },
    table: {
        showSelection: false,
        columns: [],
        toolbar: {
            show: true,
            label: '操作',
            width: 120,
            fixed: 'right',
        },
        multipleToolBar: {
            show: false,
        }
    }
}
