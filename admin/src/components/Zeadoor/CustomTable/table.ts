import { TableToolButtonBO } from '@/components/Zeadoor/CustomTable/config'

export const TableToolUpdateButton: TableToolButtonBO = {
  key: "update",
  label: '编辑',
  type: 'primary',
  link: true,
}
export const TableToolDeleteButton: TableToolButtonBO = {
  key: "delete",
  label: '删除',
  type: 'danger',
  link: true
}
export const TableToolApprovalButton: TableToolButtonBO = {
  key: "approval",
  label: '发起审批',
  type: 'primary',
  link: true
}
export const TableMultipleToolDeleteButton: TableToolButtonBO = {
  key: "batchDelete",
  label: '批量删除',
  type: 'danger',
  plain: true
}
export const SearchToolAddButton: TableToolButtonBO = {
  key: "add",
  type: "primary",
  label: '新增',
}
export const SearchToolImportButton: TableToolButtonBO = {
  key: "import",
  label: '导入',
}
export const SearchToolExportButton: TableToolButtonBO = {
  key: "export",
  label: '导出',
}
