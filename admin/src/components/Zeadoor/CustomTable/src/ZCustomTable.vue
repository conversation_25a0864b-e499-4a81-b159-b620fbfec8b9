<script lang="ts" setup>
import {Config, ConfigBO, TableToolButtonBO} from '@/components/Zeadoor/CustomTable/config'
import {ColumnVo} from '@/components/Zeadoor/interface'
import {propTypes} from '@/utils/propTypes'
import {merge} from 'lodash'
import * as CustomFormApi from '@/api/system/customForm'
import {DataTypeEnum} from '@/components/Zeadoor/CustomForm/config'
import ZImport from '@/components/Zeadoor/Import/src/ZImport.vue'
import {ArrowDown} from '@element-plus/icons-vue'
import * as ImportExportApi from "@/api/system/importExport/index";

const props = defineProps({
  pageKey: propTypes.string.def(),
  config: propTypes.object.def(),
  isCustom: propTypes.bool.def(false)
})
const emits = defineEmits([
  'getList',
  'delete',
  'before-set-columns',
  'import',
  'selection-change',
  'select',
  'select-all',
  'handleQuery'
])

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const _config = ref<ConfigBO>(merge({}, Config, props.config))
const tableRef = ref()
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<Array<any>>([]) // 列表的数据
const allColumns = ref<Array<ColumnVo>>([])
const queryParams = ref(merge({}, {
  pageNo: 1,
  pageSize: 20,
  searchKey: null
}, _config.value?.search?.defaultQuery))

// 按钮折叠
const visibleButtonCount = 5 // 直接显示的按钮数量
const visibleButtons = computed(() => {
  return _config.value?.table?.multipleToolBar?.buttons?.slice(0, visibleButtonCount) || []
})
const hiddenButtons = computed(() => {
  return _config.value?.table?.multipleToolBar?.buttons?.slice(visibleButtonCount) || []
})

const getColumns = async () => {
  let detailInfo = await CustomFormApi.getByKey(props.pageKey)
  if (detailInfo?.config) {
    let config = JSON.parse(detailInfo.config)
    emits('before-set-columns', config?.form?.columns)
    allColumns.value = config?.form?.columns
  }
  _config.value.table.columns = allColumns.value.filter((t: any) => !t.hidden)
}

/***********************************
 *            表格数据操作
 ***********************************/

/**
 * 表格操作按钮点击事件
 * @param scope       数据参数
 * @param button      按钮参数
 * @param index       按钮索引
 */
const handleTableToolBtnClick = async (scope: any, button: TableToolButtonBO, index: number) => {
  if (button.click) button.click(scope, button, index)
  else {
    switch (button.key) {
      case 'delete':
        handleDelete([scope.row.id])
        break
    }
  }
}

/**
 * 判断是否禁用事件
 * @param item
 * @param scope
 */
const isDisabledEvent = (item: any, scope: any) => {
  if (item.hasOwnProperty('vDisabled')) {
    if (typeof item.vDisabled === 'function') {
      return item.vDisabled(scope)
    }
    return !!item.vDisabled
  }
  return false
}

/**
 * 判断是否渲染事件
 * @param item
 * @param scope
 */
const isIfEvent = (item: any, scope: any) => {
  if (item.hasOwnProperty('vIf')) {
    if (typeof item.vIf === 'function') {
      return item.vIf(scope)
    }
    return !!item.vIf
  }
  return true
}

/***********************************
 *            批量操作
 ***********************************/
const getCheckAll = computed(() => {
  return list.value.length && tableRef.value.ref?.getSelectionRows().length === list.value.length
})
const onCheckAll = function () {
  tableRef.value.ref.toggleAllSelection()
}
const hasChecked = computed(() => {
  return tableRef.value.ref.getSelectionRows().length
})

/**
 * 搜索工具栏按钮点击事件
 * @param button    按钮参数
 * @param index     按钮索引
 */
const handleSearchToolBtnClick = (button: TableToolButtonBO, index: number) => {
  if (button.click) button.click(button, index)
  else {
    switch (button.key) {
      case 'export':
        handleExport()
        break
    }
  }
}

/**
 * 批量按钮点击事件
 * @param button    按钮参数
 * @param index     按钮索引
 */
const handleTableMultipleToolBtnClick = (button: TableToolButtonBO, index: number) => {
  let ids = tableRef.value.ref.getSelectionRows().map((t: any) => t.id)
  if (button.click) button.click(ids, button, index)
  else {
    switch (button.key) {
      case 'batchDelete':
        handleDelete(ids)
        break
    }
  }
}

/**
 * 批量更多按钮点击事件
 * @param command 命令参数
 */
const handleMoreCommand = (command: any) => {
  const {item, index} = command
  handleTableMultipleToolBtnClick(item, index)
}

/***********************************
 *            事件处理
 ***********************************/

/** 搜索按钮操作 */
const handleQuery = (conditions?: any) => {
  queryParams.value.pageNo = 1
  if (conditions) queryParams.value = merge({}, queryParams.value, conditions)
  emits('handleQuery', queryParams.value)
  getList()
}

/** 处理选中数据 */
const handleRowSelection = (row: any, selected?: boolean, ignoreSelectable?: boolean) => {
  tableRef.value.ref?.toggleRowSelection(row, selected, ignoreSelectable)
}

/**
 * 查询数据列表
 */
const getList = async () => {
  loading.value = true
  tableRef.value?.ref?.clearSelection()
  try {
    emits(
        'getList',
        queryParams.value,
        (dataList: Array<any>, dataTotal: number) => {
          list.value = dataList
          total.value = dataTotal
          loading.value = false
        },
        () => {
          console.log('数据查询失败')
          loading.value = false
        }
    )
  } finally {
  }
}

/**
 * 删除数据
 * @param ids id集合
 */
const handleDelete = async (ids: number[]) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    emits(
        'delete',
        ids,
        () => {
          message.success(t('common.delSuccess'))
          // 刷新列表
          getList()
        },
        () => {
          console.log('数据查询失败')
        }
    )
  } catch {
  }
}

/**
 * 导入文件
 * @param file
 */
const handleImport = async (sourceFormData: FormData) => {
  try {
    // 1. 从源 FormData 中获取文件
    const file = sourceFormData.get('file') as File;
    if (!file) {
      message.error('未找到上传的文件');
      return;
    }
    // 2. 创建新的 FormData 并添加文件和其他参数
    const newFormData = new FormData();
    newFormData.append('file', file); // 添加文件
    newFormData.append('type', props.pageKey); // 添加额外参数
    // 4. 发送请求
    await ImportExportApi.importData(newFormData);
  } catch (error) {
    console.error('导入失败:', error);
  }
};

/**
 * 导入文件
 * @param file
 */
const handleExport = async () => {
  try {
    await ImportExportApi.exportData({
      type: props.pageKey,
      condition: queryParams.value,
    })
    message.success(t('导出任务创建成功'))
  } catch {
  }
}

const columnLoading = ref(true)
onMounted(async () => {
  columnLoading.value = true
  if (props.isCustom) {
    await getColumns()
  }
  columnLoading.value = false
  await getList()
})

defineExpose({
  reload: handleQuery,
  handleRowSelection: handleRowSelection,
})
</script>

<template>
  <ZListContainer>
    <template #header>
      <slot name="header" :query-form="queryParams"></slot>
    </template>
    <template #table-tree>
      <slot name="table-tree" :query-form="queryParams"></slot>
    </template>
    <template #search>
      <ZCustomSearch
          v-model="queryParams"
          v-model:columns="_config.search.columns"
          @search="handleQuery"
          :is-custom="isCustom"
          :page-key="pageKey"
          :config="_config.search"
          v-if="_config?.search?.show"
      >
        <template #[`search-prop-${s.prop}`]="{ column, form, disabled }" v-for="s in _config.search?.columns">
          <slot :name="`search-prop-${s.prop}`" :column="column" :form="form" :disabled="disabled"></slot>
        </template>
        <template #search-fast>
          <slot name="search-fast"></slot>
        </template>
        <template #search-tool>
          <slot name="search-tool">
            <template v-for="(item, index) in _config?.search?.toolbar?.buttons" :key="index">
              <ZImport
                  :hasPermi="item.hasPermission || null"
                  v-if="(item.key === 'import') && isIfEvent(item, queryParams)"
                  :type="pageKey"
                  class="m-r-2"
                  :label="item.label"
                  template-name="导入模板"
                  :import="handleImport"
                  @success="handleQuery"
              />
              <el-button
                  v-bind="item"
                  v-hasPermi="item.hasPermission || null"
                  class="m-r-2"
                  v-else-if="isIfEvent(item, queryParams)"
                  :disabled="isDisabledEvent(item, queryParams)"
                  @click="handleSearchToolBtnClick(item, index)"
              >
                {{ item.label }}
              </el-button>
            </template>
          </slot>
        </template>
      </ZCustomSearch>
    </template>
    <template #table>
      <el-skeleton v-if="columnLoading" :rows="10" animated/>
      <slot name="table" :list="list" :total="total" v-else>
        <ZTable
            v-loading="loading"
            :data="list"
            v-bind="_config?.table"
            ref="tableRef"
            :columns="_config.table?.columns"
            @select="(selection: any[], row: any) => emits('select', selection, row)"
            @select-all="(selectionList: any[]) => emits('select-all', selectionList)"
            @selection-change="emits('selection-change', $event)"
        >
          <template
              #[`column-prop-${column.prop}`]="{ row }"
              v-for="column in _config.table?.columns"
          >
            <slot :name="`column-prop-${column.prop}`" :row="row">
              <dict-tag
                  v-if="column.dataType === DataTypeEnum.dict"
                  :type="column.dictKey"
                  :value="row[column.prop]"
              />
            </slot>
          </template>
          <template #column-tool>
            <slot name="column-tool">
              <el-table-column v-bind="_config?.table?.toolbar" v-if="_config?.table?.toolbar?.show">
                <template #default="scope">
                  <template v-for="(item, index) in _config?.table?.toolbar?.buttons" :key="index">
                    <el-button
                        v-bind="item"
                        v-if="isIfEvent(item, scope)"
                        :disabled="isDisabledEvent(item, scope)"
                        v-hasPermi="item.hasPermission || null"
                        @click="handleTableToolBtnClick(scope, item, index)"
                    >
                      {{ item.label || '' }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </slot>
          </template>
        </ZTable>
      </slot>
    </template>
    <template #page>
      <div class="is-multiple" v-if="total > 0">
        <div class="btn">
          <template v-if="_config?.table?.multipleToolBar?.show">
            <div class="btn-all">
              <el-checkbox @click="onCheckAll" v-model="getCheckAll">全选本页</el-checkbox>
            </div>
            <div class="btn-list">
              <template v-if="hasChecked">
                <el-button
                    v-for="(item, index) in visibleButtons"
                    :key="index"
                    v-bind="item"
                    v-hasPermi="item.hasPermission || null"
                    @click="handleTableMultipleToolBtnClick(item, index)"
                >
                  {{ item.label }}
                </el-button>
                <!-- 更多按钮下拉菜单 -->
                <el-dropdown v-if="hiddenButtons.length > 0" @command="handleMoreCommand">
                  <el-button size="default" plain>
                    更多
                    <el-icon class="el-icon--right">
                      <arrow-down/>
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                          v-for="(item, index) in hiddenButtons"
                          :key="index"
                          :command="{ item, index: index + visibleButtonCount }"
                      >
                        {{ item.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </div>
          </template>
        </div>
        <div class="page" v-if="_config?.page?.show">
          <!-- 分页 -->
          <Pagination
              v-model:limit="queryParams.pageSize"
              v-model:page="queryParams.pageNo"
              :total="total"
              @pagination="getList"
          />
        </div>
      </div>
    </template>
  </ZListContainer>
</template>

<style lang="scss">
.is-multiple .btn .btn-list {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-button {
    margin-left: 0;
    margin-right: 0;
  }
}
</style>
