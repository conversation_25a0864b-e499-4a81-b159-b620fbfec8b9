<script lang="ts" setup>
import { useDesign } from '@/hooks/web/useDesign'

defineOptions({ name: 'ZListContainer' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('list-container')

defineProps({})
</script>

<template>
  <div class="zeadoor-list-container">
    <div class="zeadoor-list-container-tree">
      <slot name="tree"></slot>
    </div>
    <div class="zeadoor-list-container-main">
      <div class="header-panel">
        <slot name="header"></slot>
      </div>
      <div class="main-panel">
        <slot name="main">
          <slot name="search"></slot>
          <slot name="table-top"></slot>
          <div class="zeadoor-list-container-table">
            <div class="table-tree">
              <slot name="table-tree"></slot>
            </div>
            <slot name="table-out">
              <ContentWrap>
                <div class="table-center">
                  <slot name="table"></slot>
                </div>
                <div class="table-page">
                  <slot name="page"></slot>
                </div>
              </ContentWrap>
            </slot>
          </div>
        </slot>
      </div>
    </div>
    <div class="zeadoor-list-container-right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<style lang="scss">
</style>
