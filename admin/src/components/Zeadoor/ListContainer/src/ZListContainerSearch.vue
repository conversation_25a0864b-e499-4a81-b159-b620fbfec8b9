<script lang="ts" setup>
import {useDesign} from '@/hooks/web/useDesign'
import {propTypes} from "@/utils/propTypes";
import {SearchColumnTypeVo, SearchColumnVo} from "@/components/Zeadoor/interface";
import {getIntDictOptions, getStrDictOptions} from "@/utils/dict";

defineOptions({name: 'ZListContainerSearch'})

const {getPrefixCls} = useDesign()

const prefixCls = getPrefixCls('list-container-search')

const props = defineProps({
  modelValue: propTypes.object.def({}),
  columns: {
    type: Array<SearchColumnVo>,
    default: []
  },
  showMore: {
    type: Boolean,
    default: false
  },
  hiddenSearch: {
    type: Boolean,
    default: false
  },
  hiddenReset: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(["search", "update:modelValue"])

const queryFormRef = ref()
const queryMoreFormRef = ref()

const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryMoreFormRef.value?.resetFields()
  emit('update:modelValue', formData.value)
  emit('search')
  showFilter.value = false
}

const formData = ref<any>(props.modelValue)
onMounted(() => {
  if (props.columns && props.columns.length) {
    props.columns.forEach((t: SearchColumnVo) => {
      if (t.props && t.props.remoteMethod) {
        let remoteMethod = t.props?.remoteMethod;
        t.props.remoteMethod = ($event: string) => {
          remoteMethod($event, function (dataList) {
            t.options = dataList || []
          })
        }
      }
    })
  }
})

const showFilter = ref(false)
const getSelectOptions = computed(() => {
  return  function (column: SearchColumnVo) {
    switch (column.type) {
      case SearchColumnTypeVo.dict:
        return getStrDictOptions(column.dictKey);
      case SearchColumnTypeVo.dictNumber:
        return getIntDictOptions(column.dictKey);
      case SearchColumnTypeVo.select:
        return column.options || []
    }
  }
})

const onConfirm = function (){
  showFilter.value = false
  emit('update:modelValue', formData.value)
  emit('search')
}

const onOpenFilter = function (){
  formData.value = Object.assign({}, props.modelValue)
}

const getSearchCount = computed(() => {
  let count = 0;
  for(let key in props.modelValue){
    let find = props.columns?.find((t:any) => t.prop === key)
    if(find){
      if(Array.isArray(props.modelValue[key])){
        if(props.modelValue[key].length){
          count ++
        }
      } else if(props.modelValue[key]){
        count ++
      }
    }
  }
  return count
})
</script>

<template>
  <div class="zeadoor-list-container-search">
    <ContentWrap>
      <slot name="search">
        <el-form ref="queryFormRef" :model="formData" label-width="68px"  @submit.prevent>
          <div class="zeadoor-list-container-search-top">
            <div class="fast-search-form">
              <div class="fast-search-form-item">
                <slot name="search-fast"></slot>
              </div>
              <div class="fast-search-form-search">
                <el-button @click="emit('search')" v-if="!hiddenSearch">
                  <Icon class="mr-5px" icon="ep:search"/>
                  搜索
                </el-button>
                <el-button @click="resetQuery" v-if="!hiddenReset">
                  <Icon class="mr-5px" icon="ep:refresh"/>
                  重置
                </el-button>
                <el-button v-if="showMore" @click="showFilter = true" :type="getSearchCount>0?'primary':''" :plain="getSearchCount>0">
                  <Icon icon="ep:filter"/>{{getSearchCount || ''}}
                </el-button>
              </div>
            </div>
            <div class="fast-search-tool">
              <slot name="search-tool"></slot>
            </div>
          </div>
          <div class="zeadoor-list-container-search-more">
            <slot name="search-more"></slot>
          </div>
        </el-form>
      </slot>
    </ContentWrap>

    <el-drawer
      class="z-detail-drawer"
      size="500px"
      v-model="showFilter"
      @open="onOpenFilter"
      title="筛选">
      <div class="search-form">
        <div class="search-form-center">
          <el-form ref="queryMoreFormRef" :model="formData" label-position="top">
            <slot :name="`search-filter`" :form="formData">
              <template v-for="item in columns" :key="item">
                <slot :name="`search-prop-${ item.prop }`" :column="item" :form="formData">
                  <el-form-item :prop="item.prop" :label="item.label">
                    <el-select
                      v-if="item.type===SearchColumnTypeVo.dict || item.type === SearchColumnTypeVo.dictNumber || item.type===SearchColumnTypeVo.select"
                      v-bind="item.props || {}"
                      v-model="formData[item.prop]" filterable clearable
                      :placeholder="item.placeholder">
                      <el-option :label="dict.label" :value="dict.value" v-for="dict in getSelectOptions(item)" :key="dict.value"/>
                    </el-select>
                    <el-date-picker type="daterange" v-else-if="item.type === SearchColumnTypeVo.dateBetween"
                                    v-model="formData[item.prop]" clearable
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="YYYY-MM-DD"
                    />
                    <el-date-picker type="datetimerange" v-else-if="item.type === SearchColumnTypeVo.datetimeBetween"
                                    v-model="formData[item.prop]" clearable
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
                                    :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                    />
                    <el-input v-else v-model="formData[item.prop]" clearable :placeholder="item.placeholder"/>
                  </el-form-item>
                </slot>
              </template>
            </slot>
          </el-form>
        </div>
        <div class="search-form-footer">
          <el-button type="primary" @click="onConfirm">确定</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss">
.zeadoor-list-container-search {
  .el-card{
    .el-card__body{
      padding: 15px !important;
    }
  }
  .zeadoor-list-container-search-top {
    display: flex;
    align-items: center;

    .fast-search-form {
      flex: 1;
      display: flex;
      align-items: center;

      .fast-search-form-item {
        display: flex;
        align-items: center;
      }

      .fast-search-form-search {
        margin-left: 10px;
      }

      .el-form-item {
        margin-bottom: 0 !important;
      }
    }

    .fast-search-tool {
      display: flex;
      align-items: center;
      .el-button{
        margin: 0 0 0 10px;
      }
    }
  }

  .zeadoor-list-container-search-more {
  }
}

.search-form{
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-form-center{
    flex: 1;
    overflow: auto;
    height: 0;
  }
  .search-form-footer{
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
