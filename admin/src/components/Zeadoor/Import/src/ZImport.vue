<template>
  <el-button @click="open" class="m-r-3" v-hasPermi="hasPermi || ['']">
    <Icon class="mr-5px" icon="ep:upload"/>
    导入
  </el-button>
  <Dialog v-model="dialogVisible" title="数据导入" width="400">
    <div class="download-template" @click="downloadTemplate" v-loading="downloadTemplateLoading">
      <el-icon>
        <Download/>
      </el-icon>
      下载导入模板
    </div>
    <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        :auto-upload="false"
        :disabled="formLoading"
        :limit="1"
        :on-exceed="handleExceed"
        accept=".xlsx, .xls"
        action="none"
        drag>
      <Icon icon="ep:upload"/>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">
          <span>仅允许导入 xls、xlsx 格式文件。</span>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import {useDesign} from '@/hooks/web/useDesign'
import {SystemModelEnum} from "@/views/oa/utils/constants";
import {UploadUserFile} from "element-plus";
import * as CustomerApi from "@/api/crm/customer";
import download from "@/utils/download";
import {downloadCrmImportTemplate, downloadImportTemplate} from "@/api/oa/system/import/import";

defineOptions({name: 'ZImport'})

const {getPrefixCls} = useDesign()

const prefixCls = getPrefixCls('import')
const formLoading = ref(false)

const props = defineProps({
  type: {
    type: SystemModelEnum,
    default: null
  },
  templateName: {
    type: String,
    default: "导入模板"
  },
  import: {
    type: Function,
    default: null
  },
  hasPermi: {
    type: Object,
    default: null
  },
  importParams: {
    type: Object,
    default: null
  }
})


const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const uploadRef = ref()
const fileList = ref<UploadUserFile[]>([]) // 文件列表
const emits = defineEmits(['success', 'import'])

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  await resetForm()
}

/** 提交表单 */
const submitForm = async () => {
  if (fileList.value.length == 0) {
    message.error('请上传文件')
    return
  }

  formLoading.value = true
  try {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw as Blob)
    if(props.importParams){
      for (let key in props.importParams) {
        formData.append(key, props.importParams[key])
      }
    }

    const res = await props.import(formData)
      message.success('导入任务添加成功！请到任务中心查看')

  } catch {
    // message.error('导入失败！')
  } finally {
    formLoading.value = false
    dialogVisible.value = false
  }
}

/** 重置表单 */
const resetForm = async () => {
  // 重置上传状态和文件
  fileList.value = []
  await nextTick()
  uploadRef.value?.clearFiles()
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个文件！')
}

const downloadTemplateLoading = ref(false)
/** 下载模板操作 */
const downloadTemplate = async () => {
  try {
    downloadTemplateLoading.value = true
    const res = await downloadCrmImportTemplate({type: props.type})
    download.excel(res, crmEntityMap.get(props.type) + '.xlsx')
  } finally {
    downloadTemplateLoading.value = false
  }
}
const crmEntityMap = new Map([
  ['crmClue', '线索导入模板'],
  ['crmCustomer', '客户导入模板'],
  ['crmContact', '联系人导入模板'],
  ['crmBusiness', '商机导入模板'],
  ['crmContract', '合同导入模板'],
  ['crmProduct', '产品导入模板'],
  ['crmReceivable', '回款导入模板'],
  ['crmReceivablePlan', '回款计划导入模板'],
  ['crmVisit', '回访导入模板'],
  ['crmInvoice', '发票导入模板']
]);
</script>
<style lang="scss">
.download-template {
  border: 1px dashed var(--el-border-color);
  margin-bottom: 10px;
  border-radius: 5px;
  padding: 10px 0;
  cursor: pointer;
}
</style>
