<script lang="ts" setup>
import {
  Config,
  ConfigBO,
  DataTypeEnum,
  setComposeColumn
} from '@/components/Zeadoor/CustomForm/config'
import { ColumnTypeVo } from '@/components/Zeadoor/interface'
import { propTypes } from '@/utils/propTypes'
import { merge } from 'lodash'
import * as CustomFormApi from '@/api/system/customForm/index'
import * as AreaApi from '@/api/system/area'

const props = defineProps({
  pageKey: propTypes.string.def(),
  config: propTypes.object.def(),
  isCustom: propTypes.bool.def(false),
  dataId: propTypes.object.def(),
  modelValue: propTypes.string.def(),
})
const emits = defineEmits(['getDetail', 'init', 'before-set-columns', 'update:modelValue'])

const _config = ref<ConfigBO>(merge({}, Config, props.config))
const formRef = ref()
const loading = ref(false) // 列表的加载中
const areaList = ref([]) // 地区列表

/**************************
 *  表单属性项目处理逻辑
 * ******************************/
const getColumns = async () => {
  let detailInfo = await CustomFormApi.getByKey(props.pageKey)
  if (detailInfo?.config) {
    _config.value = merge({}, _config.value, JSON.parse(detailInfo.config))
    emits('before-set-columns', _config.value?.form?.columns)
  }
  await setComposeColumns()
}

/**
 * 完善填充字段属性
 */
const setComposeColumns = async () => {
  // 查询地区列表
  if (_config.value.form.columns.find((t: any) => t.type === ColumnTypeVo.city)) {
    areaList.value = await AreaApi.getAreaTree()
  }
  _config.value.form.columns?.forEach((t: any) => {
    setComposeColumn(t)
  })
}

const formData = ref<any>({})

/**
 * 查询详情
 */
const getDetail = async () => {
  loading.value = true
  return new Promise((resolve, reject) => {
    emits(
      'getDetail',
      props.dataId,
      (detail: any) => {
        formData.value = detail
        loading.value = false
        emits("update:modelValue", detail)
        resolve()
      },
      () => {
        loading.value = false
        reject()
      }
    )
  })
}

const columnLoading = ref(true)
onMounted(async () => {
  columnLoading.value = true
  if (props.isCustom) {
    await getColumns()
  } else {
    await setComposeColumns()
  }
  columnLoading.value = false
  if (props.dataId) {
    await getDetail()
  }
  emits('init', formData.value, _config.value)
})

defineExpose({
  formData,
  reload: getDetail
})
</script>

<template>
  <el-skeleton v-if="columnLoading" :rows="8" animated />
  <el-form
    v-else
    ref="formRef"
    class="z-custom-detail-form"
    v-loading="loading"
    :model="formData"
    :label-width="_config.form?.labelWidth"
    :label-position="_config.form.labelPosition"
  >
    <el-row :gutter="_config.form?.gutter">
      <template v-for="(item, index) in _config.form?.columns" :key="index">
        <el-col :span="item.span || 24" v-if="item.type === ColumnTypeVo.divider">
          <div class="form-split line">{{ item.label }}</div>
        </el-col>
        <el-col :span="item.span || 12" v-else>
          <el-form-item :label="item.label" :prop="item.prop">
            <slot :name="`form-prop-${item.prop}`" :form="formData">
              <template v-if="item.type === ColumnTypeVo.money">{{
                formatMoney(formData[item.prop] || 0)
              }}</template>
              <template v-else-if="item.type === ColumnTypeVo.select">
                <dict-tag
                  v-if="item.dataType === DataTypeEnum.dict"
                  :type="item.dictKey"
                  :value="formData[item.prop]"
                />
              </template>
              <template v-else>
                {{ formData[item.prop] || '-' }}
              </template>
            </slot>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>
<style lang="scss">
.z-custom-detail-form{
  .el-form-item{
    margin-bottom: 5px !important;
    .el-form-item__label{
      font-size: 13px;
      color: var(--el-text-color-regular);
    }
    .el-form-item__content{
      word-break: break-all; /* 允许任意字符处断行（适合中文） */
    }
  }
}
</style>
