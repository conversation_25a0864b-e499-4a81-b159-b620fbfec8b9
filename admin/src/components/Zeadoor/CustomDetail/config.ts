import {ColumnTypeVo} from '@/components/Zeadoor/interface'
import {getObjectDictOptions} from '@/utils/dict'
import {merge} from "lodash";
import {DATE_FORMAT, DATE_TIME_FORMAT} from "@/utils/dateUtil";

export interface FormColumnBO {
    span?: number // 占据栅格数量
    label?: string //标题
    prop?: string // 属性
    props?: any // 属性
    type?: ColumnTypeVo // 类型
    component?: string // 组件类型
    dataType?: DataTypeEnum // 数据类型
    dictKey?: string // 字典KEY
    dictType?: DictTypeEnum // 字典值类型
    options?: OptionBO[],
    rules?: any[],   // 校验规则集合
    ruleType?: string,    // 规则类型集合
    required?: boolean,      // 是否必填
}
export interface FormBO {
    labelWidth?: number // 标题长度
    labelPosition?: any //标题位置
    gutter?: number // 间距
    columns?: FormColumnBO[]
}

export interface ConfigBO {
    form: FormBO
}

export interface OptionBO {
    label: string,
    value: string | number,
}

export enum DataTypeEnum {
    dict = 'dict', // 字典类型
    custom = 'custom' //自定义数据
}

export enum DictTypeEnum {
    string = 'string', // 字符串
    number = 'number', //数字
    bool = 'bool' //布尔
}

export enum ColumnTypeComponent {
    text = 'el-input',
    number = 'el-input-number',
    date = 'el-date-picker',
    select = 'el-select',
    city = 'el-cascader',
    radio = 'el-radio-group',
    checkbox = 'el-checkbox-group',
    // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
    datetime = 'el-date-picker',
    // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
    textarea = 'el-input'
}

export const Config: ConfigBO = {
    form: {
        labelWidth: 120,
        labelPosition: 'left',
        gutter: 40,
        columns: []
    }
}

export const setComposeColumn = (column: FormColumnBO) => {
    column.component = ColumnTypeComponent[column.type || ColumnTypeVo.text]
    column.options = getOptionsByColumn(column)
    column.props = merge({}, getDefaultPropsByColumn(column), column.props || {})
}

export const setRuleColumn = (column: FormColumnBO, formRules:any = {}) => {
    if(!formRules[column.prop] || !formRules[column.prop].length){
        formRules[column.prop] = []
    }
    formRules[column.prop] = formRules[column.prop].concat(column.rules && column.rules.length ? column.rules : [])
    if (column.required) {
        formRules[column.prop].push({required: true, message: column.props?.placeholder || "数据不能为空", trigger: ['blur']})
    }
    if (column.ruleType) {
        switch (column.ruleType) {
            case "mobile":
                formRules[column.prop].push({
                    pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                    message: "请输入正确的手机号",
                    trigger: ['blur']
                })
                break
            case "email":
                formRules[column.prop].push({
                    type: 'email',
                    message: "请输入正确的邮箱",
                    trigger: ['blur']
                })
                break
            case "idCard":
                formRules[column.prop].push({
                    pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
                    message: "请输入正确的身份证号",
                    trigger: ['blur']
                })
                break
        }
    }
}

export const getPlaceholderByColumn = (column: FormColumnBO) => {
    switch (column.type) {
        case ColumnTypeVo.dict:
        case ColumnTypeVo.date:
        case ColumnTypeVo.datetime:
        case ColumnTypeVo.select:
        case ColumnTypeVo.city:
            return '请选择' + column.label
        default:
            return '请输入' + column.label
    }
}

export const getMaxlengthByColumn = (column: FormColumnBO) => {
    switch (column.type) {
        case ColumnTypeVo.textarea:
            return 200
        default:
            return 50
    }
}

export const getDefaultPropsByColumn = (column: FormColumnBO) => {
    const props: any = {
        placeholder: getPlaceholderByColumn(column),
        maxlength: getMaxlengthByColumn(column),
        width: 150, //表格列宽
    }
    switch (column.type) {
        case ColumnTypeVo.text:
            break
        case ColumnTypeVo.date:
            props.valueFormat = DATE_FORMAT
            break
        case ColumnTypeVo.datetime:
            props.type = ColumnTypeVo.datetime
            props.valueFormat = DATE_TIME_FORMAT
            break
        case ColumnTypeVo.textarea:
            props['show-word-limit'] = true
            props.type = ColumnTypeVo.textarea
            column.span = 24
            break
    }
    return props
}

export const getOptionsByColumn = (column: FormColumnBO) => {
    switch (column.dataType) {
        case DataTypeEnum.dict:
            return getObjectDictOptions(column.dictKey, column.dictType)
        case DataTypeEnum.custom:
            return column.options || []
    }
}
