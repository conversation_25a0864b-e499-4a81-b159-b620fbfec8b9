import { getTenantId } from '@/utils/auth'
import { formatTime } from '@/utils'

/** 生成业务扩展路径 */
export const generateExtraPath = (path: ExtraPathEnum) => {
  return `${getTenantId()}/${path}/${formatTime(new Date(), 'yyyyMM')}/`
}

export enum ExtraPathEnum {
  // 默认的上传路径
  UPLOAD = 'upload',
  // HRO 上传路径
  HRO = 'hro',
  // 办公 上传路径
  TEAMIFY = 'teamify',
  // 办公-考勤 上传路径
  TEAMIFY_ATTENDANCE = 'teamify/attendance',
  // 办公-财务 上传路径
  TEAMIFY_FINANCE = 'teamify/finance',
    // 租户套餐图片
  TENANT_PACKAGE = 'tenant/package',
  // 租户套餐应用图片
  TENANT_PACKAGE_APP = 'tenant/package/app',
}

export interface UploadFileItem {
  id: string
  url: string
  name: string
}

export interface UploadImageItem {
  id: string
  url: string
  name: string
}
