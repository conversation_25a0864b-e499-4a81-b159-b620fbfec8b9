<template>
  <div class="drag-area-container" @drop.prevent="onDrop" @dragover.prevent @dragenter.prevent>
    <Icon icon="ep:upload-filled" :size="48" color="#a8abb2" />
    <div class="el-upload__text">将文件拖到此处，或<em @click="onUploadClick">点击上传</em></div>
    <input
      ref="uploadIpt"
      class="hidden"
      type="file"
      :multiple="props.multiple"
      :accept="props.accept"
      @change="onFileIptChange"
    />
    <el-dialog
      v-model="progressShow"
      width="240"
      align-center
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="flex justify-center flex-col items-center">
        <el-progress
          type="circle"
          :indeterminate="true"
          :duration="0.2"
          :percentage="progressOption.percentage"
          :status="progressOption.status"
        />
        <div v-if="progressOption.status == 'success'" class="mt-4 text-center">
          <p>上传并解析完成</p>
          <p>共 {{ uploadFileCount }}张照片</p>
        </div>
        <div v-else class="mt-4 text-center">
          <p>正在上传中</p>
          <p class="text-[var(--el-color-primary)] font-bold">请不要刷新页面！</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  upload2Oss,
  generateThinImage,
  generateImagePresignedUrl,
  createFileBath
} from './uploadUtils'
import * as FileApi from '@/api/share/file'

interface Props {
  // 文件类型
  accept?: string
  // 是否多选
  multiple?: boolean
  // 文件个数
  limit?: number
  // 文件大小 单位MB
  maxSize?: number
  // 是否生成缩略图
  generateThin?: boolean
  // 自定义上传方法
  customRequest?: (filelist: File[], thinFileList?: File[]) => void
  // 自定义上传前的检查方法
  beforeUpload?: (filelist: File[]) => boolean
}

const props = withDefaults(defineProps<Props>(), {
  accept: '',
  multiple: false,
  limit: 10,
  maxSize: 2,
  generateThin: false
})

// 消息弹窗
const message = useMessage()

const uploadIpt = ref()
// 点击上传按钮
const onUploadClick = () => {
  uploadIpt.value.click()
}

const emit = defineEmits(['change', 'finish', 'error'])

// 上传进度显隐
const progressShow = ref(false)
// 上传文件数
const uploadFileCount = ref(0)
// 当前已上传文件
const currentUploadSuccessFile = ref<FileApi.ResultListVO[]>([])
// 当前已上传失败文件
const currentUploadErrorFile = ref<FileApi.ResultListVO[]>([])

interface ProgressOption {
  status: 'success' | 'exception' | 'warning' | ''
  percentage: number
}

// 上传进度百分比
const progressOption = reactive<ProgressOption>({
  status: '',
  percentage: 0
})

let currentUploadNum = 0 // 当前上传文件数
/** 更新进度条 */
const updateProgress = () => {
  if (!uploadFileCount.value) return
  // 当前上传数
  currentUploadNum++
  // 计算上传总数
  const allNum = props.generateThin ? uploadFileCount.value * 2 : uploadFileCount.value
  const percentage = ((currentUploadNum / allNum) * 100).toFixed(2)
  progressOption.percentage = Number(percentage)
}

/** 初始化进度条 */
const resetProgress = () => {
  progressOption.status = ''
  progressOption.percentage = 0
  currentUploadNum = 0
}

/** 关闭弹窗并且更新状态 */
const colseProgress = (type: 'exception' | 'success') => {
  progressOption.status = type
  // 延迟关闭进度条弹窗
  setTimeout(() => {
    progressShow.value = false
  }, 200)
}

/** 重置 Upload 状态 */
const resetUpload = () => {
  uploadFileCount.value = 0
  currentUploadSuccessFile.value = []
  currentUploadErrorFile.value = []
  resetProgress()
}

/** 选择文件事件 */
const onFileIptChange = async (e: Event) => {
  // 重置 Upload 状态
  resetUpload()

  const target = e.target as HTMLInputElement

  if (target.files) {
    let fileList = Array.from(target.files)
    await onUploadCheck(fileList)
  }

  // 重置 input 的 value 值，否则上传相同图片时不触发 change 事件
  uploadIpt.value.value = ''
}

/** 拖拽文件事件 */
const onDrop = async (e: DragEvent) => {
  e.stopPropagation()

  // 重置 Upload 状态
  resetUpload()

  const dataTransfer = e.dataTransfer as DataTransfer

  if (dataTransfer?.files) {
    let fileList = Array.from(dataTransfer.files)
    await onUploadCheck(fileList)
  }

  // 重置 input 的 value 值，否则上传相同图片时不触发 change 事件
  uploadIpt.value.value = ''
}

/** 上传前的一些校验 */
const onUploadCheck = async (fileList: File[]) => {
  // 上传前的检查
  const passed = onBeforeUpload(fileList)
  if (!passed) return

  // 记录总的上传文件数
  uploadFileCount.value = fileList.length
  // 打开进度条弹窗
  progressShow.value = true

  // 生成缩略图
  // TODO: 待完善 原文件也需要压缩 具体需求参考原型图
  let thinFileList: File[] = []
  if (props.generateThin) {
    const { baseList, thinList } = await generateThinImage(fileList)
    fileList = baseList
    thinFileList = thinList
  }

  // 自定义请求
  if (props.customRequest) {
    props.customRequest(fileList, thinFileList)
    return
  }

  // 上传动作
  await onUploadAction(fileList, thinFileList)
}

/** 上传前的检查 */
const onBeforeUpload = (fileList: File[]) => {
  // 自定义的检查方法
  if (props.beforeUpload) {
    const checked = props.beforeUpload(fileList)
    if (!checked) return false
  }

  // 限制文件数量
  if (fileList.length > props.limit) {
    message.error(`最多只能上传 ${props.limit} 个文件`)
    return false
  }

  const accepts = props.accept.split(',')
  for (const file of fileList) {
    // 限制文件大小
    if (file.size > props.maxSize * 1024 * 1024) {
      message.error(`文件 ${file.name} 超过最大允许大小 ${props.maxSize} MB`)
      return false
    }
    // 限制文件类型
    if (!accepts.includes(file.type)) {
      message.error(`文件格式不正确, 请上传 ${accepts.join('、')} 格式!`)
      return false
    }
  }

  return true
}

/** 上传动作 */
const onUploadAction = async (fileList: File[], thinFileList: File[]) => {
  try {
    const presignedUrlOption = {
      fileList,
      thinFileList,
      generateThin: props.generateThin
    }
    /** 1. 批量生成文件签名 */
    const imageUrlConfig = await generateImagePresignedUrl(presignedUrlOption)

    /** 2. 并行上传到 OSS */
    const uploadPromises = fileList.map((file, i) => {
      const urlConfig = imageUrlConfig[i]
      const ossOption = {
        file,
        uploadUrl: urlConfig.uploadUrl,
        thinFile: thinFileList[i],
        thinUploadUrl: urlConfig.thinUploadUrl,
        generateThin: props.generateThin,
        config: urlConfig
      }
      return upload2Oss(ossOption, (file: File) => {
        // 同步的 callback
        updateProgress()
        emit('change', file)
      })
    })

    // 等待所有上传操作完成
    const results = await Promise.all(uploadPromises)
    // 处理上传结果
    results.forEach((result) => {
      const { status, file, config } = result
      if (status === 'success') {
        currentUploadSuccessFile.value.push({ file, config })
      } else if (status === 'error') {
        currentUploadErrorFile.value.push({ file, config })
      }
    })

    /** 3. 批量记录上传成功文件信息到后端 */
    if (!currentUploadSuccessFile.value.length) {
      // 没有成功的文件 直接抛完成
      emit('finish', { result: [], failList: currentUploadErrorFile.value })
      colseProgress('exception')
      return
    }
    try {
      // TODO：这里同步信息的接口属于小红书业务 后续需要看看怎么扩展
      const result = await createFileBath(currentUploadSuccessFile.value)
      emit('finish', { result, failList: currentUploadErrorFile.value })
      colseProgress('success')
    } catch (error) {
      // 接口异常时的处理
      emit('finish', { result: [], failList: currentUploadErrorFile.value })
      colseProgress('exception')
    }
  } catch (error) {
    emit('error', error)
    colseProgress('exception')
  }
}
</script>

<style lang="scss" scoped>
.drag-area-container {
  padding: 40px 10px;
  text-align: center;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  .el-upload__text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center;
  }
  .el-upload__text em {
    color: var(--el-color-primary);
    font-style: normal;
  }
  &:hover {
    border-color: var(--el-color-primary);
  }
}
</style>
