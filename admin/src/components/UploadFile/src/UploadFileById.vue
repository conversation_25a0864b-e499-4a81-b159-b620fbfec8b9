<template>
  <div class="upload-file">
    <el-upload
      ref="uploadRef"
      :auto-upload="autoUpload"
      v-model:file-list="fileList"
      :before-upload="beforeUpload"
      :disabled="disabled"
      :drag="drag"
      :http-request="httpRequestById"
      :limit="props.limit"
      :multiple="props.limit > 1"
      :on-error="excelUploadError"
      :on-exceed="handleExceed"
      :on-success="handleFileSuccess"
      :show-file-list="false"
      class="upload-file-uploader"
      name="file"
    >
      <el-button v-if="!disabled" type="primary">
        <Icon icon="ep:upload-filled" />
        选取文件
      </el-button>
      <template v-if="isShowTip && !disabled" #tip>
        <div style="font-size: 12px">
          <span>
            大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
          </span>
          <span class="ml1">
            格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b> 的文件
          </span>
          <slot name="tip"></slot>
        </div>
      </template>
    </el-upload>
    <div v-for="item in uploadList" :key="item.id" class="flex-between items-center">
      <div class="flex-1 mr4 max-w-300px truncate" :title="item.name">{{ item.name }}</div>
      <div class="ml-10px">
        <el-link :href="item.url" :underline="false" download target="_blank" type="primary">
          下载
        </el-link>
        <el-button class="ml2" link type="danger" @click="handleRemove(item)"> 删除</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import type { UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import { useUploadById } from './uploadUtils'
import { ExtraPathEnum } from './extraPathEnum'

defineOptions({ name: 'UploadFileById' })

const message = useMessage() // 消息弹窗
const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: {
    type: Array as PropType<UploadFileItem[]>,
    default: () => []
  },
  fileType: propTypes.array.def(['doc', 'xls', 'ppt', 'txt', 'pdf']), // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileSize: propTypes.number.def(5), // 大小限制(MB)
  limit: propTypes.number.def(5), // 数量限制
  autoUpload: propTypes.bool.def(true), // 自动上传
  drag: propTypes.bool.def(false), // 拖拽上传
  isShowTip: propTypes.bool.def(true), // 是否显示提示
  disabled: propTypes.bool.def(false), // 是否禁用上传组件 ==> 非必传（默认为 false）
  onBeforeUpload: propTypes.func.def(() => true), // 上传前回调
  path: propTypes.oneOf(Object.values(ExtraPathEnum)).def(ExtraPathEnum.UPLOAD)
})

const _path = computed(() => props.path)

const { httpRequestById } = useUploadById(_path)

interface UploadFileItem {
  id: string
  url: string
  name: string
}

// ========== 上传相关 ==========
const uploadRef = ref<UploadInstance>()
const uploadList = ref<UploadFileItem[]>([])
const fileList = ref<UploadUserFile[]>([])

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      uploadList.value = Array.isArray(val) ? val : []
    } else {
      uploadList.value = []
    }
  },
  {
    immediate: true,
    deep: true
  }
)

// 文件上传之前判断
const beforeUpload: UploadProps['beforeUpload'] = (file: UploadRawFile) => {
  if (uploadList.value.length >= props.limit) {
    message.error(`上传文件数量不能超过${props.limit}个!`)
    return false
  }
  let fileExtension = ''
  if (file.name.lastIndexOf('.') > -1) {
    fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
  }
  const isImg = props.fileType.some((type: string) => {
    if (file.type.indexOf(type) > -1) return true
    return !!(fileExtension && fileExtension.indexOf(type) > -1)
  })
  const isLimit = file.size < props.fileSize * 1024 * 1024
  if (!isImg) {
    message.error(`文件格式不正确, 请上传${props.fileType.join('/')}格式!`)
    return false
  }
  if (!isLimit) {
    message.error(`上传文件大小不能超过${props.fileSize}MB!`)
    return false
  }
  // 自定义 onBeforeUpload
  if (props.onBeforeUpload) {
    return props.onBeforeUpload?.(file)
  }
}

// 文件上传成功
const handleFileSuccess: UploadProps['onSuccess'] = (res: any): void => {
  uploadList.value.push({
    id: res.id,
    url: res.data.url,
    name: res.data.name
  })
  emit('update:modelValue', uploadList.value)
  message.success('上传成功')
}
// 文件数超出提示
const handleExceed: UploadProps['onExceed'] = (): void => {
  message.error(`上传文件数量不能超过${props.limit}个!`)
}
// 上传错误提示
const excelUploadError: UploadProps['onError'] = (): void => {
  message.error('导入数据失败，请您重新上传！')
}
// 删除上传文件
const handleRemove = (item: UploadFileItem) => {
  uploadList.value = uploadList.value.filter((v) => v.id !== item.id)
  fileList.value = fileList.value.filter((v) => v.name !== item.name)
  emit('update:modelValue', uploadList.value)
}
</script>
<style lang="scss" scoped>
.upload-file-uploader {
  margin-bottom: 5px;
}

:deep(.upload-file-list .el-upload-list__item) {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
}

:deep(.el-upload-list__item-file-name) {
  max-width: 250px;
}

:deep(.upload-file-list .ele-upload-list__item-content) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

:deep(.ele-upload-list__item-content-action .el-link) {
  margin-right: 10px;
}
</style>
