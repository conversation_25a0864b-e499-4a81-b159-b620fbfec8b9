import { useImageCompress } from '@/hooks/web/useImageCompress'
import { generateFileName } from '@/components/UploadFile/src/useUpload'
import { UploadRawFile } from 'element-plus/es/components/upload/src/upload'
import type { UploadRequestOptions } from 'element-plus'
import * as FileApi from '@/api/share/file'
import * as infraFileApi from '@/api/infra/file'
import axios from 'axios'
import { generateExtraPath, ExtraPathEnum } from './extraPathEnum'

interface Upload2OssOption {
  file: File
  uploadUrl: string
  thinFile: File
  thinUploadUrl: string
  generateThin: boolean
  config: FileApi.ImagePresignedUrlConfigVO
}

/**
 * 上传到oss 异步
 * callback 用于实时的同步信息
 */
export const upload2Oss = async (option: Upload2OssOption, callback?: Function) => {
  const arg = {
    status: 'pending',
    file: option.file,
    config: option.config
  }

  try {
    // 上传主文件
    await FileApi.uploadFile(option.file, option.uploadUrl)
    callback && callback(option.file) // 更新进度

    // 上传缩略图文件（如果需要）
    if (option.generateThin) {
      await FileApi.uploadFile(option.thinFile, option.thinUploadUrl)
      callback && callback(option.thinFile) // 更新进度
    }

    arg.status = 'success'
  } catch (error) {
    arg.status = 'error'
  }

  return arg
}

/** 生成缩略图 */
export const generateThinImage = async (fileList: File[]) => {
  // 防止生成失败导致的数据不一致 这里创建一个新的 list
  const successList: File[] = []
  const successThinList: File[] = []

  // 使用 Promise.all 并行处理所有文件的压缩操作
  const results = await Promise.all(fileList.map((file) => useImageCompress(file, { width: 200 })))

  // 根据压缩结果筛选成功的文件和压缩后的文件
  results.forEach((v, i) => {
    if (!v.error) {
      // 这里利用 Promise.all 有序返回
      successList.push(fileList[i])
      successThinList.push(v.compressedFile)
    }
  })

  return {
    baseList: successList,
    thinList: successThinList
  }
}

interface PresignedUrlOption {
  fileList: File[]
  thinFileList: File[]
  generateThin: boolean
}
/** 批量生成文件签名 */
export const generateImagePresignedUrl = async (option: PresignedUrlOption) => {
  // 并行生成文件路径和缩略图路径
  const params = await Promise.all(
    option.fileList.map(async (file, index) => {
      const filePath = await generateFileName(file as UploadRawFile)
      let thinFilePath = ''

      // 当需要生成缩略图时
      if (option.generateThin) {
        thinFilePath = await generateFileName(option.thinFileList[index] as UploadRawFile)
      }

      return {
        path: filePath,
        thinPath: thinFilePath
      }
    })
  )
  const result = await FileApi.getImagePresignedUrl(params)
  return result
}

/** 批量记录上传成功文件信息到后端 */
export const createFileBath = async (
  list: { file: File; config: FileApi.ImagePresignedUrlConfigVO }[]
) => {
  const params: FileApi.CreateFileBathParam[] = []
  list.forEach(({ file, config }) => {
    params.push({
      configId: config.configId,
      originName: file.name,
      picPath: config.path,
      picUrl: config.url,
      thinPath: config.thinPath,
      thinUrl: config.thinUrl,
      size: file.size
    })
  })
  const result = await FileApi.createFileBath(params)
  return result
}

/**
 * 自定义的关联id的上传
 */
export const useUploadById = (path = ref(ExtraPathEnum.UPLOAD)) => {
  const httpRequestById = async (options: UploadRequestOptions) => {
    const fileName = await generateFileName(options.file)
    // 1.2 获取文件预签名地址
    // const _path = uploadPathStore.getUploadPath(unref(path) ?? '')
    const extraPath = generateExtraPath(unref(path))
    const _fileName = extraPath + fileName
    const presignedInfo = await infraFileApi.getFilePresignedUrl(_fileName)
    // 1.3 上传文件（不能使用 ElUpload 的 ajaxUpload 方法的原因：其使用的是 FormData 上传，Minio 不支持）
    return axios
      .put(presignedInfo.uploadUrl, options.file, {
        headers: {
          'Content-Type': options.file.type
        }
      })
      .then(async () => {
        // 1.4. 记录文件信息到后端（异步）
        const fileContext = {
          configId: presignedInfo.configId,
          url: presignedInfo.url,
          path: _fileName,
          name: options.file.name,
          type: options.file.type,
          size: options.file.size
        }
        const id = await infraFileApi.createFile(fileContext)
        // 通知成功，数据格式保持与后端上传的返回结果一致
        return { data: fileContext, id }
      })
  }

  return {
    httpRequestById
  }
}