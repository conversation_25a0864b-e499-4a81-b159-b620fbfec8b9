<template>
  <el-input-number
    v-model="innerValue"
    :disabled="disabled"
    :min="min"
    :max="max"
    :controls="controls"
    v-bind="$attrs"
    @change="handleChange"
    @keydown="handleKeydown"
  />
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 99999999
  },
  controls: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const innerValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleChange = (val) => {
  emit('change', val)
}

/**
 * input type="number" 无法过滤 e、E、+、- 等特殊字符
 *
 * 原因如下：
 * (1) e = 2.71828，本身指的就是指数，属于数字范围
 * (2) + 用来表示正数
 * (3) - 用来表示负数
 *
 * 这里监听 Keydown 手动过滤一下
 */
const backList = ['e', 'E', '+']
const handleKeydown = (e) => {
  if (backList.includes(e.key)) {
    e.preventDefault()
  }
}
</script>

<style lang="scss" scoped>
.el-input-number :deep() .el-input__inner {
  text-align: left;
}

.el-input-number.is-without-controls :deep().el-input__wrapper {
  padding-left: 12px;
  padding-right: 12px;
}

.el-input-number--small.is-without-controls :deep() .el-input__wrapper {
  padding-left: 8px;
  padding-right: 8px;
}
</style>
