import { Layout } from '@/utils/routerHelper'

const { t } = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const oaRouter: AppRouteRecordRaw[] = [
   {
    path: '/oa', // OA
    component: Layout,
    name: 'OA',
    meta: {
      hidden: true
    },
     children: [
       {
         path: 'performance/config/template/add',
         component: () => import('@/views/oa/personnel/performance/template/item/form.vue'),
         name: 'PerformanceTemplateAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加考核模板',
           activeMenu: '/oa/performance/config/template'
         }
       },
       {
         path: 'performance/config/template/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/template/item/form.vue'),
         name: 'PerformanceTemplateEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑考核模板',
           activeMenu: '/oa/performance/config/template'
         }
       },
       {
         path: 'salary/month/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/salary/month/detail.vue'),
         name: 'SalaryMonthDetail',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '月工资明细',
           activeMenu: '/oa/salary/manage/month'
         }
       },
       {
         path: 'salary/grant/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/salary/grant/detail.vue'),
         name: 'SalaryGrantDetail',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '发放明细',
           activeMenu: '/oa/salary/manage/grant'
         }
       },
       {
         path: 'salary/manage/template/add',
         component: () => import('@/views/oa/personnel/salary/template/form.vue'),
         name: 'SalaryTemplateAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加薪资模板',
           activeMenu: '/oa/salary/manage/template'
         }
       },
       {
         path: 'salary/manage/template/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/salary/template/form.vue'),
         name: 'SalaryTemplateEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑薪资模板',
           activeMenu: '/oa/salary/manage/template'
         }
       },
       {
         path: 'attendance/holiday/balance/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/attendance/holiday/balance/record.vue'),
         name: 'AttendanceHolidayBalanceRecord',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '假期余额明细',
           activeMenu: '/oa/attendance/holiday/balance'
         }
       },
       {
         path: 'performance/plan/merits/add',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformanceMeritsPlanAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加绩效考核计划',
           activeMenu: '/oa/performance/plan/merits'
         }
       },
       {
         path: 'performance/plan/trial/add',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformanceTrialPlanAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加试用期考核计划',
           activeMenu: '/oa/performance/plan/trial'
         }
       },
       {
         path: 'performance/plan/promotion/add',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformancePromotionPlanAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加晋升考核计划',
           activeMenu: '/oa/performance/plan/promotion'
         }
       },
       {
         path: 'performance/plan/merits/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformanceMeritsPlanEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑绩效考核计划',
           activeMenu: '/oa/performance/plan/merits'
         }
       },
       {
         path: 'performance/plan/trial/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformanceTrialPlanEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑试用期考核计划',
           activeMenu: '/oa/performance/plan/trial'
         }
       },
       {
         path: 'performance/plan/promotion/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformancePromotionPlanEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑晋升考核计划',
           activeMenu: '/oa/performance/plan/promotion'
         }
       },
       {
         path: 'performance/plan/merits/show/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/form.vue'),
         name: 'PerformanceMeritsPlanShow',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '查看绩效考核计划',
           activeMenu: '/oa/performance/plan/merits'
         }
       },
       {
         path: 'performance/plan/merits/confirm/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/confirm.vue'),
         name: 'PerformanceMeritsPlanConfirm',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '启动绩效考核计划',
           activeMenu: '/oa/performance/plan/merits'
         }
       },
       {
         path: 'performance/plan/trial/confirm/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/confirm.vue'),
         name: 'PerformanceTrialPlanConfirm',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '启动试用期考核计划',
           activeMenu: '/oa/performance/plan/trial'
         }
       },
       {
         path: 'performance/plan/promotion/confirm/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/confirm.vue'),
         name: 'PerformancePromotionPlanConfirm',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '启动晋升考核计划',
           activeMenu: '/oa/performance/plan/promotion'
         }
       },
       {
         path: 'performance/plan/merits/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/detail.vue'),
         name: 'PerformanceMeritsPlanDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '绩效考核计划详情',
           activeMenu: '/oa/performance/plan/merits'
         }
       },
       {
         path: 'performance/plan/trial/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/detail.vue'),
         name: 'PerformanceTrialPlanDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '试用期考核计划详情',
           activeMenu: '/oa/performance/plan/trial'
         }
       },
       {
         path: 'performance/plan/promotion/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/plan/detail.vue'),
         name: 'PerformancePromotionPlanDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '晋升考核计划详情',
           activeMenu: '/oa/performance/plan/promotion'
         }
       },
       {
         path: 'performance/plan/user/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/performance/dossier/detail.vue'),
         name: 'PerformancePlanUserDossier',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '员工绩效详情',
           activeMenu: '/oa/performance/result/plan/dossier'
         }
       },
       {
         path: 'performance/plan/user/record/detail/:planId(\\d+)/:userId(\\d+)',
         component: () => import('@/views/oa/personnel/performance/result/detail.vue'),
         name: 'PerformancePlanUserResultDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '员工绩效评价详情',
           activeMenu: '/oa/performance/result/plan/record'
         }
       },
       {
         path: 'user/roster/info/:id(\\d+)',
         component: () => import('@/views/oa/personnel/user/detail/index.vue'),
         name: 'PersonnelUserDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '员工详情',
         }
       },
       {
         path: 'contract/template/add',
         component: () => import('@/views/oa/manage/contract/template/add.vue'),
         name: 'ContractTemplateAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加合同模板',
           activeMenu: '/oa/contract/template'
         }
       },
       {
         path: 'contract/template/edit/:id(\\d+)',
         component: () => import('@/views/oa/manage/contract/template/edit.vue'),
         name: 'ContractTemplateEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑合同模板',
           activeMenu: '/oa/contract/template'
         }
       },
       {
         path: 'contract/template/preview/:id(\\d+)',
         component: () => import('@/views/oa/manage/contract/template/preview.vue'),
         name: 'ContractTemplatePreview',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '合同模板预览',
           activeMenu: '/oa/contract/template'
         }
       },
       {
         path: 'contract/create/collaborative',
         component: () => import('@/views/oa/manage/contract/item/create/collaborative.vue'),
         name: 'ContractCreateCollaborative',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '起草合同',
           activeMenu: '/oa/contract/item'
         }
       },
       {
         path: 'contract/create/prepareFlow',
         component: () => import('@/views/oa/manage/contract/item/create/prepareFlow.vue'),
         name: 'ContractCreatePrepareFlow',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '发起合同',
           activeMenu: '/oa/contract/item'
         }
       },
       {
         path: 'contract/preview/:id(\\d+)',
         component: () => import('@/views/oa/manage/contract/item/preview.vue'),
         name: 'ContractPreview',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '合同预览',
           activeMenu: '/oa/contract/list'
         }
       },
       {
         path: 'contract/detail/:id(\\d+)',
         component: () => import('@/views/oa/manage/contract/item/detail.vue'),
         name: 'ContractDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '合同详情',
           activeMenu: '/oa/contract/list'
         }
       },
       {
         path: 'recruit/candidate/info/:id(\\d+)',
         component: () => import('@/views/oa/personnel/recruit/candidate/detail/index.vue'),
         name: 'PersonnelRecruitCandidateDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '候选人详情',
           activeMenu: '/oa/recruit/candidate'
         }
       },
       {
         path: 'recruit/talent/info/:id(\\d+)/:talentId(\\d+)',
         component: () => import('@/views/oa/personnel/recruit/talent/detail.vue'),
         name: 'PersonnelRecruitTalentDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '人才库详情',
           activeMenu: '/oa/recruit/talent'
         }
       },
       {
         path: 'socialHousing/scheme/add',
         component: () => import('@/views/oa/personnel/socialHousing/scheme/form.vue'),
         name: 'SocialHousingSchemeAdd',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '添加参保方案',
           activeMenu: '/oa/socialHousing/scheme'
         }
       },
       {
         path: 'socialHousing/scheme/edit/:id(\\d+)',
         component: () => import('@/views/oa/personnel/socialHousing/scheme/form.vue'),
         name: 'SocialHousingSchemeEdit',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '编辑参保方案',
           activeMenu: '/oa/socialHousing/scheme'
         }
       },
       {
         path: 'socialHousing/bill/user/:id(\\d+)',
         component: () => import('@/views/oa/personnel/socialHousing/bill/user.vue'),
         name: 'SocialHousingBillUser',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '账单详情',
           activeMenu: '/oa/socialHousing/bill'
         }
       },
       {
         path: 'socialHousing/bill/user/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/socialHousing/bill/detail.vue'),
         name: 'SocialHousingBillUserDetail',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '员工账单详情',
           activeMenu: '/oa/socialHousing/bill'
         }
       },
       {
         path: 'training/plan/:id(\\d+)',
         component: () => import('@/views/oa/manage/training/plan/item/detail.vue'),
         name: 'TrainingPlanDetail',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '培训计划详情',
           activeMenu: '/oa/training/plan'
         }
       },
       {
         path: 'calendar/main',
         component: () => import('@/views/oa/system/calendar/main.vue'),
         name: 'Calendar',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '日历',
           activeMenu: '/index'
         }
       },
       {
         path: 'asset/stocktaking/detail/:id(\\d+)',
         component: () => import('@/views/oa/manage/asset/stocktaking/detail/index.vue'),
         name: 'AssetStocktakingDetail',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '盘点计划详情',
           activeMenu: '/oa/asset/item/stocktaking'
         }
       },
       {
         path: 'socialHousing/config',
         component: () => import('@/views/oa/personnel/socialHousing/config/config.vue'),
         name: 'SocialHousingConfig',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '设置起始月份',
         }
       },
       {
         path: 'salary/init/config',
         component: () => import('@/views/oa/personnel/salary/system/init.vue'),
         name: 'SalaryInitConfig',
         meta: {
           noCache: false, // 需要缓存
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '设置起始月份',
         }
       },
       {
         path: 'salary/payslip/confirm/:id(\\d+)',
         component: () => import('@/views/oa/personnel/salary/payslip/grant/confirm.vue'),
         name: 'SalaryPayslipConfirm',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '工资条发放确认',
           activeMenu: '/oa/salary/payslip/grant'
         }
       },
       {
         path: 'salary/payslip/detail/:id(\\d+)',
         component: () => import('@/views/oa/personnel/salary/payslip/record/detail.vue'),
         name: 'SalaryPayslipDetail',
         meta: {
           noCache: true,
           hidden: true,
           canTo: true,
           icon: 'ep:edit',
           title: '工资条发放详情',
           activeMenu: '/oa/salary/payslip/record'
         }
       },
     ]
  },
]

export default oaRouter
