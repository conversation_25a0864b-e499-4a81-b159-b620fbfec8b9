<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="keywords" content="vtuzx 办公平台" />
  <meta name="description" content="vtuzx 办公平台" />
  <script
    src="https://webapi.amap.com/maps?v=1.4.15&key=7dae625717f9cd8886e034c4eada7915&plugin=AMap.MapType,AMap.Geocoder,AMap.Autocomplete,AMap.PlaceSearch,AMap.Scale,AMap.ToolBar,ElasticMarker"></script>
  <title>%VITE_APP_TITLE%</title>
</head>

<body>
  <div id="app">
      <style>
        body {
          background-color: var(--app-content-bg-color);
        }

        .app-loading-wrap {
          position: absolute;
          top: 50%;
          left: 50%;
          display: flex;
          -webkit-transform: translate3d(-50%, -50%, 0);
          transform: translate3d(-50%, -50%, 0);
          justify-content: center;
          align-items: center;
          flex-direction: column;
        }

        .app-loading-wrap .app-logo {
          width: 272px;
          object-fit: cover;
        }

        .app-loading-wrap .loading-gif {
          width: 80px;
          margin: 0 auto;
        }

        .app-loading-wrap .app-desc {
          font-size: 14px;
          color: #8c8c8c;
          text-align: center;
          letter-spacing: 6px;
        }
      </style>
      <div class="app-loading-wrap">
        <img src="/loading.gif" class="loading-gif" alt="" />
        <img src="/loading-logo.png" class="app-logo" alt="Logo" />
        <span class="app-desc">让工作更简单，让管理更高效</span>
      </div>
    </div>
  <script type="text/javascript">
    // 腾讯地图api 和定位sdk
    var script = document.createElement("script");
    script.type = "text/javascript";
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=%VITE_APP_TMAP_KEY%&libraries=service`;
    document.body.appendChild(script);
  </script>
  <script type="text/javascript"
    src="https://mapapi.qq.com/web/mapComponents/geoLocation/v/geolocation.min.js"></script>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>